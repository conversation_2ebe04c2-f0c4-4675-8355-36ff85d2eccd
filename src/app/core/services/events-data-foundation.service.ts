import { HttpClient } from '@angular/common/http';
import {
  AddProductEventDataFoundation,
  LoginEventDataFoundation,
  NewOrderEventDataFoundation,
  ShareByQrEventDataFoundation,
  ShareOrderEventDataFoundation,
  StoreFrontEventDataFoundation,
} from '../domain/events.data-foundation';
import { Observable } from 'rxjs';
import { EventService } from '../domain/repositories/events-service';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class EventDataFoundationService extends EventService {
  constructor(private readonly http: HttpClient) {
    super();
  }

  login(data: LoginEventDataFoundation) {
    return this.http
      .post<void>(
        `${this.baseUrl}/events/login`,
        this.putOriginInData({
          origin: this.origin,
          email: data.email,
          success: data.success,
        })
      )
      .pipe(this.observableProcess());
  }

  storeFront(data: StoreFrontEventDataFoundation) {
    return this.http
      .post<void>(
        `${this.baseUrl}/events/storefront`,
        this.putOriginInData({
          storefront_id: data.storeFrontId,
          auto_select: data.autoSelect,
        })
      )
      .pipe(this.observableProcess());
  }

  newOrder(data: NewOrderEventDataFoundation): Observable<void> {
    return this.http
      .post<void>(
        `${this.baseUrl}/events/new-order`,
        this.putOriginInData({
          storefront_id: data.storeFrontId,
        })
      )
      .pipe(this.observableProcess());
  }

  addProduct(data: AddProductEventDataFoundation): Observable<void> {
    return this.http
      .post<void>(
        `${this.baseUrl}/events/add-product`,
        this.putOriginInData(data)
      )
      .pipe(this.observableProcess());
  }

  shareByQrCode(data: ShareByQrEventDataFoundation): Observable<void> {
    return this.http
      .post<void>(`${this.baseUrl}/events/qr-order`, this.putOriginInData(data))
      .pipe(this.observableProcess());
  }

  shareOrder(data: ShareOrderEventDataFoundation): Observable<void> {
    return this.http
      .post<void>(
        `${this.baseUrl}/events/share-order`,
        this.putOriginInData(data)
      )
      .pipe(this.observableProcess());
  }

  cancelOrder(data: ShareOrderEventDataFoundation): Observable<void> {
    return this.http
      .post<void>(
        `${this.baseUrl}/events/cancel-order`,
        this.putOriginInData(data)
      )
      .pipe(this.observableProcess());
  }
}
