import { of } from 'rxjs';
import { merchantConfigDummy } from './merchant-config.dummy';
export const storeServiceMock = jasmine.createSpyObj(
  'StoreService',
  [
    'setAllowedMerchantsForMigration',
    'clearStore',
    'setStoreFromSessionStorage',
    'setSelectedBranch',
    'getMerchantId$',
    'setMerchantConfig',
  ],
  {
    allowMerchantsForMigration: of(null),
    token$: of('token'),
    merchantConfig$: of(merchantConfigDummy),
    selectedBranch$: of(merchantConfigDummy.branches[0]),
    token: 'token',
    merchantConfig: merchantConfigDummy,
    isLoggedIn$: of(false),
    userRole$: of('ROLE_MERCHANT'),
    merchantName$: of('Merchant Name'),
    integrationType$: of('POSUI'),
    selectedBranchName$: of('Branch Name'),
    branches$: of(merchantConfigDummy.branches),
    hasBranches$: of(true),
    minimumOrderAmount$: of(merchantConfigDummy.minimumOrderAmount),
    merchantLogoUrl$: of(merchantConfigDummy.logoUrl),
    selectedBranchFeatureFlags$: of(null),
    hasBranchCreationEnabled$: of(true),
  }
);
