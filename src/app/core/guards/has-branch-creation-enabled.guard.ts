import { Injectable } from '@angular/core';
import { CanActivate, Router, UrlTree } from '@angular/router';
import { LoaderService, NotifierService } from '@aplazo/merchant/shared';
import { Observable, catchError, combineLatest, map, of, take } from 'rxjs';
import { StoreService } from '../application/services/store.service';
import { RuntimePosOfflineError } from '../domain/runtime-error';

@Injectable({
  providedIn: 'root',
})
export class HasBranchCreationEnabledGuard implements CanActivate {
  constructor(
    private storeService: StoreService,
    private loader: LoaderService,
    private router: Router,
    private notificationService: NotifierService
  ) {}

  canActivate(): Observable<boolean | UrlTree> {
    return combineLatest([
      this.storeService.hasBranchCreationEnabled$.pipe(take(1)),
      this.storeService.hasBranches$.pipe(take(1)),
    ]).pipe(
      take(1),
      map(([isBranchCreationEnabled, hasBranches]) => {
        if (!isBranchCreationEnabled && !hasBranches) {
          throw new RuntimePosOfflineError(
            'No hay sucursales creadas',
            'CreateBranches::emptyBranches',
            'create-branches-error'
          );
        }

        if (!isBranchCreationEnabled && hasBranches) {
          throw new RuntimePosOfflineError(
            'Creación de sucursales deshabilitada',
            'CreateBranches::disabledBranches',
            'create-branches-error'
          );
        }

        return true;
      }),

      catchError((error: unknown) => {
        this.loader.forceHide();

        if (error instanceof RuntimePosOfflineError) {
          console.warn(error.code);

          this.notificationService.warning({
            title: 'Contacte al equipo de Soporte',
            message: error.message,
          });
        } else {
          console.error(error);
        }

        this.storeService.clearStore();
        return of(this.router.parseUrl('/'));
      })
    );
  }
}
