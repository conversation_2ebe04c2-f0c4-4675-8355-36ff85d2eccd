import { Injectable } from '@angular/core';
import {
  BrowserLocalStorageService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  map,
  MonoTypeOperatorFunction,
  Observable,
  of,
  OperatorFunction,
  pipe,
  switchMap,
  take,
} from 'rxjs';
import {
  CreateNotificationDto,
  INotificationResponseDto,
  NotificationStatus,
  NotificationType,
  NotificationUI,
} from '../../core/domain/notification';
import { INotificationsWithNewOnesUIDto } from '../domain/notification';
import { NotificationsRepository } from '../domain/repositories/notifications.repository';
import { DateUtilsService } from '../services/date-utils.service';
import { StoreService } from './services/store.service';

@Injectable({ providedIn: 'root' })
export class GetNotificationsByTypeUsecase {
  readonly #activeStatus: NotificationStatus = 'A';
  readonly #localStorageNotificationsKey = 'AplazoReadedNotifications';

  constructor(
    private readonly promosService: NotificationsRepository,
    private readonly storeService: StoreService,
    private readonly dateUtils: DateUtilsService,
    private readonly usecaseError: UseCaseErrorHandler,
    private readonly browserLocalStorage: BrowserLocalStorageService
  ) {}

  execute(
    type: NotificationType
  ): Observable<INotificationsWithNewOnesUIDto | null> {
    const rawBrowserStoredNotifications = this.browserLocalStorage.getItem(
      this.#localStorageNotificationsKey
    );

    let storedNotifications: { [key: number]: boolean } = {};

    try {
      if (rawBrowserStoredNotifications) {
        storedNotifications = JSON.parse(rawBrowserStoredNotifications);
      }
    } catch (error) {
      console.error('Error al parsear las notificaciones guardadas', error);
    }

    return this.storeService.selectedBranch$.pipe(
      take(1),

      switchMap(branchId => {
        if (branchId === null) {
          throw new RuntimeMerchantError(
            'No se pudo obtener el id del comercio',
            'GetNotificationsByTypeUsecase::execute::branchId'
          );
        }
        return this.promosService.getNotificationsByTypeAndBranchId(
          type,
          branchId.id
        );
      }),

      this.#hasNewOnes(storedNotifications),

      this.#createNotificationUI(),

      this.#filterByStatus(this.#activeStatus),

      this.#parseNotificationUI(type),

      catchError(error => this.usecaseError.handle(error, of(null))),

      take(1)
    );
  }

  #hasNewOnes(storedNotifications: {
    [key: number]: boolean;
  }): OperatorFunction<
    INotificationResponseDto[],
    Array<CreateNotificationDto & { isNewOne: boolean }>
  > {
    return pipe(
      map(response => {
        return response.map(item => ({
          ...item,
          lastUpdate: this.dateUtils.parseFromISO(item.lastUpdate),
          isNewOne: !storedNotifications[item.id],
        }));
      })
    );
  }

  #createNotificationUI(): OperatorFunction<
    Array<CreateNotificationDto & { isNewOne: boolean }>,
    NotificationUI[]
  > {
    return pipe(
      map(response =>
        response
          .map(item => NotificationUI.create(item))
          .sort((a, b) => b.lastUpdateTimestamp - a.lastUpdateTimestamp)
      )
    );
  }

  #filterByStatus(
    status: NotificationStatus
  ): MonoTypeOperatorFunction<NotificationUI[]> {
    return pipe(
      map(parsedResponse =>
        parsedResponse.filter(
          promo => promo.status.toLowerCase() === status.toLowerCase()
        )
      )
    );
  }

  #parseNotificationUI(type: NotificationType): OperatorFunction<
    NotificationUI[],
    {
      type: NotificationType;
      newOnesCounter: number;
      hasNewOnes: boolean;
      notifications: Map<string, NotificationUI[]>;
      newOnesIds: number[];
    }
  > {
    return pipe(
      map(promos => {
        const ordered = new Map<string, NotificationUI[]>();
        const newOnesIds: number[] = [];

        let newOnes = 0;

        promos.forEach(promo => {
          if (promo.isNewOne) {
            newOnesIds.push(promo.id);
            newOnes++;
          }

          const key = this.dateUtils
            .getMonthYear(promo.lastUpdate)
            .toUpperCase();

          const collection = ordered.get(key);

          if (!collection) {
            ordered.set(key, [promo]);
          } else {
            collection.push(promo);
          }

          collection?.sort(
            (a, b) => b.lastUpdateTimestamp - a.lastUpdateTimestamp
          );
        });

        return {
          type,
          newOnesCounter: newOnes,
          hasNewOnes: newOnes > 0,
          notifications: ordered,
          newOnesIds,
        };
      })
    );
  }
}
