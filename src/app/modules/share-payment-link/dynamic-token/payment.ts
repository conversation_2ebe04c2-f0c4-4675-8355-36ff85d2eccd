export interface PaymentsResponse {
  id: number;
  merchant_id: number;
  credit_available: number;
  creation_date: Date;
  status: string;
  purchase_amount: number;
  source_type: string;
  card: Card;
  schemes: Scheme[];
  next_quincena_enabled: boolean;
  aplazo_points: null;
  promo_code_enabled: boolean;
}

export interface PaymentsResponseUIDto {
  id: number;
  merchant_id: number;
  credit_available: number;
  creation_date: Date;
  status: string;
  purchase_amount: number;
  source_type: string;
  card: Card;
  schemes: Scheme;
  next_quincena_enabled: boolean;
  aplazo_points: null;
  promo_code_enabled: boolean;
}

export interface Card {
  card_number: string;
  brand: string;
  cvv_update: boolean;
  bbva: boolean;
}

export interface Scheme {
  id: number;
  interest_amount: number;
  fee_amount: number;
  commission: number;
  total: number;
  total_discount_amount: number;
  first_installment_extra_amount: null;
  total_first_installment: number;
  installments: number;
  customer_discounts_applied: any[];
  calendar: string[]; // date string array
  ghost_commission_amount: number;
  aplazo_points_amounts: null;
}
