import {
  HttpClient,
  provideHttpClient,
  withInterceptors,
} from '@angular/common/http';
import {
  HttpClientTestingModule,
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { authInterceptor } from 'src/app/core/interceptors/auth.interceptor';
import { SecuredV1LogoutUsecase } from 'src/app/modules/secured-v1/secured-v1-logout.usecase';

const setup = (mockLogout = { execute: jasmine.createSpy('execute') }) => {
  TestBed.configureTestingModule({
    imports: [HttpClientTestingModule],
    providers: [
      provideHttpClient(withInterceptors([authInterceptor])),
      provideHttpClientTesting(),
      {
        provide: SecuredV1LogoutUsecase,
        useValue: mockLogout,
      },
    ],
  });
  const httpController = TestBed.inject(HttpTestingController);
  const http = TestBed.inject(HttpClient);
  const logoutUsecase = TestBed.inject(SecuredV1LogoutUsecase);

  return { httpController, http, logoutUsecase };
};

describe('authInterceptor', () => {
  it('should call logout when receiving 401 error', () => {
    const { httpController, http, logoutUsecase } = setup();

    http.get('/test').subscribe({
      error: error => {
        expect(error.status).toBe(401);
        expect(logoutUsecase.execute).toHaveBeenCalled();
      },
    });

    const req = httpController.expectOne('/test');
    req.flush('Unauthorized', { status: 401, statusText: 'Unauthorized' });

    httpController.verify();
  });

  it('should call logout when receiving 403 error', () => {
    const { httpController, http, logoutUsecase } = setup();

    http.get('/test').subscribe({
      error: error => {
        expect(error.status).toBe(403);
        expect(logoutUsecase.execute).toHaveBeenCalled();
      },
    });

    const req = httpController.expectOne('/test');
    req.flush('Forbidden', { status: 403, statusText: 'Forbidden' });

    httpController.verify();
  });

  it('should not call logout for other error codes', () => {
    const { httpController, http, logoutUsecase } = setup();

    http.get('/test').subscribe({
      error: error => {
        expect(error.status).toBe(500);
        expect(logoutUsecase.execute).not.toHaveBeenCalled();
      },
    });

    const req = httpController.expectOne('/test');
    req.flush('Server Error', { status: 500, statusText: 'Server Error' });

    httpController.verify();
  });

  it('should not intercept SVG resource requests', () => {
    const { httpController, http } = setup();

    http.get('/test.svg').subscribe();

    const req = httpController.expectOne('/test.svg');
    req.flush('SVG content');

    httpController.verify();
  });

  it('should not intercept JSON resource requests', () => {
    const { httpController, http } = setup();

    http.get('/test.json').subscribe();

    const req = httpController.expectOne('/test.json');
    req.flush('JSON content');

    httpController.verify();
  });

  it('should not intercept SSE requests', () => {
    const { httpController, http } = setup();

    http.get('/api/events/sse').subscribe();

    const req = httpController.expectOne('/api/events/sse');
    req.flush('SSE content');

    httpController.verify();
  });
});
