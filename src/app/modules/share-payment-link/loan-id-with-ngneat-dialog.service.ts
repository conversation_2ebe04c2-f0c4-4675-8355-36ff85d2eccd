import { Injectable, inject } from '@angular/core';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { DialogService } from '@ngneat/dialog';
import { lastValueFrom, take } from 'rxjs';
import { StoreService } from '../../core/application/services/store.service';
import { Order } from '../../core/domain/order.interface';
import { ShowOnlyLoanIdDialogUsecase } from './application/show-only-loan-id-dialog.usecase';
import { ShareOnlyLoanIdComponent } from './share-only-loan-id.component';

@Injectable({
  providedIn: 'root',
})
export class LoanIdWithNgneatDialogService
  implements ShowOnlyLoanIdDialogUsecase
{
  readonly #storeService = inject(StoreService);
  readonly #dialog = inject(DialogService);

  async execute(loan: Order): Promise<void> {
    const branch = await lastValueFrom(
      this.#storeService.selectedBranch$.pipe(take(1))
    );

    if (!branch) {
      throw new RuntimeMerchantError(
        'Diálogo de métodos de pago, no hay sucursal seleccionada. Intente cerrar sesión y volver a iniciarla',
        'QrPaymentLink::emptyBranch',
        'qr-payment-link-dialog-error'
      );
    }

    this.#dialog.open(ShareOnlyLoanIdComponent, {
      data: {
        loan,
      },
      maxWidth: '448px',
      enableClose: false,
    });
  }
}
