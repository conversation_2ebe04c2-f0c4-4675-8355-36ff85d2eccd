<ng-container *ngIf="vm$ | async as vm">
  <aplz-ui-card *ngIf="vm.text" appearance="flat">
    <table aplzSimpleTable>
      <tr aplzSimpleTableHeaderRow>
        <th
          aplzSimpleTableHeaderCell
          class="text-lg text-dark-tertiary font-medium pl-6">
          {{ vm.text.orderId }}
        </th>
        <th
          *ngIf="!vm.isLargeScreen?.matches"
          aplzSimpleTableHeaderCell
          class="text-right text-lg text-dark-tertiary font-medium">
          {{ vm.text.price.label }}
        </th>
        <th
          *ngIf="vm.isLargeScreen?.matches"
          aplzSimpleTableHeaderCell
          class="text-lg text-dark-tertiary font-medium pl-6">
          {{ vm.text.date }}
        </th>
        <th
          aplzSimpleTableHeaderCell
          class="text-lg text-dark-tertiary font-medium pl-6">
          {{ vm.text.status }}
        </th>
        <th
          *ngIf="vm.isLargeScreen?.matches"
          aplzSimpleTableHeaderCell
          class="text-right text-lg text-dark-tertiary font-medium">
          {{ vm.text.price.label }}
        </th>

        <th
          *ngIf="!vm.isLargeScreen?.matches"
          aplzSimpleTableHeaderCell
          class="text-lg text-dark-tertiary font-medium pl-6">
          {{ vm.text.date }}
        </th>
        <th
          aplzSimpleTableHeaderCell
          class="text-right text-lg text-dark-tertiary font-medium">
          <span *ngIf="vm.isLargeScreen?.matches">
            {{ vm.text.actions }}
          </span>
        </th>
      </tr>

      <tr
        aplzSimpleTableBodyRow
        *ngFor="let order of vm.orders; let first = first; let last = last"
        [ngClass]="{ last: last, first: first }"
        [striped]="true">
        <td aplzSimpleTableBodyCell class="font-medium">{{ order.loanId }}</td>
        <td
          *ngIf="!vm.isLargeScreen?.matches"
          aplzSimpleTableBodyCell
          class="text-right">
          {{ order.price | aplzDynamicPipe: 'currency' }}
        </td>
        <td *ngIf="vm.isLargeScreen?.matches" aplzSimpleTableBodyCell>
          {{ order.createdAt | aplzDynamicPipe: 'date' }}
        </td>
        <td aplzSimpleTableBodyCell>
          <button
            [ngClass]="{
              'cursor-default': !order.isWalmartOrder,
              relative: order.isWalmartOrder && order.status === 'Activo'
            }"
            [aplzLabelColor]="loanStatusColorDefinition"
            [loanStatus]="order.status.toLowerCase()"
            (click)="showWMrefundInfo(order)">
            {{ order.status | aplzStatusMap: loanStatusTextDefinition }}
            <aplz-ui-icon
              *ngIf="order.isWalmartOrder && order.status === 'Activo'"
              name="info"
              class="absolute -top-3 -right-3"
              size="sm"></aplz-ui-icon>
          </button>
        </td>
        <td
          *ngIf="vm.isLargeScreen?.matches"
          aplzSimpleTableBodyCell
          class="text-right">
          {{ order.price | aplzDynamicPipe: 'currency' }}
        </td>

        <td *ngIf="!vm.isLargeScreen?.matches" aplzSimpleTableBodyCell>
          {{ order.createdAt | aplzDynamicPipe: 'date' }}
        </td>
        <td aplzSimpleTableBodyCell class="px-0 py-6">
          <div class="flex flex-wrap gap-x-2 items-center justify-end w-full">
            <ng-container
              *ngIf="vm.isLargeScreen?.matches; else actionsMobileContainer">
              <ng-container [ngSwitch]="order.status.toLowerCase()">
                <ng-container *ngSwitchCase="'new'">
                  <button
                    [disabled]="order.isDisabled"
                    aplzButton
                    size="sm"
                    aplzAppearance="stroked"
                    id="refreshLoanStatus"
                    (click)="checkStatus(order)">
                    <aplz-ui-icon name="arrow-path" size="md"></aplz-ui-icon>
                  </button>
                </ng-container>
                <ng-container *ngSwitchCase="'created'">
                  <button
                    [disabled]="order.isDisabled"
                    aplzButton
                    size="sm"
                    aplzAppearance="stroked"
                    id="refreshLoanStatus"
                    (click)="checkStatus(order)">
                    <aplz-ui-icon name="arrow-path" size="md"></aplz-ui-icon>
                  </button>
                  <ng-container *ngIf="(isShareLinkEnabled$ | async) === true">
                    <button
                      aplzButton
                      size="sm"
                      aplzAppearance="stroked"
                      (click)="share(order)">
                      <aplz-ui-icon name="share" size="md"></aplz-ui-icon>
                    </button>
                  </ng-container>

                  <button
                    aplzButton
                    aplzAppearance="stroked"
                    aplzColor="danger"
                    size="md"
                    (click)="cancel(order)">
                    Cancelar
                  </button>
                </ng-container>

                <ng-container *ngSwitchCase="'refunded'"> </ng-container>
                <span *ngSwitchDefault></span>
              </ng-container>
            </ng-container>

            <ng-template #actionsMobileContainer>
              <ng-container *ngIf="order.status.toLowerCase() === 'created'">
                <button
                  aplzButton
                  [aplzDropdownTriggerFor]="actionsMobile"
                  aplzAppearance="basic"
                  size="xs">
                  <aplz-ui-icon name="chevron-down" size="sm"></aplz-ui-icon>
                </button>
                <aplz-ui-dropdown #actionsMobile>
                  <aplz-ui-dropdown-item>
                    <button
                      class="py-2 px-4 w-full"
                      [disabled]="order.isDisabled"
                      (click)="checkStatus(order)">
                      Verificar Estatus
                    </button>
                  </aplz-ui-dropdown-item>
                  <aplz-ui-dropdown-item>
                    <button class="py-2 px-4 w-full" (click)="share(order)">
                      Link de pago
                    </button>
                  </aplz-ui-dropdown-item>
                  <aplz-ui-dropdown-item>
                    <button class="py-2 px-4 w-full" (click)="cancel(order)">
                      Cancelar
                    </button>
                  </aplz-ui-dropdown-item>
                </aplz-ui-dropdown>
              </ng-container>
            </ng-template>
          </div>
        </td>
      </tr>
    </table>
  </aplz-ui-card>
</ng-container>
