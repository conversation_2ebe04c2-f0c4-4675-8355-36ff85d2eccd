import { Component } from '@angular/core';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';

import { Toast } from 'ngx-toastr';

@Component({
  standalone: true,
  selector: 'app-custom-toastr',
  template: `
    <article
      class="bg-white"
      [style.display]="state().value === 'inactive' ? 'none' : ''">
      <p class="flex flex-col w-full gap-2 mb-4">
        @if (title) {
          <span class="text-dark-primary font-medium" [attr.aria-label]="title">
            {{ title }}
          </span>
        }
        @if (message) {
          <span class="text-dark-secondary text-md" [attr.aria-label]="message">
            {{ message }}
          </span>
        }
      </p>

      <div class="flex gap-2 justify-end p-3">
        <button
          aplzButton
          aplzAppearance="stroked"
          aplzColor="info"
          size="sm"
          [rounded]="true"
          (click)="close()">
          Más tarde
        </button>
        <button
          aplzButton
          aplzAppearance="solid"
          aplzColor="dark"
          size="sm"
          [rounded]="true"
          (click)="action($event)">
          Actualizar
        </button>
      </div>
    </article>
  `,
  styles: `
    :host {
      @apply inline-flex bg-light-primary border border-aplazo-aplazo;
    }
  `,
  imports: [AplazoButtonComponent],
})
export class CustomToastrComponent extends Toast {
  action(_: Event) {
    this.toastPackage.triggerAction({ confirmation: true });
  }

  close() {
    this.toastPackage.triggerAction({ confirmation: false });
  }
}
