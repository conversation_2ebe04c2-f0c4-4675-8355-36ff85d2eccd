import {
  CreateNotificationDto,
  NotificationUI,
} from '../../../app/core/domain/notification';

describe('NotificationUI', () => {
  const createArgs: CreateNotificationDto = {
    id: 14,
    commType: 'INFO',
    title: 'Prue<PERSON>, notificacion INFO',
    description:
      'Si te preguntas si Kueski Pay es seguro o si Aplazo es seguro para tu Ecommerce en este video te diré cuál es la mejor opción BNPL para tu Negocio, además vamos a comparar como funcionan estas dos pasarelas de pago que permiten pagar a crédito sin tarjeta.',
    status: 'A',
    platform: 'P',
    merchants: '2239',
    lastUpdate: new Date('2024-03-04T23:14:33.288930Z'),
    applyAll: true,
    complement: {
      urlVideo: 'https://www.youtube.com/watch?v=1ulI-t9qT7U',
      urlThumbnail: 'string',
      tags: ['FAQ'],
    },
    isNewOne: false,
  };

  it('should throw an error when id is not a number', () => {
    const testArgs: any = {
      ...createArgs,
      id: 'a1234',
    };

    expect(() => NotificationUI.create(testArgs)).toThrowError();
  });

  it('should throw an error when id is negative', () => {
    const testArgs: any = {
      ...createArgs,
      id: -1,
    };

    expect(() => NotificationUI.create(testArgs)).toThrowError();
  });

  it('should throw an error when id is infinity', () => {
    const testArgs: any = {
      ...createArgs,
      id: Infinity,
    };

    expect(() => NotificationUI.create(testArgs)).toThrowError();
  });

  it('should throw an error when commType is not valid', () => {
    const testArgs: any = {
      ...createArgs,
      commType: 'INVALID',
    };

    expect(() => NotificationUI.create(testArgs)).toThrowError();
  });

  it('should throw an error when status is not valid', () => {
    const testArgs: any = {
      ...createArgs,
      status: 'INVALID',
    };

    expect(() => NotificationUI.create(testArgs)).toThrowError();
  });

  it('should throw an error when platform is not valid', () => {
    const testArgs: any = {
      ...createArgs,
      platform: 'INVALID',
    };

    expect(() => NotificationUI.create(testArgs)).toThrowError();
  });

  it('should throw an error when lastUpdate is not a Date', () => {
    const testArgs: any = {
      ...createArgs,
      lastUpdate: '2024-03-04T23:14:33.288930Z',
    };

    expect(() => NotificationUI.create(testArgs)).toThrowError();
  });

  it('should create a NotificationUI instance', () => {
    const notification = NotificationUI.create(createArgs);

    expect(notification).toBeInstanceOf(NotificationUI);
    expect(notification.id).toBe(createArgs.id);
    expect(notification.commType).toBe(createArgs.commType);
    expect(notification.title).toBe(createArgs.title);
    expect(notification.description).toBe(createArgs.description);
    expect(notification.status).toBe(createArgs.status);
    expect(notification.platform).toBe(createArgs.platform);
    expect(notification.lastUpdate).toEqual(
      new Date(createArgs.lastUpdate as any)
    );
    expect(notification.merchants).toEqual([2239]);
  });
});
