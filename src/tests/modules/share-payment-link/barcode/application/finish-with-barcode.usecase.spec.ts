import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { FinishWithBarcodeUsecase } from '../../../../../app/modules/share-payment-link/barcode/application/finish-with-barcode.usecase';
import { BarCodeRepository } from '../../../../../app/modules/share-payment-link/barcode/domain/barcode.repository';

describe('FinishWithBarcodeUsecase', () => {
  let usecase: FinishWithBarcodeUsecase;
  let repository: jasmine.SpyObj<BarCodeRepository>;
  let loaderService: jasmine.SpyObj<LoaderService>;
  let successNotifier: jasmine.Spy;
  let infoNotifier: jasmine.Spy;
  let errorHandler: jasmine.Spy;

  const loanId = 12345;
  const customerId = '123456';

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        FinishWithBarcodeUsecase,
        {
          provide: BarCodeRepository,
          useValue: jasmine.createSpyObj('BarCodeRepository', ['payLoan']),
        },
        {
          provide: LoaderService,
          useValue: jasmine.createSpyObj('LoaderService', ['show', 'hide']),
        },
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
      ],
    });

    usecase = TestBed.inject(FinishWithBarcodeUsecase);
    repository = TestBed.inject(
      BarCodeRepository
    ) as jasmine.SpyObj<BarCodeRepository>;
    loaderService = TestBed.inject(
      LoaderService
    ) as jasmine.SpyObj<LoaderService>;

    loaderService.show.and.returnValue('loader-id');

    const notifier = TestBed.inject(NotifierService);
    successNotifier = spyOn(notifier, 'success').and.callThrough();
    infoNotifier = spyOn(notifier, 'info').and.callThrough();

    errorHandler = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(FinishWithBarcodeUsecase);
  });

  it('should handle error if customerId is missing', fakeAsync(() => {
    let result: any;

    usecase
      .execute({
        customerId: undefined as any,
        loanId: loanId,
        successMessage: 'test success message',
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe(
      'FinishWithBarcodeUsecase::execute::emptyArgument'
    );
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.payLoan).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
    expect(loaderService.hide).toHaveBeenCalledWith('loader-id');
  }));

  it('should throw an error if loan is missing', fakeAsync(() => {
    let result: any;

    usecase
      .execute({
        customerId: customerId,
        loanId: undefined as any,
        successMessage: 'test success message',
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe(
      'FinishWithBarcodeUsecase::execute::emptyArgument'
    );
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.payLoan).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
    expect(loaderService.hide).toHaveBeenCalledWith('loader-id');
  }));

  it('should throw an error if customerId is invalid', fakeAsync(() => {
    let result: any;

    usecase
      .execute({
        customerId: 'abc' as any,
        loanId: loanId,
        successMessage: 'test success message',
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe('Guard::againstInvalidNumbers');
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.payLoan).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
    expect(loaderService.hide).toHaveBeenCalledWith('loader-id');
  }));

  it('should call the repository and show success notification when valid inputs are provided', fakeAsync(() => {
    repository.payLoan.and.returnValue(of(void 0));
    let completed = false;

    usecase
      .execute({
        customerId: customerId,
        loanId: loanId,
        successMessage: 'test success message',
      })
      .subscribe({
        next: () => {},
        error: fail,
        complete: () => {
          completed = true;
        },
      });

    tick(2500);

    expect(repository.payLoan).toHaveBeenCalledWith({
      customerId: +customerId,
      loanId: loanId,
    });
    expect(infoNotifier).toHaveBeenCalledWith({
      title: 'test success message',
    });
    expect(errorHandler).toHaveBeenCalledTimes(0);
    expect(loaderService.hide).toHaveBeenCalledWith('loader-id');
    expect(completed).toBe(true);
  }));

  it('should handle repository errors', fakeAsync(() => {
    const error = new Error('Repository error');
    repository.payLoan.and.returnValue(throwError(() => error));

    usecase
      .execute({
        customerId: customerId,
        loanId: loanId,
        successMessage: 'test success message',
      })
      .subscribe({
        next: () => {},
        error: fail,
      });

    tick();

    expect(repository.payLoan).toHaveBeenCalledWith({
      customerId: +customerId,
      loanId: loanId,
    });
    expect(errorHandler).toHaveBeenCalledWith(error, jasmine.any(Object));
    expect(successNotifier).toHaveBeenCalledTimes(0);
    expect(loaderService.hide).toHaveBeenCalledWith('loader-id');
  }));
});
