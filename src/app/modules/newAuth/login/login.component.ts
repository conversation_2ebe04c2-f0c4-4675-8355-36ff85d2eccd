import { Async<PERSON>ip<PERSON>, NgIf } from '@angular/common';
import { Component, inject } from '@angular/core';
import { TagManagerService } from '@aplazo/front-analytics/tag-manager';
import { I18NService } from '@aplazo/i18n';
import { RedirectionService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import {
  LoginFormComponent,
  LoginFormTextUI,
} from '@aplazo/shared-ui/merchant';
import { map, take } from 'rxjs';
import { AuthType } from 'src/app/core/domain/auth-type.enum';
import { POS_ENVIRONMENT_CORE } from '../../../../app-core/domain/environments';
import { POS_APP_ROUTES } from '../../../core/domain/config/app-routes.core';
import { LoginCentralizedUseCase } from './application/login-centralized.usecase';

export interface ILoginFormInjectedTextUI {
  email: {
    label: string;
    placeholder: string;
    requiredError: string;
    emailPatternError: string;
    hideRequiredMark: boolean;
  };
  password: {
    label: string;
    placeholder: string;
    requiredError: string;
    hideRequiredMark: boolean;
  };
  submitButton: string;
}
export interface IMerchantLoginTextUIResponse {
  header: { title: string };
  form: ILoginFormInjectedTextUI;
  actions: {
    title: string;
    registerButton: string;
    customerLoginButton: string;
    forgotPasswordButton?: string;
  };
}

export interface IMerchantLoginTextUI {
  header: { title: string };
  form: LoginFormTextUI;
  actions: {
    title: string;
    registerButton: string;
    customerLoginButton: string;
    forgotPasswordButton?: string;
  };
}

@Component({
  standalone: true,
  selector: 'aplazo-login',
  imports: [
    AplazoLogoComponent,
    LoginFormComponent,
    AplazoButtonComponent,
    AsyncPipe,
    NgIf,
  ],
  template: `
    <ng-container *ngIf="textTemplate$ | async as text">
      <div
        class="flex flex-col justify-start gap-y-12 max-w-xs mx-auto min-h-screen text-center py-20">
        <aplz-ui-logo size="lg"></aplz-ui-logo>

        <h1 class="font-medium text-2xl">
          {{ text.header.title }}
        </h1>

        <aplz-mui-login-form
          (login)="login($event)"
          [textUI]="text.form"></aplz-mui-login-form>

        <h2 class="text-lg">
          {{ text.actions.title }}
        </h2>

        <button
          aplzButton
          aplzAppearance="stroked"
          size="md"
          [rounded]="true"
          [fullWidth]="true"
          (click)="goToRegisterMerchantRoute()">
          {{ text.actions.registerButton }}
        </button>

        <button
          aplzButton
          aplzAppeatance="basic"
          size="md"
          [rounded]="true"
          [fullWidth]="true"
          (click)="goToCustomerLoginRoute()">
          {{ text.actions.customerLoginButton }}
        </button>
      </div>
    </ng-container>
  `,
})
export class LoginComponent {
  readonly #redirectionService = inject(RedirectionService);
  readonly #i18n = inject(I18NService);
  readonly #tagService = inject(TagManagerService);
  readonly #appRoutes = inject(POS_APP_ROUTES);
  readonly #environment = inject(POS_ENVIRONMENT_CORE);
  readonly #scope = 'login';
  readonly #loginCentralizedUseCase = inject(LoginCentralizedUseCase);

  readonly textTemplate$ = this.#i18n
    .getTranslateObjectByKey<IMerchantLoginTextUIResponse>({
      key: 'template',
      scope: this.#scope,
    })
    .pipe(
      map<IMerchantLoginTextUIResponse, IMerchantLoginTextUI>(resp => ({
        actions: resp.actions,
        header: resp.header,
        form: {
          username: {
            label: resp.form.email.label,
            placeholder: resp.form.email.placeholder,
            requiredError: resp.form.email.requiredError,
            emailPatternError: resp.form.email.emailPatternError,
            hideRequiredMarker: resp.form.email.hideRequiredMark,
          },
          password: {
            label: resp.form.password.label,
            placeholder: resp.form.password.placeholder,
            requiredError: resp.form.password.requiredError,
            hideRequiredMarker: resp.form.password.hideRequiredMark,
          },
          submitButton: { label: resp.form.submitButton },
        },
      }))
    );

  login(credentials: { username: string; password: string }): void {
    this.#loginCentralizedUseCase
      .execute({
        merchantUsername: credentials.username,
        merchantPassword: credentials.password,
        authType: AuthType.LOGIN_RETRY,
      })
      .pipe(take(1))
      .subscribe({
        next: async userJwt => {
          if (userJwt.merchantId) {
            this.#tagService.trackEvent({
              event: 'success',
              merchantId: userJwt.merchantId,
              merchantName: userJwt.companyName,
              branchId: 0,
              branchName: '',
              description: {
                genericInfo: 'loginSuccess',
                startDate: '',
                endDate: '',
                status: '',
                searchTerm: '',
                pageNum: 0,
                pageSize: 0,
                loanId: 0,
                graphType: '',
                buttonName: '',
              },
            });

            this.#redirectionService.internalNavigation([
              this.#appRoutes.securedSelectBranch,
            ]);
          }
        },
        error: () => {
          this.#tagService.trackEvent({
            event: 'failure',
            merchantId: 0,
            merchantName: credentials.username,
            branchId: 0,
            branchName: '',
            description: {
              genericInfo: 'loginFailure',
              startDate: '',
              endDate: '',
              status: '',
              searchTerm: '',
              pageNum: 0,
              pageSize: 0,
              loanId: 0,
              graphType: '',
              buttonName: '',
            },
          });
        },
      });
  }

  goToRegisterMerchantRoute(): void {
    this.#redirectionService.externalNavigation(
      this.#environment.registerMerchantUrl,
      '_blank'
    );
  }

  goToCustomerLoginRoute(): void {
    this.#tagService.trackEvent({
      event: 'buttonClick',
      merchantId: 0,
      merchantName: '',
      branchId: 0,
      branchName: '',
      description: {
        buttonName: 'customerLogin',
        genericInfo: '',
        startDate: '',
        endDate: '',
        status: '',
        searchTerm: '',
        pageNum: 0,
        pageSize: 0,
        loanId: 0,
        graphType: '',
      },
    });
    this.#redirectionService.externalNavigation(
      this.#environment.customerLoginUrl,
      '_blank'
    );
  }

  goToForgotPasswordRoute(): void {
    this.#redirectionService.internalNavigation([
      '/',
      this.#appRoutes.authentication,
      this.#appRoutes.forgotPassword,
    ]);
  }
}
