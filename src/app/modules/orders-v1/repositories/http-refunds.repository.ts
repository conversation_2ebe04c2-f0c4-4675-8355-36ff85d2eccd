import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  POS_ENVIRONMENT_CORE,
  PosEnvironmentCoreType,
} from '../../../../app-core/domain/environments';
import { Refund } from '../domain/entities/refund';
import { RefundsRepository } from '../domain/repositories/refunds.repository';

@Injectable({ providedIn: 'root' })
export class HttpRefundsRepository implements RefundsRepository {
  constructor(
    private http: HttpClient,
    @Inject(POS_ENVIRONMENT_CORE)
    private environment: PosEnvironmentCoreType
  ) {}

  getInfoById(loanId: string): Observable<Refund[]> {
    return this.http.get<Refund[]>(
      `${this.environment.merchantDashApiUrl}api/v1/merchant/refund/filter`,
      {
        params: {
          element: loanId,
        },
      }
    );
  }
}
