import { Injectable, inject } from '@angular/core';
import { DialogService } from '@ngneat/dialog';
import { AplazoPopupComponent } from './aplazo-popup.component';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class AplazoPopupDialogService {
  private dialog = inject(DialogService);

  /**
   * Abre el diálogo con el HTML del popup.
   * @param htmlContent - Contenido HTML del popup
   * @returns Observable que emite cuando el diálogo se cierra con información de dismiss
   */
  openPopup(
    htmlContent: string
  ): Observable<{ permanentDismiss: boolean } | null> {
    console.log(
      '🎭 [AplazoPopupDialogService] Opening popup with HTML content, length:',
      htmlContent.length
    );
    console.log('🎭 [AplazoPopupDialogService] HTML content:', htmlContent);

    const dialogRef = this.dialog.open(AplazoPopupComponent, {
      data: { htmlContent },
      maxWidth: '600px',
      enableClose: true,
    });

    console.log('🎭 [AplazoPopupDialogService] Dialog opened successfully');

    return dialogRef.afterClosed$.pipe(
      tap(result => {
        console.log(
          '🎭 [AplazoPopupDialogService] Dialog closed with result:',
          result
        );
      })
    );
  }

  /**
   * Método legacy para compatibilidad - ahora usa openPopup directamente
   * @deprecated Use openPopup(htmlContent) instead
   */
  openPopupForBranch() {
    console.warn(
      'openPopupForBranch is deprecated. Use openPopup(htmlContent) instead.'
    );
    // Por ahora, mostrar un popup de prueba
    this.openPopup(
      '<h2>Popup de prueba</h2><p>Este es un popup de prueba.</p>'
    );
  }
}
