import {
  DynamicBanner,
  fromDynamicBannerToDynamicBannerContentUI,
} from '../../../../app/modules/dynamic-banners/domain/entities/banner';

describe('Banner Entity', () => {
  // Define test data once at the top of the describe block
  const testBanner: DynamicBanner = {
    id: 1,
    title: 'Test Banner',
    message: 'Test Message',
    applyAll: true,
    merchantIds: [1, 2],
    branchIds: [1, 2],
    buttonVisible: true,
    buttonLabel: 'Click Me',
    redirectUrl: 'https://example.com',
    bannerVisible: true,
    imageUrl: 'https://example.com/image.png',
    endAt: '2023-12-31T23:59:59Z',
    createdAt: '2023-01-01T00:00:00Z',
    updatedAt: '2023-01-01T00:00:00Z',
  };

  describe('fromDynamicBannerToDynamicBannerContentUI', () => {
    it('should correctly transform a DynamicBanner to DynamicBannerContentUI with complete data', () => {
      // Act
      const result = fromDynamicBannerToDynamicBannerContentUI(testBanner);

      // Assert
      expect(result).toEqual({
        id: 1,
        title: 'Test Banner',
        description: 'Test Message',
        imageUrl: 'https://example.com/image.png',
        ctaText: 'Click Me',
        ctaUrl: 'https://example.com',
        isInternalRedirect: false,
      });
    });

    it('should correctly transform a DynamicBanner with minimal data', () => {
      // Arrange
      const minimalBanner: DynamicBanner = {
        id: 1,
        title: 'Minimal Banner',
        message: '',
        applyAll: false,
        merchantIds: [],
        branchIds: [],
        buttonVisible: false,
        buttonLabel: '',
        redirectUrl: '',
        bannerVisible: true,
        imageUrl: '',
        endAt: '2023-12-31T23:59:59Z',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      };

      // Act
      const result = fromDynamicBannerToDynamicBannerContentUI(minimalBanner);

      // Assert
      expect(result).toEqual({
        id: 1,
        title: 'Minimal Banner',
        description: '',
        imageUrl: '',
        ctaText: '',
        ctaUrl: '',
        isInternalRedirect: true,
      });
    });

    it('should verify correct mapping of all fields', () => {
      // Arrange
      const customBanner: DynamicBanner = {
        ...testBanner,
        title: 'Custom Title',
        message: 'Custom Message',
        buttonLabel: 'Custom Button',
        redirectUrl: 'https://custom.com',
        imageUrl: 'https://custom.com/image.jpg',
      };

      // Act
      const result = fromDynamicBannerToDynamicBannerContentUI(customBanner);

      // Assert
      expect(result.title).toBe('Custom Title');
      expect(result.description).toBe('Custom Message');
      expect(result.ctaText).toBe('Custom Button');
      expect(result.ctaUrl).toBe('https://custom.com');
      expect(result.imageUrl).toBe('https://custom.com/image.jpg');
    });

    it('should identify internal URLs (no http/www prefix)', () => {
      // Arrange
      const internalBanner: DynamicBanner = {
        ...testBanner,
        redirectUrl: '/some/internal/path',
      };

      // Act
      const result = fromDynamicBannerToDynamicBannerContentUI(internalBanner);

      // Assert
      expect(result.isInternalRedirect).toBe(true);
    });

    it('should identify external URLs with http prefix', () => {
      // Arrange
      const externalBanner: DynamicBanner = {
        ...testBanner,
        redirectUrl: 'http://example.com',
      };

      // Act
      const result = fromDynamicBannerToDynamicBannerContentUI(externalBanner);

      // Assert
      expect(result.isInternalRedirect).toBe(false);
    });

    it('should identify external URLs with www prefix', () => {
      // Arrange
      const externalBanner: DynamicBanner = {
        ...testBanner,
        redirectUrl: 'www.example.com',
      };

      // Act
      const result = fromDynamicBannerToDynamicBannerContentUI(externalBanner);

      // Assert
      expect(result.isInternalRedirect).toBe(false);
    });
  });
});
