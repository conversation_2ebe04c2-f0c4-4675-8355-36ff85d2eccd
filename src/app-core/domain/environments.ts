import {
  EnvironmentProviders,
  InjectionToken,
  makeEnvironmentProviders,
} from '@angular/core';

export type PosEnvironmentCoreType = Readonly<{
  production: boolean;
  apiUrl: string;
  appName: string;
  posApiUrl: string;
  posWsUrl: string;
  hostname: string;
  landingpage: string;
  registerMerchantUrl: string;
  customerLoginUrl: string;
  authApiUrl: string;
  gtmId: string;
  promoApiUrl: string;
  featureFlagsApiKey: string;
  featureFlagsEnv: string;
  datadogApplicationId: string;
  datadogClientToken: string;
  datadogEnv: string;
  datadogService: string;
  merchantDashApiUrl: string;
  webchatApiKey: string;
  webchatBrandId: string;
  paymentsCoreApiUrl: string;
  mspaymentMicro: string;
  kountClientId: string;
  kountEnvironment: 'TEST' | 'PROD';
  msMerchantMicro: string;
  bifrostUrl: string;
  customerRegistrationMicro: string;
  exposedPublicApiUrl: string;
  uaUrl: string;
  i18nUrl: string;
}>;

const production = import.meta.env.NG_APP_PRODUCTION;
const apiUrl = import.meta.env.NG_APP_API_URL;
const appName = import.meta.env.NG_APP_APP_NAME;
const posApiUrl = import.meta.env.NG_APP_POS_API_URL;
const posWsUrl = import.meta.env.NG_APP_POS_WS_URL;
const hostname = import.meta.env.NG_APP_HOSTNAME;
const landingpage = import.meta.env.NG_APP_LANDINGPAGE;
const registerMerchantUrl = import.meta.env.NG_APP_REGISTER_MERCHANT_URL;
const customerLoginUrl = import.meta.env.NG_APP_CUSTOMER_LOGIN_URL;
const authApiUrl = import.meta.env.NG_APP_AUTH_API_URL;
const gtmId = import.meta.env.NG_APP_GTM_ID;
const promoApiUrl = import.meta.env.NG_APP_PROMO_API_URL;
const featureFlagsApiKey = import.meta.env.NG_APP_FEATURE_FLAGS_API_KEY;
const featureFlagsEnv = import.meta.env.NG_APP_FEATURE_FLAGS_ENV;
const datadogApplicationId = import.meta.env.NG_APP_DATADOG_APPLICATION_ID;
const datadogClientToken = import.meta.env.NG_APP_DATADOG_CLIENT_TOKEN;
const datadogEnv = import.meta.env.NG_APP_DATADOG_ENV;
const datadogService = import.meta.env.NG_APP_DATADOG_SERVICE;
const merchantDashApiUrl = import.meta.env.NG_APP_MERCHANT_DASH_API_URL;
const webchatApiKey = import.meta.env.NG_APP_WEBCHAT_API_KEY;
const webchatBrandId = import.meta.env.NG_APP_WEBCHAT_BRAND_ID;
const paymentsCoreApiUrl = import.meta.env.NG_APP_PAYMENTS_CORE_API_URL;
const mspaymentMicro = import.meta.env.NG_APP_MSPAYMENT_MICRO;
const kountClientId = import.meta.env.NG_APP_KOUNT_CLIENT_ID;
const kountEnvironment = import.meta.env.NG_APP_KOUNT_ENVIRONMENT;
const msMerchantMicro = import.meta.env.NG_APP_MS_MERCHANT_MICRO;
const bifrostUrl = import.meta.env.NG_APP_BIFROST_URL;
const customerRegistrationMicro = import.meta.env
  .NG_APP_CUSTOMER_REGISTRATION_MICRO;
const exposedPublicApiUrl = import.meta.env.NG_APP_EXPOSED_PUBLIC_API_URL;
const uaUrl = import.meta.env.NG_APP_UA_URL;
const i18nUrl = import.meta.env.NG_APP_I18N_URL;

export const posEnvironmentCore: PosEnvironmentCoreType = {
  production,
  apiUrl,
  appName,
  posApiUrl,
  posWsUrl,
  hostname,
  landingpage,
  registerMerchantUrl,
  customerLoginUrl,
  authApiUrl,
  gtmId,
  promoApiUrl,
  featureFlagsApiKey,
  featureFlagsEnv,
  datadogApplicationId,
  datadogClientToken,
  datadogEnv,
  datadogService,
  merchantDashApiUrl,
  webchatApiKey,
  webchatBrandId,
  paymentsCoreApiUrl,
  mspaymentMicro,
  kountClientId,
  kountEnvironment: ['TEST', 'PROD'].includes(kountEnvironment)
    ? (kountEnvironment as 'TEST' | 'PROD')
    : 'TEST',
  msMerchantMicro,
  bifrostUrl,
  customerRegistrationMicro,
  exposedPublicApiUrl,
  uaUrl,
  i18nUrl,
} as const;

export const POS_ENVIRONMENT_CORE = new InjectionToken<PosEnvironmentCoreType>(
  'POS_ENVIRONMENT_CORE'
);

export function providePosEnvironmentCore(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: POS_ENVIRONMENT_CORE,
      useValue: posEnvironmentCore,
    },
  ]);
}
