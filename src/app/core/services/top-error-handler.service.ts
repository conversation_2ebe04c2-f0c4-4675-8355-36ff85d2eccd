import { ErrorHandler, Injectable, Injector, inject } from '@angular/core';
import { ObservabilityService } from '@aplazo/front-observability';
import {
  BrowserLocalStorageService,
  BrowserReloaderService,
} from '@aplazo/merchant/shared';
import { ToastrService } from 'ngx-toastr';
import { take } from 'rxjs';
import { RuntimePosOfflineError } from '../domain/runtime-error';

@Injectable()
export class TopErrorHandlerService implements ErrorHandler {
  readonly #injector = inject(Injector);
  readonly #ERROR_STORAGE_KEY = 'loadChunkErrorAplazoPosui';

  // Need to get ToastrService from injector rather than constructor injection to avoid cyclic dependency error
  get toastr(): ToastrService {
    return this.#injector.get(ToastrService);
  }

  get localStorage(): BrowserLocalStorageService {
    return this.#injector.get(BrowserLocalStorageService);
  }

  get reloader(): BrowserReloaderService {
    return this.#injector.get(BrowserReloaderService);
  }

  get observability(): ObservabilityService {
    return this.#injector.get(ObservabilityService);
  }

  handleError(error: unknown) {
    const hasLoadChunkError = this.#hasLoadChunkError(error);
    const hasErrorInStorage = Boolean(
      this.localStorage.getItem(this.#ERROR_STORAGE_KEY)
    );

    if (hasLoadChunkError && hasErrorInStorage) {
      const s = this.toastr.warning(
        undefined,
        'Error al cargar el contenido deseado, recargue la página cuando tenga conexión a internet',
        {
          disableTimeOut: true,
        }
      );

      s.onHidden.pipe(take(1)).subscribe(() => {
        this.reloader.executeWithAPI();
      });

      return;
    }

    if (hasLoadChunkError && !hasErrorInStorage) {
      console.warn(error);
      this.reloader.executeWithAPI();

      this.observability.sendCustomLog({
        message: `reload by chunk error `,
        status: 'warn',
      });

      this.localStorage.setItem(this.#ERROR_STORAGE_KEY, 'true');
      return;
    }

    if (error instanceof RuntimePosOfflineError) {
      console.warn(
        `        message: controled runtime >> ${error.code}::_::${
          error.origin ? error.origin : error.message
        }`
      );
      this.observability.sendCustomLog({
        message: `controled runtime >> ${error.code}::_::${
          error.origin ? error.origin : error.message
        }`,
        status: 'warn',
      });
      return;
    }

    const message =
      typeof error !== 'string'
        ? (error as any)?.error?.message ??
          (error as any)?.error?.error ??
          (error as any)?.message ??
          'Estamos trabajando para resolver el error. Por favor, reintente más tarde o contacte a soporte.'
        : error;

    this.observability.sendCustomLog({
      message: `uncontroled runtime >> ${message}`,
      status: 'error',
    });

    console.error(error);
  }

  #hasLoadChunkError(error: unknown): boolean {
    const chunkFailedMessage = /Loading chunk (.)+ failed/;
    const err = error as any;
    const isValidObj = err != null && typeof err === 'object';
    let msg: string;

    if (isValidObj && Object.prototype.hasOwnProperty.call(err, 'message')) {
      msg = err.message as string;
    } else if (isValidObj && Object.prototype.hasOwnProperty.call(err, 'msg')) {
      msg = err.msg as string;
    } else if (
      isValidObj &&
      Object.prototype.hasOwnProperty.call(err, 'error') &&
      Object.prototype.hasOwnProperty.call(err.error, 'msg')
    ) {
      msg = err.error.msg as string;
    } else if (
      isValidObj &&
      Object.prototype.hasOwnProperty.call(err, 'error') &&
      typeof err.error === 'string'
    ) {
      msg = err.error;
    } else {
      msg = err;
    }

    return typeof msg === 'string'
      ? msg.match(chunkFailedMessage) !== null
      : false;
  }
}
