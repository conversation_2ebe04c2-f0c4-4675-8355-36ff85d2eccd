import { Component, inject } from '@angular/core';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { DialogRef } from '@ngneat/dialog';
import { Order } from '../../core/domain/order.interface';

@Component({
  standalone: true,
  selector: 'aplazo-share-only-phone',
  template: `
    <aplz-ui-card size="md">
      <header class="text-center">
        <h4 class="text-lg w-5/6 mx-auto">
          Indica el siguiente ID de compra al cliente
        </h4>
        <h5 class="font-medium text-lg my-4">
          {{ data.loan?.loanId }}
        </h5>
      </header>

      <div class="flex justify-end mt-8">
        <button aplzButton aplzAppearance="stroked" size="md" (click)="close()">
          Cerrar
        </button>
      </div>
    </aplz-ui-card>
  `,
  imports: [AplazoCardComponent, AplazoButtonComponent],
})
export class ShareOnlyLoanIdComponent {
  readonly #dialogRef: DialogRef<{
    loan: Order | null;
  }> = inject(DialogRef);

  get data() {
    return this.#dialogRef.data;
  }

  close(): void {
    this.#dialogRef.close();
  }
}
