<ng-container *ngIf="isLargeScreen$ | async as largeScreen">
  <aplz-ui-card *ngIf="text" appearance="flat">
    <table aplzSimpleTable>
      <tr aplzSimpleTableHeaderRow>
        <th
          aplzSimpleTableHeaderCell
          class="text-lg text-dark-tertiary font-medium pl-6">
          {{ text.product }}
        </th>
        <th
          aplzSimpleTableHeaderCell
          class="text-lg text-dark-tertiary font-medium pl-6">
          {{ text.sku }}
        </th>
        <th
          aplzSimpleTableHeaderCell
          class="text-right text-lg text-dark-tertiary font-medium pl-6">
          {{ text.quantity }}
        </th>
        <th
          aplzSimpleTableHeaderCell
          class="text-right text-lg text-dark-tertiary font-medium">
          {{ text.price }}
        </th>
        <th
          aplzSimpleTableHeaderCell
          class="text-right text-lg text-dark-tertiary font-medium">
          {{ text.actions.label }}
        </th>
      </tr>

      <tr
        aplzSimpleTableBodyRow
        *ngFor="let product of products; let first = first; let last = last"
        [ngClass]="{ last: last, first: first }"
        [striped]="true">
        <td aplzSimpleTableBodyCell class="text-lg font-medium">
          <span [aplzTooltip]="product.name">
            {{ product.name }}
          </span>
        </td>
        <td aplzSimpleTableBodyCell class="text-lg">
          <span [aplzTooltip]="product.sku">
            {{ product.sku }}
          </span>
        </td>
        <td aplzSimpleTableBodyCell class="text-lg text-right">
          {{ product.quantity }}
        </td>
        <td aplzSimpleTableBodyCell class="text-lg text-right">
          {{ product.price | aplzDynamicPipe: 'currency' }}
        </td>
        <td aplzSimpleTableBodyCell class="px-0 py-6">
          <div class="flex flex-wrap gap-2 items-center justify-end w-full">
            <button
              aplzButton
              aplzAppearance="stroked"
              size="md"
              aplzColor="light"
              (click)="edit(product)">
              {{ text.actions.editButton }}
            </button>

            <button
              aplzButton
              aplzAppearance="stroked"
              aplzColor="danger"
              size="md"
              (click)="delete(product)">
              {{ text.actions.deleteButton }}
            </button>
          </div>
        </td>
      </tr>
    </table>
  </aplz-ui-card>
</ng-container>
