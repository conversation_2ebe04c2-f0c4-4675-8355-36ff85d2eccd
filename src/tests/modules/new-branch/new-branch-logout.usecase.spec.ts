import { TestBed } from '@angular/core/testing';
import { JwtDecoderService, RedirectionService } from '@aplazo/merchant/shared';
import {
  LocalJwtDecoder,
  LocalRedirecter,
} from '@aplazo/merchant/shared-testing';
import {
  POS_ENVIRONMENT_CORE,
  posEnvironmentCore,
} from '../../../app-core/domain/environments';
import { StoreService } from '../../../app/core/application/services/store.service';
import { NewBranchLogoutUseCase } from '../../../app/modules/new-branch/application/new-branch-logout.usecase';
import { LocalStore } from '../../core/services/impl/local-store.service';

describe('NewBranchLogoutUseCase', () => {
  let useCase: NewBranchLogoutUseCase;
  let redirectionService: RedirectionService;
  let store: StoreService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        NewBranchLogoutUseCase,
        {
          provide: StoreService,
          useClass: LocalStore,
        },
        {
          provide: RedirectionService,
          useClass: LocalRedirecter,
        },
        {
          provide: JwtDecoderService,
          useClass: LocalJwtDecoder,
        },
        {
          provide: POS_ENVIRONMENT_CORE,
          useValue: posEnvironmentCore,
        },
      ],
    });

    redirectionService = TestBed.inject(RedirectionService);
    store = TestBed.inject(StoreService);
    useCase = TestBed.inject(NewBranchLogoutUseCase);
  });

  it('should be created', () => {
    expect(useCase).toBeTruthy();
  });

  it('should call clearConfig, clearStorage and internalNavigation on execute', () => {
    const clearSpy = spyOn(store, 'clearStore');
    const navigationSpy = spyOn(redirectionService, 'internalNavigation');
    useCase.execute();

    expect(clearSpy).toHaveBeenCalledTimes(1);
    expect(navigationSpy).toHaveBeenCalledTimes(1);
    expect(navigationSpy).toHaveBeenCalledWith('/');
  });
});
