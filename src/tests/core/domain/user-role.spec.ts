import { isValidByRole } from '../../../app/core/domain/user-role';

describe('UserRole', () => {
  it('should return false if the user is not a valid Role', () => {
    const result = isValidByRole('invalid');

    expect(result).withContext('The user is not a valid Role').toBeFalse();
  });

  it('should return true when the role is ROLE_MERCHANT', () => {
    const result = isValidByRole('ROLE_MERCHANT');

    expect(result).withContext('The user is a valid one').toBeTrue();
  });

  it('should return true when the role is ROLE_SELL_AGENT', () => {
    const result = isValidByRole('ROLE_SELL_AGENT');

    expect(result).withContext('The user is a valid one').toBeTrue();
  });

  it('should return true when the role is ROLE_MANAGER', () => {
    const result = isValidByRole('ROLE_MANAGER');

    expect(result).withContext('The user is a valid one').toBeTrue();
  });
});
