/**
 * ✅ MANUAL TEST FOR PERMANENT DISMISS FUNCTIONALITY
 * 
 * This file contains manual tests to verify that the permanent dismiss
 * functionality is working correctly. Run these in the browser console.
 */

import { PopupStorageService } from './application/services/popup-storage.service';

export class PermanentDismissManualTest {
  private storageService = new PopupStorageService();

  /**
   * Test 1: Basic permanent dismiss functionality
   */
  testBasicPermanentDismiss() {
    console.log('🧪 [TEST 1] Basic Permanent Dismiss');
    
    const popupId = 'test-popup-1';
    const branchId = '123';
    
    // Initially should not be permanently dismissed
    console.log('1. Initial state:', this.storageService.isPopupPermanentlyDismissed(popupId, branchId));
    // Expected: false
    
    // Mark as permanently dismissed
    this.storageService.markPopupAsPermanentlyDismissed(popupId, branchId);
    console.log('2. After marking as dismissed:', this.storageService.isPopupPermanentlyDismissed(popupId, branchId));
    // Expected: true
    
    // Should not show popup
    console.log('3. Should show popup:', this.storageService.shouldShowPopup(popupId, branchId));
    // Expected: false
    
    console.log('✅ Test 1 completed');
  }

  /**
   * Test 2: Different branches have separate dismiss states
   */
  testBranchSeparation() {
    console.log('🧪 [TEST 2] Branch Separation');
    
    const popupId = 'test-popup-2';
    const branch1 = '123';
    const branch2 = '456';
    
    // Mark as dismissed for branch1 only
    this.storageService.markPopupAsPermanentlyDismissed(popupId, branch1);
    
    console.log('1. Branch 123 dismissed:', this.storageService.isPopupPermanentlyDismissed(popupId, branch1));
    // Expected: true
    
    console.log('2. Branch 456 dismissed:', this.storageService.isPopupPermanentlyDismissed(popupId, branch2));
    // Expected: false
    
    console.log('3. Branch 123 should show:', this.storageService.shouldShowPopup(popupId, branch1));
    // Expected: false
    
    console.log('4. Branch 456 should show:', this.storageService.shouldShowPopup(popupId, branch2));
    // Expected: true (assuming not shown today)
    
    console.log('✅ Test 2 completed');
  }

  /**
   * Test 3: Clear permanent dismiss
   */
  testClearPermanentDismiss() {
    console.log('🧪 [TEST 3] Clear Permanent Dismiss');
    
    const popupId = 'test-popup-3';
    const branchId = '123';
    
    // Mark as dismissed
    this.storageService.markPopupAsPermanentlyDismissed(popupId, branchId);
    console.log('1. After marking dismissed:', this.storageService.isPopupPermanentlyDismissed(popupId, branchId));
    // Expected: true
    
    // Clear dismiss
    this.storageService.clearPermanentDismiss(popupId, branchId);
    console.log('2. After clearing dismiss:', this.storageService.isPopupPermanentlyDismissed(popupId, branchId));
    // Expected: false
    
    console.log('3. Should show popup again:', this.storageService.shouldShowPopup(popupId, branchId));
    // Expected: true (assuming not shown today)
    
    console.log('✅ Test 3 completed');
  }

  /**
   * Test 4: Daily limit vs permanent dismiss
   */
  testDailyVsPermanent() {
    console.log('🧪 [TEST 4] Daily vs Permanent Dismiss');
    
    const popupId = 'test-popup-4';
    const branchId = '123';
    const today = new Date().toISOString().split('T')[0];
    
    // Mark as shown today
    this.storageService.markPopupAsShown(popupId, branchId, today);
    console.log('1. After marking shown today:', this.storageService.shouldShowPopup(popupId, branchId));
    // Expected: false (daily limit)
    
    // Also mark as permanently dismissed
    this.storageService.markPopupAsPermanentlyDismissed(popupId, branchId);
    console.log('2. After permanent dismiss:', this.storageService.shouldShowPopup(popupId, branchId));
    // Expected: false (permanent dismiss)
    
    // Clear daily records but keep permanent dismiss
    this.storageService.clearRecordsForDate(today);
    console.log('3. After clearing daily records:', this.storageService.shouldShowPopup(popupId, branchId));
    // Expected: false (still permanently dismissed)
    
    console.log('✅ Test 4 completed');
  }

  /**
   * Test 5: Error handling
   */
  testErrorHandling() {
    console.log('🧪 [TEST 5] Error Handling');
    
    // Test with empty parameters
    console.log('1. Empty popup ID:', this.storageService.isPopupPermanentlyDismissed('', '123'));
    // Expected: false
    
    console.log('2. Empty branch ID:', this.storageService.isPopupPermanentlyDismissed('popup', ''));
    // Expected: false
    
    // Should not throw errors
    try {
      this.storageService.markPopupAsPermanentlyDismissed('', '123');
      this.storageService.markPopupAsPermanentlyDismissed('popup', '');
      console.log('3. No errors thrown with empty parameters ✅');
    } catch (error) {
      console.error('3. Error thrown:', error);
    }
    
    console.log('✅ Test 5 completed');
  }

  /**
   * Test 6: LocalStorage persistence
   */
  testPersistence() {
    console.log('🧪 [TEST 6] LocalStorage Persistence');
    
    const popupId = 'test-popup-6';
    const branchId = '123';
    
    // Mark as dismissed
    this.storageService.markPopupAsPermanentlyDismissed(popupId, branchId);
    
    // Create new service instance (simulates page reload)
    const newStorageService = new PopupStorageService();
    console.log('1. After "page reload":', newStorageService.isPopupPermanentlyDismissed(popupId, branchId));
    // Expected: true (persisted)
    
    console.log('✅ Test 6 completed');
  }

  /**
   * Run all tests
   */
  runAllTests() {
    console.log('🚀 Starting Permanent Dismiss Manual Tests');
    console.log('==========================================');
    
    // Clear storage first
    this.storageService.clearAllRecords();
    
    this.testBasicPermanentDismiss();
    console.log('');
    
    this.testBranchSeparation();
    console.log('');
    
    this.testClearPermanentDismiss();
    console.log('');
    
    this.testDailyVsPermanent();
    console.log('');
    
    this.testErrorHandling();
    console.log('');
    
    this.testPersistence();
    console.log('');
    
    console.log('🎉 All manual tests completed!');
    console.log('Check the console output above for results.');
    
    // Clean up
    this.storageService.clearAllRecords();
  }
}

// Export for browser console usage
(window as any).PermanentDismissTest = PermanentDismissManualTest;

/**
 * USAGE IN BROWSER CONSOLE:
 * 
 * const test = new PermanentDismissTest();
 * test.runAllTests();
 * 
 * Or run individual tests:
 * test.testBasicPermanentDismiss();
 */
