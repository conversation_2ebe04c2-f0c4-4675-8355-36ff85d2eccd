import { Injectable } from '@angular/core';
import {
  JwtDecoderService,
  LoaderService,
  NotifierService,
} from '@aplazo/merchant/shared';
import {
  MonoTypeOperatorFunction,
  Observable,
  OperatorFunction,
  catchError,
  defer,
  exhaustMap,
  map,
  of,
  pipe,
  switchMap,
  take,
  tap,
  zip,
} from 'rxjs';
import { AuthType } from 'src/app/core/domain/auth-type.enum';
import { CredentialsV2 } from 'src/app/core/domain/credentialsV2.interface';
import { StoreService } from '../../../../core/application/services/store.service';
import { IUserJwt } from '../../../../core/domain/access-token';
import { isValidByIntegrationType } from '../../../../core/domain/integration-type';
import { EventService } from '../../../../core/domain/repositories/events-service';
import { RuntimePosOfflineError } from '../../../../core/domain/runtime-error';
import { isValidByRole } from '../../../../core/domain/user-role';
import { LoginCentralizedRepository } from '../domain/login-centralized.repository';

@Injectable()
export class LoginCentralizedUseCase {
  constructor(
    private readonly loginCentralizedService: LoginCentralizedRepository,
    private readonly notifier: NotifierService,
    private readonly loader: LoaderService,
    private readonly decoder: JwtDecoderService<IUserJwt>,
    private readonly storeService: StoreService,
    private readonly eventService: EventService
  ) {}

  public execute(credentials: CredentialsV2): Observable<IUserJwt> {
    const loaderId = this.loader.show();

    const sanitizedUsername = credentials.merchantUsername.trim();

    return this.loginCentralizedService
      .execute({
        merchantPassword: credentials.merchantPassword,
        merchantUsername: sanitizedUsername,
        authType: AuthType.LOGIN_RETRY,
      })
      .pipe(
        take(1),
        this.tokenProcesses(),

        tap(() => {
          this.eventService
            .login({
              email: sanitizedUsername,
              success: true,
            })
            .subscribe();
        }),
        catchError(loginFlowError => {
          if (loginFlowError instanceof RuntimePosOfflineError) {
            throw loginFlowError;
          }

          throw new RuntimePosOfflineError(
            'Verifique su usuario / contraseña',
            'Login::emailValidation',
            'login-usecase-error'
          );
        }),

        catchError(error => {
          this.eventService
            .login({
              email: sanitizedUsername,
              success: false,
            })
            .subscribe();

          this.loader.hide(loaderId);

          if (error instanceof RuntimePosOfflineError) {
            console.warn(error.code);
            this.notifier.warning({
              title: '¡Ups! Parece que ha ocurrido un error',
              message: error.message,
            });
          } else {
            console.error(error);
            this.notifier.warning({
              title: '¡Ups! Parece que ha ocurrido un error',
              message: 'Verifique su correo / contraseña',
            });
          }

          throw error;
        })
      );
  }

  private tokenProcesses(): OperatorFunction<string, IUserJwt> {
    return pipe(
      this.decodeToken(),

      this.validateRole(),

      this.validateIntegrationType(),

      this.storeMerchanName(),

      this.storeUser()
    );
  }

  private decodeToken(): OperatorFunction<string, [IUserJwt, string]> {
    return pipe(
      exhaustMap(jwtToken => {
        if (!jwtToken) {
          throw new RuntimePosOfflineError(
            'Verifique su correo / contraseña',
            'Login::emptyToken',
            'login-usecase-error'
          );
        }

        return zip([
          defer(() => this.decoder.decodeToken(jwtToken) as Promise<IUserJwt>),
          of(jwtToken),
        ]);
      })
    );
  }

  private validateRole(): MonoTypeOperatorFunction<[IUserJwt, string]> {
    return pipe(
      map(([userJwt, token]) => {
        if (!isValidByRole(userJwt.role)) {
          throw new RuntimePosOfflineError(
            'El usuario no tiene privilegios para iniciar sesión',
            'Login::roles',
            'login-usecase-error'
          );
        }

        return [userJwt, token];
      })
    );
  }

  private validateIntegrationType(): MonoTypeOperatorFunction<
    [IUserJwt, string]
  > {
    return pipe(
      map(([userJwt, token]) => {
        if (!isValidByIntegrationType(userJwt.integrationType)) {
          throw new RuntimePosOfflineError(
            'Solo puede iniciar sesión si es integración propia o PosUI',
            'Login::integrationType',
            'login-usecase-error'
          );
        }

        return [userJwt, token];
      })
    );
  }

  private storeMerchanName(): MonoTypeOperatorFunction<[IUserJwt, string]> {
    return pipe(
      switchMap(([userJwt, token]) =>
        this.loginCentralizedService
          .getMerchantDetails(token)
          .pipe(map(resp => ({ userJwt, token, resp })))
      ),
      switchMap(({ userJwt, token, resp }) => {
        return defer(() => this.storeService.setMerchantName(resp.name)).pipe(
          map<void, [IUserJwt, string]>(() => [userJwt, token])
        );
      })
    );
  }

  private storeUser(): OperatorFunction<[IUserJwt, string], IUserJwt> {
    return pipe(
      exhaustMap(([userJwt, token]) => {
        this.storeService.setDecodedToken(userJwt);

        return of(this.storeService.setToken(token)).pipe(map(() => userJwt));
      })
    );
  }
}
