import { TestBed } from '@angular/core/testing';
import { PopupStorageService } from 'src/app/modules/shared/popup/application/services/popup-storage.service';
import { fakeAsync } from '@angular/core/testing';

describe('PopupStorageService', () => {
  let service: PopupStorageService;
  let localStorageSpy: jasmine.SpyObj<Storage>;
  let originalLocalStorage: Storage;

  beforeEach(() => {
    originalLocalStorage = window.localStorage;
    Object.defineProperty(window, 'localStorage', {
      value: jasmine.createSpyObj('Storage', [
        'getItem',
        'setItem',
        'removeItem',
      ]),
      configurable: true,
    });
    localStorageSpy = window.localStorage as any;
    TestBed.configureTestingModule({ providers: [PopupStorageService] });
    service = TestBed.inject(PopupStorageService);
  });

  afterEach(() => {
    Object.defineProperty(window, 'localStorage', {
      value: originalLocalStorage,
    });
    service.clearAllRecords();
  });

  it('should return true when popup was shown today', fakeAsync(() => {
    const today = new Date().toDateString();
    const mockRecords = [
      {
        popupId: 'popup1',
        branchId: '123',
        date: today,
        timestamp: Date.now(),
      },
    ];
    localStorageSpy.getItem.and.returnValue(JSON.stringify(mockRecords));
    expect(service.wasPopupShownToday('popup1', '123', today)).toBe(true);
  }));

  it('should return false when popup was not shown today', fakeAsync(() => {
    const today = new Date().toDateString();
    const mockRecords = [
      {
        popupId: 'popup1',
        branchId: '123',
        date: today,
        timestamp: Date.now(),
      },
    ];
    localStorageSpy.getItem.and.returnValue(JSON.stringify(mockRecords));
    expect(service.wasPopupShownToday('popup2', '123', today)).toBe(false);
  }));

  it('should return false when no records exist', fakeAsync(() => {
    localStorageSpy.getItem.and.returnValue(null);
    expect(service.wasPopupShownToday('popup1', '123', '2024-01-01')).toBe(
      false
    );
  }));

  it('should return false when localStorage throws error', fakeAsync(() => {
    localStorageSpy.getItem.and.throwError('Storage error');
    expect(service.wasPopupShownToday('popup1', '123', '2024-01-01')).toBe(
      false
    );
  }));

  it('should add new record when popup not shown today', fakeAsync(() => {
    const today = new Date().toDateString();
    localStorageSpy.getItem.and.returnValue('[]');
    service.markPopupAsShown('popup1', '123', today);
    expect(localStorageSpy.setItem).toHaveBeenCalledWith(
      'aplazo_popups_shown',
      jasmine.stringContaining('popup1')
    );
  }));

  it('should not add duplicate record when popup already shown today', fakeAsync(() => {
    const today = new Date().toDateString();
    const mockRecords = [
      {
        popupId: 'popup1',
        branchId: '123',
        date: today,
        timestamp: Date.now(),
      },
    ];
    localStorageSpy.getItem.and.returnValue(JSON.stringify(mockRecords));
    service.markPopupAsShown('popup1', '123', today);
    expect(localStorageSpy.setItem).not.toHaveBeenCalled();
  }));

  it('should handle localStorage error gracefully (markPopupAsShown)', fakeAsync(() => {
    localStorageSpy.getItem.and.throwError('Storage error');
    expect(() => {
      service.markPopupAsShown('popup1', '123', '2024-01-01');
    }).not.toThrow();
  }));

  // ✅ NEW TESTS FOR PERMANENT DISMISS FUNCTIONALITY
  describe('Permanent Dismiss Functionality', () => {
    it('should mark popup as permanently dismissed', fakeAsync(() => {
      localStorageSpy.getItem.and.returnValue('[]');

      service.markPopupAsPermanentlyDismissed('popup1', '123');

      expect(localStorageSpy.setItem).toHaveBeenCalledWith(
        'aplazo_popups_shown',
        jasmine.stringContaining('"permanentDismiss":true')
      );
    }));

    it('should return true when popup is permanently dismissed', fakeAsync(() => {
      const mockRecords = [
        {
          popupId: 'popup1',
          branchId: '123',
          date: '2024-01-01',
          timestamp: Date.now(),
          permanentDismiss: true,
        },
      ];
      localStorageSpy.getItem.and.returnValue(JSON.stringify(mockRecords));

      expect(service.isPopupPermanentlyDismissed('popup1', '123')).toBe(true);
    }));

    it('should return false when popup is not permanently dismissed', fakeAsync(() => {
      const mockRecords = [
        {
          popupId: 'popup1',
          branchId: '123',
          date: '2024-01-01',
          timestamp: Date.now(),
          permanentDismiss: false,
        },
      ];
      localStorageSpy.getItem.and.returnValue(JSON.stringify(mockRecords));

      expect(service.isPopupPermanentlyDismissed('popup1', '123')).toBe(false);
    }));

    it('should return false when no records exist for popup', fakeAsync(() => {
      localStorageSpy.getItem.and.returnValue('[]');

      expect(service.isPopupPermanentlyDismissed('popup1', '123')).toBe(false);
    }));

    it('should handle multiple popups with different dismiss states', fakeAsync(() => {
      const mockRecords = [
        {
          popupId: 'popup1',
          branchId: '123',
          date: '2024-01-01',
          timestamp: Date.now(),
          permanentDismiss: true,
        },
        {
          popupId: 'popup2',
          branchId: '123',
          date: '2024-01-01',
          timestamp: Date.now(),
          permanentDismiss: false,
        },
      ];
      localStorageSpy.getItem.and.returnValue(JSON.stringify(mockRecords));

      expect(service.isPopupPermanentlyDismissed('popup1', '123')).toBe(true);
      expect(service.isPopupPermanentlyDismissed('popup2', '123')).toBe(false);
    }));

    it('should not show popup when permanently dismissed', fakeAsync(() => {
      const mockRecords = [
        {
          popupId: 'popup1',
          branchId: '123',
          date: '2024-01-01',
          timestamp: Date.now(),
          permanentDismiss: true,
        },
      ];
      localStorageSpy.getItem.and.returnValue(JSON.stringify(mockRecords));

      expect(service.shouldShowPopup('popup1', '123')).toBe(false);
    }));

    it('should show popup when not permanently dismissed and not shown today', fakeAsync(() => {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString().split('T')[0];

      const mockRecords = [
        {
          popupId: 'popup1',
          branchId: '123',
          date: yesterdayStr,
          timestamp: Date.now(),
          permanentDismiss: false,
        },
      ];
      localStorageSpy.getItem.and.returnValue(JSON.stringify(mockRecords));

      expect(service.shouldShowPopup('popup1', '123')).toBe(true);
    }));

    it('should handle localStorage errors gracefully for permanent dismiss', fakeAsync(() => {
      localStorageSpy.getItem.and.throwError('Storage error');

      expect(() => {
        service.markPopupAsPermanentlyDismissed('popup1', '123');
      }).not.toThrow();

      expect(service.isPopupPermanentlyDismissed('popup1', '123')).toBe(false);
    }));
  });

  it('should remove records older than 30 days', fakeAsync(() => {
    const today = new Date();
    const oldDate = new Date(today.getTime() - 31 * 24 * 60 * 60 * 1000);
    const mockRecords = [
      {
        popupId: 'popup1',
        branchId: '123',
        date: oldDate.toDateString(),
        timestamp: oldDate.getTime(),
      },
      {
        popupId: 'popup2',
        branchId: '123',
        date: today.toDateString(),
        timestamp: today.getTime(),
      },
    ];
    localStorageSpy.getItem.and.returnValue(JSON.stringify(mockRecords));
    service.cleanupOldRecords();
    const lastCall = localStorageSpy.setItem.calls.mostRecent().args[1];
    expect(lastCall).toContain('popup2');
    expect(lastCall).not.toContain('popup1');
  }));

  it('should not call setItem when no old records to remove', fakeAsync(() => {
    const today = new Date();
    const mockRecords = [
      {
        popupId: 'popup1',
        branchId: '123',
        date: today.toDateString(),
        timestamp: today.getTime(),
      },
    ];
    localStorageSpy.getItem.and.returnValue(JSON.stringify(mockRecords));
    service.cleanupOldRecords();
    expect(localStorageSpy.setItem).not.toHaveBeenCalled();
  }));

  it('should remove all records from localStorage', fakeAsync(() => {
    service.clearAllRecords();
    expect(localStorageSpy.removeItem).toHaveBeenCalledWith(
      'aplazo_popups_shown'
    );
  }));

  it('should handle JSON parse error gracefully', fakeAsync(() => {
    localStorageSpy.getItem.and.returnValue('invalid json');
    expect(service.wasPopupShownToday('popup1', '123', '2024-01-01')).toBe(
      false
    );
  }));

  it('should handle setItem error gracefully', fakeAsync(() => {
    localStorageSpy.getItem.and.returnValue('[]');
    localStorageSpy.setItem.and.throwError('Storage error');
    expect(() => {
      service.markPopupAsShown('popup1', '123', '2024-01-01');
    }).not.toThrow();
  }));
});
