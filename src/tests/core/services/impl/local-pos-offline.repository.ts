import { UIDateRange } from '@aplazo/shared-ui';
import { Observable, of } from 'rxjs';
import { LoanRequest } from '../../../../app/core/domain/cart';
import { Challenge } from '../../../../app/core/domain/challenge';
import { Order } from '../../../../app/core/domain/order.interface';
import {
  CommonPosOfflineResponse,
  PosOfflineRepository,
  SendLinkRequestDTO,
} from '../../../../app/core/domain/repositories/pos-offline.repository';
import historicOrders from '../../../local-orders.db.json';

export class LocalPosOfflineRepository implements PosOfflineRepository {
  private readonly orders: Order[] = historicOrders;

  getLastLoanQrImage(branchId: string): Observable<string> {
    throw new Error('Method not implemented.');
  }

  getTodayOrders(): Observable<Order[]> {
    throw new Error('Method not implemented.');
  }

  createLoanWithPreloan(
    order: LoanRequest,
    preloanCode: string
  ): Observable<Order> {
    throw new Error('Method not implemented.');
  }

  getHistoricalWeekOrders(
    startAt?: string | undefined,
    endAt?: string | undefined
  ): Observable<Order[]> {
    const filteredOrders = this.orders.filter(order => {
      if (startAt && endAt) {
        return (
          new Date(order.createdAt).getTime() >= new Date(startAt).getTime() &&
          new Date(order.createdAt).getTime() <=
            new Date(endAt + 'T23:59:59').getTime()
        );
      }
      return true;
    });

    return of(filteredOrders);
  }

  getHistoricalReportByDate(startAt: string, endAt: string): Observable<any> {
    throw new Error('Method not implemented.');
  }

  getHistoricalSummaryByDate(dateRange: UIDateRange): Observable<{
    totalOrder: number;
    totalSale: number;
    avgPrice: number;
  } | null> {
    throw new Error('Method not implemented.');
  }

  deleteOrder(orderId: number): Observable<any> {
    return of({
      order_id: orderId,
      origin: 'posui',
    });
  }

  createLoan(order: LoanRequest): Observable<Order> {
    const today = new Date().toLocaleString();
    const newOrder: Order = {
      id: Math.floor(Math.random() * 100000),
      createdAt: today,
      updatedAt: today,
      DeletedAt: null,
      date: today,
      status: 'created',
      price: order.totalPrice,
      url: 'https://checkout-offline.aplazo.dev/main/' + order.cartId,
      loanId: Math.floor(Math.random() * 100000),
      merchantId: 2163,
      products: order.products.map(p => ({
        id: Math.floor(Math.random() * 100000),
        name: p.title,
        price: p.price,
        quantity: p.count,
        total: p.price * p.count,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        DeletedAt: null,
        description: p.description,
        title: p.title,
        imageUrl: p.imageUrl,
        externalId: p.externalId,
        merchantPosOrderId: Math.floor(Math.random() * 100000),
      })),
      branchId: 123,
      sellsAgentId: order.sellsAgentId,
    };
    return of(newOrder);
  }

  sendPaymentLink(request: SendLinkRequestDTO): Observable<any> {
    throw new Error('Method not implemented.');
  }

  refundOrder(loanId: number): Observable<any> {
    throw new Error('Method not implemented.');
  }

  sendWSPaymentLink(request: SendLinkRequestDTO): Observable<any> {
    throw new Error('Method not implemented.');
  }

  getQrImage(loanId: string): Observable<string> {
    throw new Error('Method not implemented.');
  }

  checkOrderStatus(
    loanId: number
  ): Observable<CommonPosOfflineResponse<Order>> {
    throw new Error('Method not implemented.');
  }

  getChallenges(branchId: number): Observable<Challenge[]> {
    throw new Error('Method not implemented.');
  }

  requestReport(): Observable<Blob> {
    throw new Error('Method not implemented.');
  }
}
