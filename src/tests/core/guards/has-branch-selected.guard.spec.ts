import { RedirectionService } from '@aplazo/merchant/shared';
import { StoreService } from '../../../app/core/application/services/store.service';
import { ROUTE_CONFIG } from '../../../app/core/domain/config/app-routes.core';
import { Branch } from '../../../app/core/domain/entities';
import { HasBranchSelectedGuard } from '../../../app/core/guards/has-branch-selected.guard';
import branches from '../../local-branches.db.json';
import { LocalRedirecter } from '../services/impl/local-redirecter.service';
import { LocalStore } from '../services/impl/local-store.service';

describe('HasBranchSelectedGuard', () => {
  let guard: HasBranchSelectedGuard;
  let store: StoreService;
  let redirecter: RedirectionService;

  beforeEach(() => {
    store = new LocalStore();
    redirecter = new LocalRedirecter();

    guard = new HasBranchSelectedGuard(store, redirecter, ROUTE_CONFIG);
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should allow navigation when selectedBranch not falsy', done => {
    const branch: Branch = branches[0];
    store.setSelectedBranch(branch);

    guard.canActivate().subscribe(canActivate => {
      expect(canActivate).toBeTruthy();

      done();
    });
  });

  it('should deny navigation when selectedBranch is falsy', done => {
    const branch: Branch | null = null;
    store.setSelectedBranch(branch);

    guard.canActivate().subscribe(canActivate => {
      expect(canActivate).toBeFalsy();

      done();
    });
  });

  it('should call internalNavigation when selectedBranch is falsy', done => {
    const internalRedirectSpy = spyOn(
      redirecter,
      'internalNavigation'
    ).and.callThrough();
    const branch: Branch | null = null;
    store.setSelectedBranch(branch);

    guard.canActivate().subscribe(() => {
      expect(internalRedirectSpy).toHaveBeenCalledTimes(1);

      done();
    });
  });
});
