import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { map, mergeMap, take } from 'rxjs';
import { StoreService } from '../application/services/store.service';
import { parseUrl } from '../domain/url-parser';

export const jwtInterceptor: HttpInterceptorFn = (request, next) => {
  const store = inject(StoreService);
  const isSvgResource = request.url.match(/\.svg$|\.json$/);
  const url = parseUrl(request.url);
  const isSSE = url?.pathname.endsWith('sse') ?? false;

  if (isSvgResource || isSSE) {
    return next(request);
  }

  return store.token$.pipe(
    take(1),
    map(accessToken =>
      accessToken
        ? request.clone({
            headers: request.headers.set(
              'Authorization',
              `Bearer ${accessToken.replace('Bearer ', '')}`
            ),
          })
        : request
    ),
    mergeMap(newReq => next(newReq))
  );
};
