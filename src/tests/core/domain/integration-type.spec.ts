import { isValidByIntegrationType } from '../../../app/core/domain/integration-type';

describe('isValidByIntegrationType', () => {
  it('should return false when value is not a valid integration type', () => {
    const value = 'INVALID';
    const result = isValidByIntegrationType(value);

    expect(result)
      .withContext(
        'should return false when value is not a valid integration type'
      )
      .toBeFalse();
  });

  it('should return true when value is a valid integration type API', () => {
    const result = isValidByIntegrationType('API');

    expect(result)
      .withContext(
        'should return true when value is a valid integration type API'
      )
      .toBeTrue();
  });
  it('should return true when value is a valid integration type API_OFFLINE', () => {
    const result = isValidByIntegrationType('API_OFFLINE');

    expect(result)
      .withContext(
        'should return true when value is a valid integration type API_OFFLINE'
      )
      .toBeTrue();
  });
  it('should return true when value is a valid integration type POSUI', () => {
    const result = isValidByIntegrationType('POSUI');

    expect(result)
      .withContext(
        'should return true when value is a valid integration type POSUI'
      )
      .toBeTrue();
  });
  it('should return true when value is a valid integration type WM_APLAZO', () => {
    const result = isValidByIntegrationType('WM_APLAZO');

    expect(result)
      .withContext(
        'should return true when value is a valid integration type WM_APLAZO'
      )
      .toBeTrue();
  });
});
