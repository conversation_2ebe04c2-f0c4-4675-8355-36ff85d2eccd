import { NgIf } from '@angular/common';
import { Component, inject } from '@angular/core';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { DialogRef } from '@ngneat/dialog';
import { Refund } from './domain/entities/refund';

@Component({
  standalone: true,
  selector: 'aplazo-refund-info',
  template: `
    <article class="flex flex-col items-center gap-y-4 mt-6">
      <ng-container *ngIf="data?.loanId; else emptyRefund">
        <h3 class="text-xl">
          Orden
          {{ data.loanId }}
        </h3>

        <h4 class="text-lg">
          Reembolso con estatus
          <strong>
            {{ data.statusRefund }}
          </strong>
        </h4>

        <p class="">
          Fecha de solicitud de reembolso:
          <span class="font-medium text-base">
            {{ data.created | aplzDynamicPipe: 'date' }}
          </span>
        </p>
      </ng-container>
    </article>

    <ng-template #emptyRefund>
      <h3 class="text-xl">La orden se confirmó satisfactoriamente</h3>
      <p class="text-base">
        El usuario puede consultar su nuevo saldo en la aplicación de Cashi
      </p>
    </ng-template>
  `,
  imports: [AplazoDynamicPipe, NgIf],
})
export class RefundInfoComponent {
  readonly #dialogRef: DialogRef<Refund> = inject(DialogRef);

  get data() {
    return this.#dialogRef.data;
  }
}
