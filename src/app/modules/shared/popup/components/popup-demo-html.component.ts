import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogRef } from '@ngneat/dialog';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'app-popup-demo-html',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div [innerHTML]="safeHtml()" class="popup-demo-html-content"></div>
  `,
  styles: [
    `
      .popup-demo-html-content {
        padding: 20px;
        max-width: 100%;
        overflow-wrap: break-word;
      }
    `,
  ],
})
export class PopupDemoHtmlComponent {
  private dialogRef = inject(DialogRef);
  private sanitizer = inject(DomSanitizer);

  safeHtml = signal<SafeHtml>(
    this.sanitizer.bypassSecurityTrustHtml(
      this.dialogRef.data?.htmlContent || '<p>No content</p>'
    )
  );
}
