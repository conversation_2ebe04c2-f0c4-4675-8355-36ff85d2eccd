import { Product } from '../domain/product.interface';
import { OrderMessages } from './order-message-type.enum';
import { PaymentErrorMessages } from './paymnet-error-message-type.enum';

export class Order {
  id: number;
  createdAt: Date | string;
  updatedAt: Date | string;
  DeletedAt: Date | string | null;
  date: Date | string;
  branchId?: number;
  status: string;
  price: number;
  url: string;
  loanId: number;
  merchantId: number;
  products: Product[];
  branch?: { id: number };
  sellsAgentId?: number;
  isWalmartOrder?: boolean;
}

export interface OrdersResponseDto {
  body: Order[];
  merchantId: number;
  type: OrderMessages;
}

export interface PaymentFailureEventBody {
  branchId: string;
  cashierMessage: string;
  code: string;
  createdAt: string;
  customerId: number;
  loanId: number;
  merchantId: number;
}

export interface ErrorResponseDto {
  body: PaymentFailureEventBody;
  merchantId: number;
  type: PaymentErrorMessages;
  errorCode?: number;
}
