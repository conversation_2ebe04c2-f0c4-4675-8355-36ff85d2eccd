export interface Branch {
  id: number;
  name: string;
  createdAt?: string | null;
  updatedAt?: string | null;
  DeletedAt?: string | null;
  banned?: boolean | null;
  merchantConfigId?: number | null;
  shareLinkFlags: {
    isWhatsappAllowed: boolean;
    isQrAllowed: boolean;
    isSMSAllowed: boolean;
    id?: number | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    DeletedAt?: string | null;
    branchId?: number | null;
  } | null;
  branchFeatureFlags?: {
    isSellAgentTrackEnable: boolean;
    isSupportChatEnable: boolean;
    isOrderRefundEnable: boolean;
    isTodayReportEnable: boolean;
    id?: number | null;
    createdAt?: string | null;
    updatedAt?: string | null;
    DeletedAt?: string | null;
    branchId?: number | null;
  } | null;
}
