import { AsyncPipe } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { LoaderService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconSpinCircle } from '@aplazo/ui-icons';
import {
  BehaviorSubject,
  combineLatest,
  delay,
  finalize,
  lastValueFrom,
  map,
  take,
} from 'rxjs';
import { AnalyticsService } from '../../../../../core/application/services/analytics.service';
import { SendQRCodeUseCase } from '../../../application/usecases/send-qr-code.usecase';
import { AplazoContestPositionWithSearchComponent } from '../../components/contest-position-with-search/contest-position-with-search.component';
import { ParticipantRankingComponent } from '../../components/participant-ranking/participant-ranking.component';
import { ContestStoreService } from '../../services/contest-store.service';

@Component({
  standalone: true,
  selector: 'app-contest-position',
  template: `
    <div class="max-w-4xl mx-auto">
      <app-contest-position-with-search></app-contest-position-with-search>

      @if (ranking$ | async; as ranking) {
        @if (ranking.hasDetails) {
          <h2 class="text-xl text-center my-8 text-pretty">
            El reto
            {{ ranking.isFutureDate ? ' termina ' : ' terminó ' }}

            <span class="ml-1">
              {{ ranking.campaignFinishDate }}
            </span>
          </h2>

          <div class="flex flex-wrap gap-7 justify-center">
            @if (ranking.data && ranking.data.length === 0) {
              <div
                class="flex flex-col items-center justify-center flex-grow flex-shrink-0 h-full max-w-60">
                <h2 class="text-lg text-dark-primary text-center text-pretty">
                  No tienes registros
                </h2>
                <p
                  class="font-light text-dark-secondary mt-6 text-pretty text-center">
                  Registra clientes y podrás ser uno de los ganadores de
                  increíbles premios.
                </p>
              </div>

              <!--
            TODO: The Jira Ticket https://aplazo.atlassian.net/browse/MEXP-519 dismiss handling tiers different from '4'
            at least for the moment.
            -->
            } @else if (!ranking.tier || ranking.tier !== '4') {
              <app-participant-ranking
                [list]="ranking.data"></app-participant-ranking>
            } @else if (ranking.tier === '4') {
              <div
                class="flex flex-col items-center justify-center flex-grow flex-shrink-0 h-full max-w-60">
                <h2 class="text-lg text-dark-primary text-center text-pretty">
                  Tienes los siguientes registros acumulados:
                </h2>
                <p
                  class="font-semibold text-dark-primary mt-2 text-center text-xl">
                  {{ ranking.data[0].totalRegistration }}
                </p>
              </div>
            }

            <div class="mx-auto">
              <h2
                class="my-4 text-lg text-dark-primary text-center text-pretty max-w-32 mx-auto">
                Mi QR
              </h2>
              <figure
                class="w-48 max-w-48 aspect-square mx-auto my-8 flex-grow flex-shrink-0">
                <img
                  class="max-w-full object-contain"
                  [src]="ranking.qrUrl"
                  alt="QR premios Aplazo personalizado por cada participante" />
              </figure>
              <div class="my-10 flex justify-center">
                <button
                  aplzButton
                  aplzAppearance="solid"
                  aplzColor="dark"
                  size="lg"
                  [rounded]="true"
                  [disabled]="
                    (showSpinner$ | async) === true ||
                    (isThrottled$ | async) === true
                  "
                  (click)="sendQRCode()">
                  <span class="mr-2">{{ buttonText$ | async }}</span>
                  @if (showSpinner$ | async) {
                    <aplz-ui-icon name="spin-circle" size="sm"></aplz-ui-icon>
                  }
                </button>
              </div>
            </div>
          </div>
        }
      }
    </div>
  `,
  imports: [
    AsyncPipe,
    ParticipantRankingComponent,
    AplazoContestPositionWithSearchComponent,
    AplazoButtonComponent,
    AplazoIconComponent,
  ],
})
export class AplazoContestPositionComponent implements OnInit {
  readonly #store = inject(ContestStoreService);
  readonly #analytics = inject(AnalyticsService);
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #loader = inject(LoaderService);
  readonly #sendQRUseCase = inject(SendQRCodeUseCase);
  readonly ranking$ = this.#store.contestRanking$;

  readonly #isThrottled$ = new BehaviorSubject<boolean>(false);
  readonly isThrottled$ = this.#isThrottled$.asObservable();

  readonly #isLoading$ = this.#loader.isLoading$;

  THROTTLE_TIME = 10000;

  readonly showSpinner$ = combineLatest([
    this.#isLoading$,
    this.isThrottled$,
  ]).pipe(map(([isLoading, isThrottled]) => isLoading || isThrottled));

  readonly isDisabled$ = this.showSpinner$;

  readonly buttonText$ = this.showSpinner$.pipe(
    map(isSpinning => (isSpinning ? 'Enviando QR...' : 'Enviar QR'))
  );

  constructor() {
    this.#iconRegister.registerIcons([iconSpinCircle]);
  }

  ngOnInit(): void {
    this.#analytics.track('pageView', {
      timestamp: Date.now() as any,
    });
  }

  async sendQRCode(): Promise<void> {
    const isDisabled = await lastValueFrom(this.isDisabled$.pipe(take(1)));

    if (isDisabled) {
      return;
    }

    this.#isThrottled$.next(true);
    this.#sendQRUseCase
      .execute()
      .pipe(
        delay(this.THROTTLE_TIME),
        finalize(() => {
          this.#isThrottled$.next(false);
        })
      )
      .subscribe();
  }
}
