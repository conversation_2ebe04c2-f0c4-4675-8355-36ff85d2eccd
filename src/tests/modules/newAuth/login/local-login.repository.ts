import { Observable, of, throwError } from 'rxjs';
import { Credentials } from '../../../../app/core/domain/credentials.interface';
import { LoginRepository } from '../../../../app/modules/newAuth/login/domain/login.repository';
import usersWithEmail from '../../../local-user-with-email.db.json';
import usersWithoutEmail from '../../../local-user-without-email.db.json';

export class LocalLoginRepository implements LoginRepository {
  private readonly usersWithEmail = usersWithEmail;
  private readonly usersWithoutEmail = usersWithoutEmail;

  public execute(credentials: Credentials): Observable<string> {
    const user = this.usersWithoutEmail.find(
      item =>
        item.sub === credentials.username &&
        item.password === credentials.password
    );

    if (!user) {
      return throwError(() => new Error('User not found'));
    }

    const { password, ...userWithoutPassword } = user;

    return of(JSON.stringify(userWithoutPassword));
  }

  public legacyByCredentials(credentials: Credentials): Observable<string> {
    const user = this.usersWithEmail.find(
      item =>
        item.sub === credentials.username &&
        item.password === credentials.password
    );

    if (!user) {
      return throwError(() => new Error('User not found'));
    }

    const { password, ...userWithoutPassword } = user;
    return of(JSON.stringify(userWithoutPassword));
  }

  public getMerchantDetails(token: string): Observable<{ name: string }> {
    const user = this.usersWithEmail.find(item => item.sub === token);

    return of({ name: user?.name ?? '' });
  }
}
