import { As<PERSON><PERSON><PERSON><PERSON>, NgIf, NgTemplateOutlet } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import {
  AplazoTruncateLengthDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { ValidateOTPUseCase } from 'src/app/modules/share-payment-link/no-internet-purchase/application/validate-otp.usecase';
import { RequestOTPUseCase } from '../../../app/modules/share-payment-link/no-internet-purchase/application/request-otp.usecase';
import { NoInternetPurchaseComponent } from '../../../app/modules/share-payment-link/no-internet-purchase/infra/components/no-internet-purchase.component';

describe('NoInternetPurchaseComponent', () => {
  let fixture: ComponentFixture<NoInternetPurchaseComponent>;
  let component: NoInternetPurchaseComponent;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        NgIf,
        AsyncPipe,
        NgTemplateOutlet,
        ReactiveFormsModule,
        OnlyNumbersDirective,
        AplazoTruncateLengthDirective,
        AplazoFormFieldDirectives,
        AplazoButtonComponent,
        AplazoIconComponent,
      ],
      providers: [
        {
          provide: RequestOTPUseCase,
          useValue: jasmine.createSpyObj('RequestOTPUseCase', ['execute']),
        },
        {
          provide: ValidateOTPUseCase,
          useValue: jasmine.createSpyObj('ValidateOTPUseCase', ['execute']),
        },
        AplazoIconRegistryService,
      ],
    });

    fixture = TestBed.createComponent(NoInternetPurchaseComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
