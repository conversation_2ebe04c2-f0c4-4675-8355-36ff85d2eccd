import { TestBed } from '@angular/core/testing';
import {
  JwtDecoderService,
  LoaderService,
  NotifierService,
} from '@aplazo/merchant/shared';
import {
  LocalJwtDecoder,
  LocalLoader,
  LocalNotifier,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, of } from 'rxjs';
import { AuthType } from 'src/app/core/domain/auth-type.enum';
import { LoginCentralizedUseCase } from 'src/app/modules/newAuth/login/application/login-centralized.usecase';
import { LoginCentralizedRepository } from 'src/app/modules/newAuth/login/domain/login-centralized.repository';
import { StoreService } from '../../../../app/core/application/services/store.service';
import { IUserJwt } from '../../../../app/core/domain/access-token';
import { EventService } from '../../../../app/core/domain/repositories/events-service';
import { LocalEventsService } from '../../../core/services/impl/local-event.service';
import { LocalStore } from '../../../core/services/impl/local-store.service';
import { LocalLoginCentralizedRepository } from './local-login-centralized.repository';

describe('Login Centralized Use case', () => {
  let usecase: LoginCentralizedUseCase;
  let repository: LoginCentralizedRepository;
  let notifier: NotifierService;
  let decoder: JwtDecoderService<IUserJwt>;
  let loader: LoaderService;
  let store: StoreService;
  let event: EventService;

  beforeEach(() => {
    const mockHttpHandler: any = {
      post: () => of(),
    };

    TestBed.configureTestingModule({
      providers: [
        LoginCentralizedUseCase,
        {
          provide: LoginCentralizedRepository,
          useClass: LocalLoginCentralizedRepository,
        },
        {
          provide: NotifierService,
          useClass: LocalNotifier,
        },
        {
          provide: JwtDecoderService,
          useClass: LocalJwtDecoder,
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: StoreService,
          useClass: LocalStore,
        },
        {
          provide: EventService,
          useClass: LocalEventsService,
        },
      ],
    });

    usecase = TestBed.inject(LoginCentralizedUseCase);
    repository = TestBed.inject(LoginCentralizedRepository);
    notifier = TestBed.inject(NotifierService);
    decoder = TestBed.inject(JwtDecoderService);
    loader = TestBed.inject(LoaderService);
    store = TestBed.inject(StoreService);
    event = TestBed.inject(EventService);
  });

  it('create instance', () => {
    expect(usecase).toBeTruthy();
  });

  it('should return a IUserJwt object on success when credentials are valid and have a username with email', async () => {
    const controlUser = {
      merchantId: 348,
      email: '<EMAIL>',
      role: 'ROLE_MERCHANT',
      name: 'Alice',
    };

    await lastValueFrom(
      usecase.execute({
        merchantUsername: '<EMAIL>',
        merchantPassword: '2323',
        authType: AuthType.LOGIN_RETRY,
      })
    ).then(user => {
      expect(user.name).toBe(controlUser.name);
      expect(user.role).toBe(controlUser.role);
      expect(user.merchantId).toBe(controlUser.merchantId);
      expect(user.sub).toBe(controlUser.email);
    });
  });

  it('should throw an exception when credentials are invalid', async () => {
    try {
      await lastValueFrom(
        usecase.execute({
          merchantUsername: '<EMAIL>',
          merchantPassword: '9999',
          authType: AuthType.LOGIN_RETRY,
        })
      );
      fail('The function should have thrown an error');
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
    }
  });
});
