import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { lastValueFrom, take } from 'rxjs';
import { POS_APP_ROUTES } from '../../core/domain/config/app-routes.core';
import { StoreService } from '../application/services/store.service';

export const hasNotSelectedBranchGuard: CanActivateFn = async () => {
  const store = inject(StoreService);
  const router = inject(Router);
  const routes = inject(POS_APP_ROUTES);

  const selectedBranch = await lastValueFrom(
    store.selectedBranch$.pipe(take(1))
  );

  const hasBranchSelected =
    selectedBranch != null &&
    typeof selectedBranch === 'object' &&
    // TODO: validate if this assumption is correct
    // maybe branch must have an id, merchantConfigId and name
    // at the same time
    (Object.prototype.hasOwnProperty.call(selectedBranch, 'id') ||
      Object.prototype.hasOwnProperty.call(
        selectedBranch,
        'merchantConfigId'
      ) ||
      Object.prototype.hasOwnProperty.call(selectedBranch, 'name'));

  if (hasBranchSelected) {
    return router.parseUrl(`/${routes.aplazoRoot}`);
  }

  return true;
};
