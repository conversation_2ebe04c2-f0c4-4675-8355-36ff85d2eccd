import { TestBed } from '@angular/core/testing';
import { Router, UrlTree } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { Observable, lastValueFrom } from 'rxjs';
import { StoreService } from '../../../app/core/application/services/store.service';
import { IUserJwt } from '../../../app/core/domain/access-token';
import {
  POS_APP_ROUTES,
  ROUTE_CONFIG,
} from '../../../app/core/domain/config/app-routes.core';
import { allowByIntegrationGuard } from '../../../app/core/guards/allow-by-integration.guard';
import users from '../../local-user-with-email.db.json';
import { LocalStore } from '../services/impl/local-store.service';

describe('AllowByIntegrationGuard', () => {
  let router: Router;
  let store: StoreService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      providers: [
        {
          provide: POS_APP_ROUTES,
          useValue: ROUTE_CONFIG,
        },
        {
          provide: StoreService,
          useClass: LocalStore,
        },
      ],
    });
    store = TestBed.inject(StoreService);
    router = TestBed.inject(Router);
  });

  it('should return true if integration type is POSUI', async () => {
    const result = TestBed.runInInjectionContext(
      allowByIntegrationGuard() as () => Observable<boolean | UrlTree>
    );
    const posuiIntTypeUserID = 234;
    const posuiUser = users.find(
      i => i.merchantId === posuiIntTypeUserID
    ) as IUserJwt;
    store.setDecodedToken(posuiUser);

    expect(await lastValueFrom(result)).toBeTruthy();
  });

  it('should return true if integration type is API', async () => {
    const result = TestBed.runInInjectionContext(
      allowByIntegrationGuard() as () => Observable<boolean | UrlTree>
    );
    const apiIntTypeID = 456;
    const posuiUser = users.find(
      i => i.merchantId === apiIntTypeID
    ) as IUserJwt;
    store.setDecodedToken(posuiUser);

    expect(await lastValueFrom(result)).toBeTruthy();
  });

  it('should call router.parseUrl if integration type is not Valid Integration Type', async () => {
    const parserSpy = spyOn(router, 'parseUrl');

    const result = TestBed.runInInjectionContext(
      allowByIntegrationGuard() as () => Observable<boolean | UrlTree>
    );
    const notValidIntTypeID = 8383;
    const posuiUser = users.find(
      i => i.merchantId === notValidIntTypeID
    ) as IUserJwt;
    store.setDecodedToken(posuiUser);

    await lastValueFrom(result);

    expect(parserSpy).toHaveBeenCalledTimes(1);
  });

  it('should call router.parseUrl if integration type is not Valid POSUI Integration Type', async () => {
    const parserSpy = spyOn(router, 'parseUrl');

    const result = TestBed.runInInjectionContext(
      allowByIntegrationGuard() as () => Observable<boolean | UrlTree>
    );
    const walmartNotCashiIntTypeID = 8384;
    const posuiUser = users.find(
      i => i.merchantId === walmartNotCashiIntTypeID
    ) as IUserJwt;
    store.setDecodedToken(posuiUser);

    await lastValueFrom(result);

    expect(parserSpy).toHaveBeenCalledTimes(1);
  });

  it('should not call router.parseUrl method if integration type is Valid Integration Type', async () => {
    const parserSpy = spyOn(router, 'parseUrl');
    const result = TestBed.runInInjectionContext(
      allowByIntegrationGuard() as () => Observable<boolean | UrlTree>
    );
    const apiIntTypeID = 456;
    const posuiUser = users.find(
      i => i.merchantId === apiIntTypeID
    ) as IUserJwt;
    store.setDecodedToken(posuiUser);

    await lastValueFrom(result);

    expect(parserSpy).toHaveBeenCalledTimes(0);
  });
});
