<section class="min-h-[90vh] px-8 pt-8 pb-[30vh]">
  <ng-container *ngIf="vm$ | async as ctx">
    <aplz-ui-card appearance="flat">
      <aplz-ui-tab-group
        [selectedIndex]="ctx.selectedIndex || 0"
        (tabSelectionChange)="changeTab($event)">
        <aplz-ui-tab *ngFor="let label of ctx.newItemsCounter">
          <aplz-ui-tab-label>
            <p class="flex items-center flex-grow-0 flex-shrink-0 px-4 gap-4">
              <span
                [class.font-medium]="label.newItemsCounter === 0"
                [class.font-black]="label.newItemsCounter > 0"
                [class.text-dark-black]="label.newItemsCounter > 0">
                {{ label.label }}
              </span>

              <span
                *ngIf="label.newItemsCounter > 0"
                class="relative inline-flex items-center justify-center w-6 aspect-square font-bold bg-transparent before:absolute before:rounded-full before:bg-special-danger before:w-full before:h-full before:z-[-1] z-[1] text-light text-[12px] flex-shrink-0 flex-grow-0">
                {{ label.newItemsCounter }}
              </span>
            </p>
          </aplz-ui-tab-label>

          <ng-container *ngIf="ctx.notifications.hasContent; else emptyList">
            <aplz-ui-tab-body>
              <article *ngFor="let groupedData of ctx.notifications.data">
                <h2 class="text-xl font-medium my-6">
                  {{ groupedData.label }}
                </h2>

                <aplazo-notification-card
                  [notification]="notif"
                  [dashed]="!isLast"
                  *ngFor="let notif of groupedData.content; last as isLast">
                </aplazo-notification-card>
              </article>
            </aplz-ui-tab-body>
          </ng-container>
        </aplz-ui-tab>
      </aplz-ui-tab-group>
    </aplz-ui-card>
    <ng-template #emptyList>
      <div class="pt-8 my-6">
        <aplz-ui-common-message
          [i18Text]="{
            title: ctx.textUI.title || '',
            description: ctx.textUI.description || ''
          }">
        </aplz-ui-common-message>
      </div>
    </ng-template>
  </ng-container>
</section>
