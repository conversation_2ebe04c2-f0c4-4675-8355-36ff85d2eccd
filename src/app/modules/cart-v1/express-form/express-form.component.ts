import { <PERSON><PERSON><PERSON><PERSON><PERSON>, NgIf, NgTemplateOutlet } from '@angular/common';
import {
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  TemplateRef,
  ViewChild,
  inject,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconSpinCircle } from '@aplazo/ui-icons';
import { DialogRef } from '@ngneat/dialog';
import { NgxMaskDirective } from 'ngx-mask';
import {
  BehaviorSubject,
  Subject,
  combineLatestWith,
  map,
  startWith,
  takeUntil,
  tap,
} from 'rxjs';
import { StoreService } from '../../../core/application/services/store.service';
import { AgentIdComponent } from '../agent-id.component';

export interface IExpressOrderInjectedTextUI {
  title: string;
  agentId: {
    inputLabel: string;
    inputPlaceholder: string;
    checkboxLabel: string;
    errorInvalidId: string;
    errorRequired: string;
  };
  priceControl: {
    label: string;
    placeholder: string;
    errorRequired: string;
    errorMin: string;
  };
  formActions: {
    confirm: string;
  };
  confirmDialog: {
    questionFirst: string;
    questionSecond: string;
    actions: {
      confirm: string;
      cancel: string;
    };
  };
}

@Component({
  standalone: true,
  imports: [
    AplazoButtonComponent,
    AplazoFormFieldDirectives,
    AplazoCardComponent,
    AplazoDynamicPipe,
    AplazoIconComponent,
    AgentIdComponent,
    ReactiveFormsModule,
    NgxMaskDirective,
    NgIf,
    AsyncPipe,
    NgTemplateOutlet,
  ],
  templateUrl: './express-form.component.html',
})
export class ExpressFormComponent implements OnInit, OnDestroy {
  readonly #dialog: DialogRef<unknown, { price: number; agentId: number }> =
    inject(DialogRef);
  readonly #store = inject(StoreService);
  readonly #registerIcon = inject(AplazoIconRegistryService);
  readonly #i18n = inject(I18NService);
  readonly #scope = 'express-form';

  readonly #isReadyToFinishOrder$ = new BehaviorSubject<boolean>(false);
  readonly #destroy = new Subject<void>();

  readonly textUI$ =
    this.#i18n.getTranslateObjectByKey<IExpressOrderInjectedTextUI>({
      key: 'content',
      scope: this.#scope,
    });
  readonly isSellAgentIdEnable$ = this.#store.selectedBranchFeatureFlags$.pipe(
    map(featureFlags => featureFlags?.isSellAgentTrackEnable || false)
  );

  @ViewChild('priceForm', {
    read: TemplateRef,
    static: true,
  })
  _priceForm!: TemplateRef<any>;

  @ViewChild('confirmMessage', {
    read: TemplateRef,
    static: true,
  })
  _confirmMsg!: TemplateRef<any>;

  currentContent: TemplateRef<any> | null = null;

  priceControl = new FormControl<string | null>(null, [
    Validators.required,
    Validators.min(1),
  ]);

  agentIdControl = new FormControl<string>('');

  formGroup = new FormGroup({
    price: this.priceControl,
    agentIdControl: this.agentIdControl,
  });

  get data() {
    return this.#dialog.data;
  }

  get isValidForm(): boolean {
    return (
      this.#isReadyToFinishOrder$.value &&
      this.formGroup.valid &&
      this.formGroup.dirty
    );
  }

  get price(): number {
    const price = this.priceControl.value;
    return price && !isNaN(+price) ? parseFloat(price) : 0;
  }

  constructor() {
    this.#registerIcon.registerIcons([iconSpinCircle]);
  }

  openConfirmation(): void {
    if (this.formGroup.valid && this.formGroup.dirty) {
      this.currentContent = this._confirmMsg;
    }
  }

  save(confirmOrder = false): void {
    if (!confirmOrder) {
      this.currentContent = this._priceForm;
      this.agentIdControl.setValue('');
      return;
    }

    const price = this.priceControl.value;
    this.#dialog.close({
      price: price && !isNaN(+price) ? parseFloat(price) : 0,
      agentId: Number(this.agentIdControl.value),
    });
  }

  ngOnInit(): void {
    this.currentContent = this._priceForm;
    this.priceControl.valueChanges
      .pipe(
        startWith(0),
        combineLatestWith(
          this.#store.minimumOrderAmount$,
          this.isSellAgentIdEnable$,
          this.agentIdControl.valueChanges.pipe(
            startWith(this.agentIdControl.value)
          )
        ),

        tap(([price, minimumOrderAmount, isSellAgentIdEnable]) => {
          if (!price || !minimumOrderAmount) {
            this.#isReadyToFinishOrder$.next(false);
            return;
          }

          const isPriceOverMinimumValidAmount =
            !isNaN(+price) && +price >= minimumOrderAmount;

          const isIdValidAndEnabled =
            this.agentIdControl.value != null &&
            this.agentIdControl.value !== '' &&
            +this.agentIdControl.value > 0;

          const isIdDisabled = this.agentIdControl.value == null;

          if (isSellAgentIdEnable) {
            this.#isReadyToFinishOrder$.next(
              isPriceOverMinimumValidAmount &&
                (isIdValidAndEnabled || isIdDisabled)
            );
            return;
          }

          this.#isReadyToFinishOrder$.next(isPriceOverMinimumValidAmount);
        }),

        takeUntil(this.#destroy)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy.next();
    this.#destroy.complete();
  }
}
