import { TestBed } from '@angular/core/testing';
import { RedirectionService } from '@aplazo/merchant/shared';
import { lastValueFrom, take } from 'rxjs';
import { StoreService } from '../../../app/core/application/services/store.service';
import { IUserJwt } from '../../../app/core/domain/access-token';
import {
  POS_APP_ROUTES,
  ROUTE_CONFIG,
} from '../../../app/core/domain/config/app-routes.core';
import { NoLoggedOnlyGuard } from '../../../app/core/guards/no-logged-only.guard';
import users from '../../local-user-with-email.db.json';
import { LocalRedirecter } from '../services/impl/local-redirecter.service';
import { LocalStore } from '../services/impl/local-store.service';

describe('NoLoggedOnlyGuard', () => {
  let guard: NoLoggedOnlyGuard;
  let store: StoreService;
  let redirecter: RedirectionService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: POS_APP_ROUTES,
          useValue: ROUTE_CONFIG,
        },
        {
          provide: StoreService,
          useValue: new LocalStore(),
        },
        {
          provide: RedirectionService,
          useClass: LocalRedirecter,
        },
      ],
    });
    guard = TestBed.inject(NoLoggedOnlyGuard);
    store = TestBed.inject(StoreService);
    redirecter = TestBed.inject(RedirectionService);
  });

  afterEach(() => {
    store.clearStore();
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should allow navigation when isLoggedIn$ emit false', async () => {
    store.setToken(null);
    expect(await lastValueFrom(store.isLoggedIn$.pipe(take(1)))).toBeFalse();

    expect(await lastValueFrom(guard.canActivate())).toBeTruthy();
  });

  it('should deny navigation when isLoggedIn$ emit true', async () => {
    const posuiIntTypeUserID = 234;
    const posuiUser = users.find(
      i => i.merchantId === posuiIntTypeUserID
    ) as IUserJwt;
    store.setToken(JSON.stringify(posuiUser));

    expect(await lastValueFrom(store.isLoggedIn$.pipe(take(1)))).toBeTrue();

    expect(await lastValueFrom(guard.canActivate())).toBeFalse();
  });

  it('should call internalNavigation when isLoggedIn$ emit true', async () => {
    const internalRedirectSpy = spyOn(redirecter, 'internalNavigation');
    const posuiIntTypeUserID = 234;
    const posuiUser = users.find(
      i => i.merchantId === posuiIntTypeUserID
    ) as IUserJwt;
    store.setToken(JSON.stringify(posuiUser));

    expect(await lastValueFrom(store.isLoggedIn$.pipe(take(1)))).toBeTrue();

    await lastValueFrom(guard.canActivate());

    expect(internalRedirectSpy).toHaveBeenCalledTimes(1);
  });
});
