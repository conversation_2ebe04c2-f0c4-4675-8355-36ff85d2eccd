import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';

export interface IProductRequestUIDto {
  productId?: string;
  sku: string;
  name: string;
  quantity: number;
  price: string;
}

export interface IProductRequestProps {
  count: number;
  externalId: string;
  description: string;
  imageUrl: string;
  price: number;
  title: string;
}

export const invalidPriceError =
  'Error agregando productos al carrito: ingrese solo números para el precio de los productos';

export const invalidQuantityError =
  'Error agregando productos al carrito: el número de productos solo acepta números';

export class ProductRequest {
  public readonly title: string;
  public readonly imageUrl: string;
  public readonly description: string;
  public readonly externalId: string; //sku
  public readonly price: number; // float
  public readonly count: number;

  private constructor(product: IProductRequestProps) {
    this.count = product.count;
    this.externalId = product.externalId;
    this.description = product.description;
    this.imageUrl = product.imageUrl;
    this.price = product.price;
    this.title = product.title;
  }

  public static create(product: IProductRequestUIDto): ProductRequest {
    Guard.againstInvalidNumbers(
      +product.price,
      'ProductRequest::create::price',
      invalidPriceError
    );

    if (+product.price < 0) {
      throw new RuntimeMerchantError(
        invalidPriceError,
        'CreateProduct::InvalidPrice',
        'create-product-error'
      );
    }

    Guard.againstInvalidNumbers(
      product.quantity,
      'ProductRequest::create::quantity',
      invalidQuantityError
    );

    if (product.quantity < 0) {
      throw new RuntimeMerchantError(
        invalidQuantityError,
        'CreateProduct::InvalidQuantity',
        'create-product-error'
      );
    }

    return new ProductRequest({
      count: +product.quantity,
      externalId: product.sku,
      description: 'POS OFFLine',
      imageUrl:
        'https://aplazoassets.s3-us-west-2.amazonaws.com/aplazo-logo-png-colores.png',
      price: parseFloat(product.price),
      title: product.name,
    });
  }
}
