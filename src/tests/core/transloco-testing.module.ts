import {
  TranslocoTestingModule,
  TranslocoTestingOptions,
} from '@jsverse/transloco';
import cart from '../../assets/i18n/cart/es.json';
import es from '../../assets/i18n/es.json';
import historical from '../../assets/i18n/historical/es.json';
import login from '../../assets/i18n/login/es.json';
import orderGeneration from '../../assets/i18n/order-generation/es.json';
import orders from '../../assets/i18n/orders/es.json';
import principalLayout from '../../assets/i18n/principal-layout/es.json';
import promos from '../../assets/i18n/promos/es.json';
import selectBranch from '../../assets/i18n/select-branch/es.json';

export function getTranslocoModule(options: TranslocoTestingOptions = {}) {
  return TranslocoTestingModule.forRoot({
    langs: {
      es,
      'login/es': login,
      'select-branch/es': selectBranch,
      'cart/es': cart,
      'principal-layout/es': principalLayout,
      'orders/es': orders,
      'historical/es': historical,
      'promos/es': promos,
      'order-generation/es': orderGeneration,
    },
    translocoConfig: {
      availableLangs: ['es'],
      defaultLang: 'es',
    },
    preloadLangs: true,
    ...options,
  });
}
