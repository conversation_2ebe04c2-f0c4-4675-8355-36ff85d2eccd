import {
  invalidPriceError,
  invalidQuantityError,
  IProductRequestUIDto,
  ProductRequest,
} from '../../../app/core/domain/product-request';

describe('ProductRequest', () => {
  const prod: IProductRequestUIDto = {
    name: 'Product 1',
    price: '10',
    quantity: 1,
    sku: '123',
    productId: '1',
  };

  it('should throw an error if the price is not a number', () => {
    const test = {
      ...prod,
      price: 'abc',
    };

    expect(() => ProductRequest.create(test)).toThrowError(invalidPriceError);
  });

  it('should throw error if the price is less than 0', () => {
    const test = {
      ...prod,
      price: '-1',
    };

    expect(() => ProductRequest.create(test)).toThrowError(invalidPriceError);
  });

  it('should throw an error if the quantity is not a number', () => {
    const test = {
      ...prod,
      quantity: null,
    } as any;

    expect(() => ProductRequest.create(test)).toThrowError(
      invalidQuantityError
    );
  });

  it('should throw an error if the quantity is less than 0', () => {
    const test = {
      ...prod,
      quantity: -1,
    };

    expect(() => ProductRequest.create(test)).toThrowError(
      invalidQuantityError
    );
  });

  it('should create a product request', () => {
    const test = ProductRequest.create(prod);

    expect(test).toBeInstanceOf(ProductRequest);
    expect(test.title).toBe(prod.name);
    expect(test.price).toBe(+prod.price);
    expect(test.count).toBe(prod.quantity);
    expect(test.externalId).toBe(prod.sku);
    expect(test.description).toBe('POS OFFLine');
  });
});
