import { TestBed } from '@angular/core/testing';
import { TagManagerService } from '@aplazo/front-analytics/tag-manager';
import { of } from 'rxjs';
import { AnalyticsWithGtmService } from 'src/app/core/services/analytics-with-gtm.service';
import { StoreService } from '../../../app/core/application/services/store.service';
import { LocalStore } from './impl/local-store.service';

let service: AnalyticsWithGtmService;
let trackSpy: jasmine.Spy;

const setup = (store?: Partial<StoreService>) => {
  if (store) {
    TestBed.overrideProvider(StoreService, { useValue: store });
  }

  service = TestBed.inject(AnalyticsWithGtmService);
  trackSpy = spyOn(
    TestBed.inject(TagManagerService),
    'trackEvent'
  ).and.callThrough();
};

describe('AnalyticsWithGtmService', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        AnalyticsWithGtmService,
        {
          provide: StoreService,
          useClass: LocalStore,
        },
        {
          provide: TagManagerService,
          useValue: {
            trackEvent: () => void 0,
          },
        },
      ],
    });
  });

  it('should be created', () => {
    setup();
    expect(service).toBeTruthy();
  });

  it('should call trackEvent with default data', () => {
    setup();
    service.track('pageView');

    expect(trackSpy).toHaveBeenCalledTimes(1);
    expect(trackSpy).toHaveBeenCalledWith({
      event: 'pageView',
      merchantId: 0,
      merchantName: '',
      branchId: 0,
      branchName: '',
      description: undefined,
    });
  });

  it('should call trackEvent with the injected merchantId', () => {
    setup({
      getMerchantId$: () => of(1),
      selectedBranch$: of({} as any),
      merchantName$: of(''),
    });
    service.track('pageView');

    expect(trackSpy).toHaveBeenCalledTimes(1);
    expect(trackSpy).toHaveBeenCalledWith({
      event: 'pageView',
      merchantId: 1,
      merchantName: '',
      branchId: 0,
      branchName: '',
      description: undefined,
    });
  });

  it('should call trackEvent with the injected merchantName', () => {
    setup({
      getMerchantId$: () => of(1),
      selectedBranch$: of({} as any),
      merchantName$: of('merchant'),
    });
    service.track('pageView');

    expect(trackSpy).toHaveBeenCalledTimes(1);
    expect(trackSpy).toHaveBeenCalledWith({
      event: 'pageView',
      merchantId: 1,
      merchantName: 'merchant',
      branchId: 0,
      branchName: '',
      description: undefined,
    });
  });

  it('should call trackEvent with the injected branch', () => {
    setup({
      getMerchantId$: () => of(1),
      selectedBranch$: of({ id: 1, name: 'branch' } as any),
      merchantName$: of('merchant'),
    });
    service.track('pageView');

    expect(trackSpy).toHaveBeenCalledTimes(1);
    expect(trackSpy).toHaveBeenCalledWith({
      event: 'pageView',
      merchantId: 1,
      merchantName: 'merchant',
      branchId: 1,
      branchName: 'branch',
      description: undefined,
    });
  });

  it('should call trackEvent with optional data', () => {
    setup({
      getMerchantId$: () => of(1),
      selectedBranch$: of({ id: 1, name: 'branch' } as any),
      merchantName$: of('merchant'),
    });

    service.track('buttonClick', {
      buttonName: 'testButton',
    });

    expect(trackSpy).toHaveBeenCalledTimes(1);
    expect(trackSpy).toHaveBeenCalledWith({
      event: 'buttonClick',
      merchantId: 1,
      merchantName: 'merchant',
      branchId: 1,
      branchName: 'branch',
      description: {
        genericInfo: '',
        startDate: '',
        endDate: '',
        status: '',
        searchTerm: '',
        pageNum: 0,
        pageSize: 0,
        loanId: 0,
        graphType: '',
        buttonName: 'testButton',
      },
    });
  });
});
