import { NgIf } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { provideLoaderTesting } from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { TranslocoModule } from '@jsverse/transloco';
import { provideI18NTesting } from 'src/tests/i18n.local';
import { NewBranchAddUseCase } from '../../../app/modules/new-branch/application/new-branch-add.usecase';
import { NewBranchLogoutUseCase } from '../../../app/modules/new-branch/application/new-branch-logout.usecase';
import { NewBranchComponent } from '../../../app/modules/new-branch/new-branch.component';
import { getTranslocoModule } from '../../core/transloco-testing.module';

describe('NewBranchComponent', () => {
  let component: NewBranchComponent;
  let fixture: ComponentFixture<NewBranchComponent>;

  let logoutUsecaseSpy: jasmine.SpyObj<NewBranchLogoutUseCase>;
  let addBranchUseCaseSpy: jasmine.SpyObj<NewBranchAddUseCase>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        AplazoButtonComponent,
        AplazoLogoComponent,
        AplazoFormFieldDirectives,
        TranslocoModule,
        NgIf,
        ReactiveFormsModule,
        getTranslocoModule(),
      ],
      providers: [
        {
          provide: NewBranchLogoutUseCase,
          useValue: jasmine.createSpyObj('NewBranchLogoutUseCase', ['execute']),
        },
        {
          provide: NewBranchAddUseCase,
          useValue: jasmine.createSpyObj('NewBranchAddUseCase', ['execute']),
        },
        provideLoaderTesting(),
        provideI18NTesting('new-branch'),
      ],
    });
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(NewBranchComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    logoutUsecaseSpy = TestBed.inject(
      NewBranchLogoutUseCase
    ) as jasmine.SpyObj<NewBranchLogoutUseCase>;
    addBranchUseCaseSpy = TestBed.inject(
      NewBranchAddUseCase
    ) as jasmine.SpyObj<NewBranchAddUseCase>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should execute logoutUsecase when logout method is executed', () => {
    component.logOut();

    expect(logoutUsecaseSpy.execute).toHaveBeenCalledTimes(1);
  });

  it('should call logOut when logout button is clicked', () => {
    const logoutSpy = spyOn(component, 'logOut');
    const logoutButtonElement = fixture.debugElement.query(
      By.css('[data-test="new-branch-button-logout"]')
    );

    logoutButtonElement.triggerEventHandler('click', null);

    expect(logoutSpy).toHaveBeenCalledTimes(1);
  });

  it('should execute add newBranchUseCase when branchNameControl is valid and saveBranch method is executed', () => {
    component.branchNameControl.setValue('test');

    fixture.detectChanges();

    component.saveBranch();

    expect(addBranchUseCaseSpy.execute).toHaveBeenCalledTimes(1);
  });

  it('should call saveBranch when nameBranch input is valid and saveButton is clicked', async () => {
    const saveBranchSpy = spyOn(component, 'saveBranch');
    const saveButtonElement = fixture.debugElement.query(
      By.css('[data-test="new-branch-button-save-branch"]')
    );
    const nameBranchInputElement = fixture.debugElement.query(
      By.css('input[type="text"]')
    );

    saveButtonElement.nativeElement.click();
    fixture.detectChanges();

    expect(saveBranchSpy).toHaveBeenCalledTimes(0);

    nameBranchInputElement.nativeElement.value = 'test';
    nameBranchInputElement.nativeElement.dispatchEvent(new Event('input'));
    fixture.detectChanges();

    saveButtonElement.nativeElement.click();
    fixture.detectChanges();

    expect(saveBranchSpy).toHaveBeenCalledTimes(1);
  });
});
