import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { GetActiveBannerUsecase } from '../../application/get-active-banner.usecase';
import { DynamicBannerRepository } from '../../domain/repositories/dynamic-banner.repository';
import { DynamicBannerWithHttpClientRepository } from '../repositories/dynamic-banner-with-http-client.repository';

export function provideDynamicBanners(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: DynamicBannerRepository,
      useClass: DynamicBannerWithHttpClientRepository,
    },
    GetActiveBannerUsecase,
  ]);
}
