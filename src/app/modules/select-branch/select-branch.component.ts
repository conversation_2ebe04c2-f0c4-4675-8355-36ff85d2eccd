import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { LoaderService, RedirectionService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { iconSearch } from '@aplazo/ui-icons';
import { StatsigService } from '@statsig/angular-bindings';
import {
  Observable,
  combineLatest,
  debounceTime,
  lastValueFrom,
  map,
  startWith,
  take,
  tap,
} from 'rxjs';
import { AnalyticsService } from '../../core/application/services/analytics.service';
import { StoreService } from '../../core/application/services/store.service';
import { POS_APP_ROUTES } from '../../core/domain/config/app-routes.core';
import { Branch } from '../../core/domain/entities';
import { EventService } from '../../core/domain/repositories/events-service';

export interface SelectBranchTextTemplate {
  title: string;
  legend: string;
  searchbar: { placeholder: string };
  selectButton: string;
}

@Component({
  selector: 'aplazo-select-branch',
  standalone: true,
  templateUrl: './select-branch.component.html',
  imports: [
    ReactiveFormsModule,
    AplazoButtonComponent,
    AplazoIconComponent,
    AplazoLogoComponent,
    AplazoFormFieldDirectives,
    NgIf,
    NgFor,
    AsyncPipe,
  ],
})
export class SelectBranchComponent implements OnInit {
  readonly #storeService = inject(StoreService);
  readonly #redirectionService = inject(RedirectionService);
  readonly #loaderService = inject(LoaderService);
  readonly #i18n = inject(I18NService);
  readonly #scope = 'select-branch';
  readonly #appRoutes = inject(POS_APP_ROUTES);
  readonly #registryIconService = inject(AplazoIconRegistryService);
  readonly #eventService = inject(EventService);

  readonly #analyticsService = inject(AnalyticsService);
  readonly #featureFlagsService = inject(StatsigService);

  branchNameCtrl = new FormControl<string>('');
  currentSelection: Branch | null = null;

  branches$ = this.#storeService.branches$;
  filteredBranches$ = this.setFilteredBranches();
  injectedText$ = this.#i18n.getTranslateObjectByKey<SelectBranchTextTemplate>({
    key: 'template',
    scope: this.#scope,
  });

  constructor() {
    this.#registryIconService.registerIcons([iconSearch]);
  }

  setSelectedBranch(branch: Branch): void {
    this.currentSelection = branch;
  }

  async selectBranch(): Promise<void> {
    if (this.currentSelection !== null) {
      this.#storeService.setSelectedBranch(this.currentSelection);
      const merchantId = await lastValueFrom(
        this.#storeService.getMerchantId$().pipe(take(1))
      );

      this.#analyticsService.track('buttonClick', {
        buttonName: 'storefrontSelection',
      });

      await this.#featureFlagsService.updateUserAsync({
        ...this.#featureFlagsService.getClient()?.getContext().user,
        custom: {
          ...this.#featureFlagsService.getClient()?.getContext().user.custom,
          branchId: this.currentSelection.id,
          merchantId: String(merchantId),
        },
        userID: String(merchantId),
        customIDs: {
          branchID: String(this.currentSelection.id),
          storefrontID: String(this.currentSelection.id),
        },
      });

      this.#featureFlagsService.logEvent('posui_front_login', Date.now(), {
        merchantId: String(merchantId),
        branchId: String(this.currentSelection.id),
      });

      await lastValueFrom(
        this.#eventService
          .storeFront({
            storeFrontId: this.currentSelection.id,
            autoSelect: false,
          })
          .pipe(take(1))
      );

      await this.#redirectionService.internalNavigation([
        this.#appRoutes.aplazoRoot,
      ]);
    }
  }

  ngOnInit(): void {
    this.#storeService.selectedBranch$
      .pipe(
        take(1),
        tap(() => {
          this.#loaderService.forceHide();
        })
      )
      .subscribe(selectedBranch => {
        this.currentSelection = selectedBranch;
      });
  }

  private setFilteredBranches(): Observable<Branch[]> {
    return combineLatest([
      this.branches$,
      this.branchNameCtrl.valueChanges.pipe(
        startWith(''),
        debounceTime(450),
        tap(value => {
          if (value) {
            this.#analyticsService.track('search', {
              searchTerm: value,
            });
          }
        })
      ),
    ]).pipe(
      tap(([branches]) => {
        if (branches.length === 1) {
          this.currentSelection = branches[0];
          this.selectBranch();
        }
      }),
      map(([branches, branchName]) => {
        if (!branchName) {
          return branches;
        }
        return branches.filter(branch =>
          branch.name
            .toLocaleLowerCase()
            .includes(branchName.toLocaleLowerCase())
        );
      })
    );
  }
}
