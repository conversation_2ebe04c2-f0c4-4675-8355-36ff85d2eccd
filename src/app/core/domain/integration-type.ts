export const integrationTypes = [
  'API',
  'API_OFFLINE',
  'MGT',
  'SHOPI',
  'WOO',
  'PRESTA',
  'TDN',
  'SHOPIV2',
  'MGTV2',
  'VTEX',
  'POSUI',
  'WM_APLAZO',
  'WALMART',
  'VIRTUAL_CARD',
] as const;

export type IntegrationType = (typeof integrationTypes)[number];

export const validPosUiIntegrations = [
  'API',
  'API_OFFLINE',
  'POSUI',
  'WM_APLAZO',
] as const;

export type ValidIntegrationType = (typeof validPosUiIntegrations)[number];

export const isValidByIntegrationType = (value: string) =>
  validPosUiIntegrations.includes(value as ValidIntegrationType);
