export interface DynamicBanner {
  id: number;
  title: string;
  message: string;
  applyAll: boolean;
  merchantIds: number[];
  branchIds: number[];
  buttonVisible: boolean;
  buttonLabel: string;
  redirectUrl: string;
  bannerVisible: boolean;
  imageUrl: string;
  endAt: string; // ISO 8601
  createdAt: string; // ISO 8601
  updatedAt: string; // ISO 8601
}

export interface DynamicBannerContentUI {
  id: number;
  title: string;
  description: string;
  imageUrl: string;
  ctaText: string;
  ctaUrl: string;
  isInternalRedirect: boolean;
}

export const fromDynamicBannerToDynamicBannerContentUI = (
  banner: DynamicBanner
): DynamicBannerContentUI => {
  return {
    id: banner.id,
    title: banner.title,
    description: banner.message,
    imageUrl: banner.imageUrl,
    ctaText: banner.buttonLabel,
    ctaUrl: banner.redirectUrl,
    isInternalRedirect:
      !banner.redirectUrl.startsWith('http') &&
      !banner.redirectUrl.startsWith('www'),
  };
};
