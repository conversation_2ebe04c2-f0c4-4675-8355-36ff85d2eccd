import {
  HttpClient,
  HttpErrorResponse,
  HttpParams,
} from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable, catchError, throwError } from 'rxjs';
import {
  POS_ENVIRONMENT_CORE,
  PosEnvironmentCoreType,
} from '../../../app-core/domain/environments';
import { PaymentsRepository } from '../domain/repositories/payments.repository';

@Injectable({ providedIn: 'root' })
export class PaymentsService implements PaymentsRepository {
  constructor(
    @Inject(POS_ENVIRONMENT_CORE)
    private environment: PosEnvironmentCoreType,
    private httpClient: HttpClient
  ) {}

  requestCustomerAuthorizationCode(
    phoneNumber: string,
    loanToken: string
  ): Observable<any> {
    return this.httpClient.get<void>(
      `${this.environment.paymentsCoreApiUrl}/api/v1/checkout/${loanToken}/customer/${phoneNumber}`
    );
  }
  confirmFirstInstallment(
    loanId: number,
    phoneNumber: string,
    authCode: string,
    schemeId: number
  ): Observable<void> {
    let params = new HttpParams();
    params = params.append('loan-id', loanId);
    return this.httpClient
      .post<void>(
        `${this.environment.paymentsCoreApiUrl}/api/v1/payment/first-installment-offline`,
        { code: authCode, login: phoneNumber, schemeId },
        { params }
      )
      .pipe(
        catchError((error: HttpErrorResponse) =>
          throwError(() => Error(JSON.stringify(error.error)))
        )
      );
  }
}
