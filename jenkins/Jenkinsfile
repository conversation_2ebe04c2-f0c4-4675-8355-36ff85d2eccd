def slackResponse = slackSend(message: "Setting up Jenkins-<PERSON>...")
def SNX_IMAGE_TAG = ""
def REGULAR_IMAGE_TAG = ""

// Function to convert JSON content to .env format without exposing sensitive data to logs
def convertJsonToEnv(jsonFilePath, envFilePath = '.env') {
  // Use a separate script file to avoid command line exposure in logs
  writeFile file: 'convert_json_to_env.sh', text: '''#!/bin/bash
set -e
# Read JSON without printing to console
JSON_FILE="$1"
ENV_FILE="$2"

# Ensure the output file doesn't exist yet
rm -f "$ENV_FILE"

# Use jq to convert JSON to KEY=VALUE format without exposing values in logs
jq -r 'to_entries | map("\\(.key)=\\(.value)") | .[]' "$JSON_FILE" > "$ENV_FILE"

# Remove the JSON file to avoid leaving sensitive data
rm -f "$JSON_FILE"
'''

  // Make script executable and run it
  sh "chmod +x convert_json_to_env.sh"
  sh "./convert_json_to_env.sh '${jsonFilePath}' '${envFilePath}'"

  // Remove the script file
  sh "rm -f convert_json_to_env.sh"
}

// Function to deploy using Pulumi to the specified environment
def deployWithPulumi(projectName, environment, imageTag, passphrase = 'passphrase', region = 'us-west-1', s3Bucket = 'pulumi-apz-infra') {
  sh "cd services/$projectName && npm install && touch $passphrase"
  sh "pulumi login s3://$s3Bucket"
  sh "pulumi stack select ${projectName}.${environment} --cwd services/${projectName}/"
  sh "pulumi up -f -y -c ECR_TAG=${imageTag} -C services/${projectName}/"
}

pipeline {
  agent {
    node {
      label 'Jenkins-Slave-M'
    }
  }
  tools {
    nodejs 'node 22.2.0'
  }
  stages {
    stage('Prepare Environment') {
      steps {
        echo 'Getting secrets...'
        script {
          sh "curl -fsSL https://get.pulumi.com | sh"
          sh "$HOME/.pulumi/bin/pulumi version"
          PROJECT_NAME = env.JOB_NAME.tokenize("/").first()
          def userIds = slackUserIdsFromCommitters()
          def userIdsString = userIds.collect {
            "<@$it>"
          }.join(' ')
          def blocks = [
            ["type": "section", "text": ["type": "mrkdwn", "text": "*Project:* <$GIT_URL|$PROJECT_NAME>\n*Branch:* $GIT_BRANCH\n*Commit:* ${GIT_COMMIT.substring(0,7)}\n*Changes by* $userIdsString"]],
            ["type": "divider"]
          ]
          slackSend(channel: slackResponse.channelId, blocks: blocks, timestamp: slackResponse.ts)
          slackSend(channel: slackResponse.channelId, message: "Prepare Environment...", timestamp: slackResponse.ts)
          switch (GIT_BRANCH) {
          case 'integration/develop':
            environment = 'dev'
            break
          case "integration/stage":
            environment = 'stg'
            break
          case "master":
            environment = 'prod'
            break
          default:
            println("Branch value error: " + GIT_BRANCH)
          }
        }
        sh "aws secretsmanager get-secret-value --region us-west-1 --secret-id 'Jenkins/$PROJECT_NAME/$environment' --query SecretString --output text > envs.json"
      }
    }

    stage('Unit Tests') {
      steps {
        script {
          props = readJSON(file: "envs.json")
          slackSend(channel: slackResponse.channelId, color: "#24B0D5", message: "Running unit tests...", timestamp: slackResponse.ts)
          sh "aws secretsmanager get-secret-value --region us-west-1 --secret-id 'Service/$PROJECT_NAME/stg' --query SecretString --output text > secrets.json"
          // Convert JSON secrets.json file to proper KEY=VALUE format
          convertJsonToEnv('secrets.json')

          // Clear existing coverage directory
          sh 'rm -rf coverage || true'
          sh 'mkdir -p coverage'
        }

        echo "Running unit tests..."
        sh "docker-compose down posui_local_test || true"
        sh "docker-compose build --no-cache posui_local_test"
        sh "docker-compose run --rm posui_local_test"

        // Extract coverage data from the Docker volume
        sh '''
          # Get the volume name - use a more robust approach that doesn't rely on JOB_BASE_NAME
          # The volume name format is typically: posui_local_test_coverage_vol
          VOLUME_NAME=$(docker volume ls --filter name=posui_local_test_coverage_vol -q | grep "${JOB_NAME}" | head -n 1 || docker volume ls --filter name=posui_local_test_coverage_vol -q | grep "${PROJECT_NAME}" | head -n 1)

          # If still not found, try with a more generic approach
          if [ -z "$VOLUME_NAME" ]; then
            VOLUME_NAME=$(docker volume ls --filter name=posui_local_test_coverage_vol -q | head -n 1)
          fi

          if [ -z "$VOLUME_NAME" ]; then
            echo "No matching coverage volume found"
            exit 1
          fi

          echo "Using coverage volume: $VOLUME_NAME"

          # Create a temporary container that mounts the existing volume
          TEMP_CONTAINER=$(docker create -v ${VOLUME_NAME}:/coverage_data alpine:latest sh)

          # Start the container
          docker start ${TEMP_CONTAINER}

          # Copy data from the volume to the Jenkins workspace
          docker cp ${TEMP_CONTAINER}:/coverage_data/. ./coverage/

          # Stop and remove the temporary container
          docker stop ${TEMP_CONTAINER}
          docker rm ${TEMP_CONTAINER}
        '''

        // List coverage files to verify extraction
        sh "ls -la ./coverage"

        // Don't remove the volume until after extraction is confirmed
        sh "docker-compose down posui_local_test"
        sh "docker volume rm \$(docker volume ls --filter name=posui_local_test_coverage_vol -q | grep \"${PROJECT_NAME}\" | head -n 1 || docker volume ls --filter name=posui_local_test_coverage_vol -q | head -n 1) || true"
      }
    }
    stage('SonarQube Analysis') {
      steps {
        script {
          slackSend(channel: slackResponse.channelId, color: "#E89E2E", message: "Running static analysis... ", timestamp: slackResponse.ts)

          // Get package.json version using Node.js
          def packageVersion = sh(script: 'node -e "console.log(require(\'./package.json\').version)"', returnStdout: true).trim()

          // Get Git branch name
          def gitBranch = sh(script: 'git name-rev --name-only HEAD', returnStdout: true).trim()
          def sonarVersion = gitBranch == 'master' ? "master-${packageVersion}" : "development-${packageVersion}"

          def scannerHome = tool 'SonarScanner';

          withSonarQubeEnv() {
            sh "${scannerHome}/bin/sonar-scanner -Dsonar.projectVersion=${sonarVersion}"
          }
        }
      }
    }
    stage('Quality Gate Check') {
      steps {
        sleep(15)
        timeout(time: 2, unit: 'MINUTES') {
          waitForQualityGate abortPipeline: true
        }
      }
    }
    stage('Build and Push image') {
      steps {
        script {
          props = readJSON(file: "envs.json")
          slackSend(channel: slackResponse.channelId, color: "#24B0D5", message: "🏗️ Building and pushing images...", timestamp: slackResponse.ts)

          // Get the current date formatted for image tags
          def dateTag = sh(script: "date '+%d%m%y'", returnStdout: true).trim()

          echo "Retrieving secrets.json file for ${environment}"
          sh "rm -f secrets.json && aws secretsmanager get-secret-value --region us-west-1 --secret-id 'Service/$PROJECT_NAME/$environment' --query SecretString --output text > secrets.json"

          // Convert JSON secrets.json file to proper KEY=VALUE format
          convertJsonToEnv('secrets.json')

          echo 'Login to ECR'
          sh 'aws ecr get-login-password --region us-west-1 | docker login --username AWS --password-stdin 159200192518.dkr.ecr.us-west-1.amazonaws.com'

          echo 'Starting BUILD'
          def regularImageTag = "${dateTag}-${BUILD_NUMBER}"
          sh "docker build --build-arg ENV=$props.ENV --build-arg AUTH_TOKEN=$props.NEXUS_AUTH_TOKEN -t $props.ECR_URL:$props.IMAGE_TAG -t $props.ECR_URL:${regularImageTag} ."

          echo 'Pushing image to ECR'
          sh "docker push $props.ECR_URL:$props.IMAGE_TAG"
          sh "docker push $props.ECR_URL:${regularImageTag}"

          // Store the regular image tag for the Deploy stage
          REGULAR_IMAGE_TAG = regularImageTag
        }
      }
    }

    stage('Approve') {
      when {
        branch 'master'
      }
      steps {
        script {
          slackSend(color: "#24B0D5", channel: slackResponse.channelId, message: "<$RUN_DISPLAY_URL|Waiting for an approval...>", timestamp: slackResponse.ts)
        }
        input(message: 'Should we continue?', ok: 'Yes, we should')
      }
    }

    stage('Deploy') {
      environment {
        AWS_REGION = 'us-west-1'
        PULUMI_CONFIG_PASSPHRASE_FILE = 'passphrase'
      }
      steps {
        script {
          slackSend(channel: slackResponse.channelId, message: "Deploying to ${environment}...", timestamp: slackResponse.ts)
        }
        git(url: 'https://github.com/aplazo/node.pulumi-infrastructure.git', branch: 'master', credentialsId: 'github')
        nodejs(nodeJSInstallationName: "node 15.4.0") {
          withEnv(["PATH+PULUMI=$HOME/.pulumi/bin"]) {
            // Use the stored REGULAR_IMAGE_TAG
            deployWithPulumi(PROJECT_NAME, environment, REGULAR_IMAGE_TAG)
          }
        }
      }
    }

    stage('Sandbox Environment') {
      when {
        branch 'master'
      }
      agent {
        node {
          label 'Jenkins-Slave-Sandbox'
        }
      }
      environment {
        AWS_REGION = 'us-west-2'  // Sandbox AWS account region
      }
      steps {
        script {
          sh "aws secretsmanager get-secret-value --region us-west-2 --secret-id 'Jenkins/$PROJECT_NAME/snx' --query SecretString --output text > envs.json"
          props = readJSON(file: "envs.json")

          echo "Retrieving secrets.json file for sandbox environment"
          sh "rm -f secrets.json && aws secretsmanager get-secret-value --region us-west-2 --secret-id 'Service/$PROJECT_NAME/snx' --query SecretString --output text > secrets.json"
          convertJsonToEnv('secrets.json')

          echo 'Starting BUILD for sandbox environment'
          def dateTag = sh(script: "date '+%d%m%y'", returnStdout: true).trim()
          def sandboxImageTag = "${dateTag}-${BUILD_NUMBER}-snx"
          SNX_IMAGE_TAG = sandboxImageTag
          echo 'Login to ECR for Sandbox'
          sh 'aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin ************.dkr.ecr.us-west-2.amazonaws.com'

          sh "docker build --build-arg ENV=$props.ENV --build-arg AUTH_TOKEN=$props.NEXUS_AUTH_TOKEN -t $props.ECR_URL:$props.IMAGE_TAG -t $props.ECR_URL:${sandboxImageTag} ."

          echo 'Pushing sandbox environment images to ECR'
          sh "docker push $props.ECR_URL:$props.IMAGE_TAG"
          sh "docker push $props.ECR_URL:${SNX_IMAGE_TAG}"

          slackSend(channel: slackResponse.channelId, message: "Deploying to Sandbox environment...", timestamp: slackResponse.ts)
          echo "Using sandbox image tag: ${SNX_IMAGE_TAG}"
        }
        git(url: 'https://github.com/aplazo/node.pulumi-infrastructure.git', branch: 'master', credentialsId: 'github')
        withEnv(["PATH+PULUMI=$HOME/.pulumi/bin"]) {
          deployWithPulumi(PROJECT_NAME, "snx", SNX_IMAGE_TAG, 'sandbox_passphrase', 'us-west-2', 'pulumi-apz-infra-sandbox')
        }
      }
    }
  }
  post {
    always {
      script {
        def COLOR_MAP = ['SUCCESS': 'good', 'FAILURE': 'danger', 'UNSTABLE': 'danger', 'ABORTED': 'danger']
        slackSend(channel: slackResponse.channelId, color: COLOR_MAP[currentBuild.currentResult], message: "*${currentBuild.currentResult}:* <$RUN_DISPLAY_URL|Click here> for more info.", timestamp: slackResponse.ts)
      }
    }
  }
}
