import { Injectable } from '@angular/core';

export const ERRORS_DEFINITIONS = [
  '500',
  '417',
  '4101',
  '4102',
  'APC001',
  '4100',
  '4002',
  '4000',
  '4001',
  '4003',
  '4004',
  '4005',
  '4006',
  '4007',
  '4008',
  '4400',
  'LOAN_CANCELLED',
  'PURCHASE_LIMIT',
  '406',
  '4411',
  '4412',
  '4401',
  '4403',
  '4500',
  '4505',
  '409', // TBD 409 could be more than 1 case
] as const;

export type ErrorCode = (typeof ERRORS_DEFINITIONS)[number];

@Injectable({
  providedIn: 'root',
})
export class DynamicTokenErrorsService {
  getErrorByCode(error: { status: string; code?: string; error?: string }) {
    const currentCode =
      error?.code ?? error?.error ?? error?.status ?? undefined;
    return [404, 406, 412, 417].includes(+error.status)
      ? String(error.status)
      : currentCode;
  }
}
