import {
  ContestRegistrationUI,
  fromParticipantDetailResponseToContestRankingUI,
  fromUIToContestRegistrationRepositoryRequest,
  ParticipantDetailResponse,
} from '../../../../app/modules/contest-entries/domain/contest';
import { PremiosAplazoRole } from '../../../../app/modules/contest-entries/domain/roles';

describe('Contest Domain', () => {
  describe('fromParticipantDetailResponseToContestRankingUI', () => {
    it('should transform participant detail response to contest ranking UI', () => {
      // Arrange
      const mockResponse: ParticipantDetailResponse = {
        campaignParticipants: 10,
        currentPosition: 2,
        ranking: [
          { rank: 1, signup_completed_count: 15 },
          { rank: 2, signup_completed_count: 12 },
          { rank: 3, signup_completed_count: 8 },
        ],
        endDateCampaign: '2023-12-31T23:59:59Z',
        qr: 'https://example.com/qr.png',
        participantId: '12345',
        tier: 'not_tier',
      };

      // Act
      const result =
        fromParticipantDetailResponseToContestRankingUI(mockResponse);

      // Assert
      expect(result).toEqual({
        data: [
          { rank: 1, totalRegistration: 15, matched: false },
          { rank: 2, totalRegistration: 12, matched: true },
          { rank: 3, totalRegistration: 8, matched: false },
        ],
        campaignFinishDate: '2023-12-31T23:59:59Z',
        qrUrl: 'https://example.com/qr.png',
        hasDetails: true,
        participantId: '12345',
        tier: 'not_tier',
      });
    });

    it('should handle null signup_completed_count', () => {
      // Arrange
      const mockResponse: ParticipantDetailResponse = {
        campaignParticipants: 5,
        currentPosition: 1,
        ranking: [
          { rank: 1, signup_completed_count: null },
          { rank: 2, signup_completed_count: null },
        ],
        endDateCampaign: '2023-12-31T23:59:59Z',
        qr: 'https://example.com/qr.png',
      };

      // Act
      const result =
        fromParticipantDetailResponseToContestRankingUI(mockResponse);

      // Assert
      expect(result.data).toEqual([
        { rank: 1, totalRegistration: 0, matched: true },
        { rank: 2, totalRegistration: 0, matched: false },
      ]);
    });

    it('should set hasDetails to false when no participants or invalid position', () => {
      // Arrange
      const mockResponse: ParticipantDetailResponse = {
        campaignParticipants: 0,
        currentPosition: -1,
        ranking: [],
        endDateCampaign: '2023-12-31T23:59:59Z',
        qr: 'https://example.com/qr.png',
      };

      // Act
      const result =
        fromParticipantDetailResponseToContestRankingUI(mockResponse);

      // Assert
      expect(result.hasDetails).toBe(false);
    });
  });

  describe('fromUIToContestRegistrationRepositoryRequest', () => {
    it('should transform UI request to repository request with all fields', () => {
      // Arrange
      const uiRequest: ContestRegistrationUI = {
        fullName: 'John Doe',
        phone: '1234567890',
        region: 'CDMX',
        merchantName: 'Test Store',
        branchId: 123,
        role: 'Vendedor(a)',
        email: '<EMAIL>',
      };

      // Act
      const result = fromUIToContestRegistrationRepositoryRequest(uiRequest);

      // Assert
      expect(result).toEqual({
        fullName: 'John Doe',
        phoneNumber: '1234567890',
        region: 'CDMX',
        merchantName: 'Test Store',
        storeFrontId: '123',
        participantRole: 'Vendedor(a)',
        userMail: '<EMAIL>',
      });
    });

    it('should transform UI request without optional fields', () => {
      // Arrange
      const uiRequest: ContestRegistrationUI = {
        fullName: 'John Doe',
        phone: '1234567890',
        region: 'CDMX',
        merchantName: 'Test Store',
        branchId: 123,
      };

      // Act
      const result = fromUIToContestRegistrationRepositoryRequest(uiRequest);

      // Assert
      expect(result).toEqual({
        fullName: 'John Doe',
        phoneNumber: '1234567890',
        region: 'CDMX',
        merchantName: 'Test Store',
        storeFrontId: '123',
      });
      expect(result.userMail).toBeUndefined();
      expect(result.participantRole).toBeUndefined();
    });

    it('should throw error when required fields are missing', () => {
      // Arrange
      const uiRequest = {
        fullName: 'John Doe',
        // Missing phone
        region: 'CDMX',
        merchantName: 'Test Store',
        branchId: 123,
      } as ContestRegistrationUI;

      // Act & Assert
      expect(() =>
        fromUIToContestRegistrationRepositoryRequest(uiRequest)
      ).toThrowError(/phone is undefined or null/);
    });

    it('should throw error when fullName is empty', () => {
      // Arrange
      const uiRequest: ContestRegistrationUI = {
        fullName: '',
        phone: '1234567890',
        region: 'CDMX',
        merchantName: 'Test Store',
        branchId: 123,
      };

      // Act & Assert
      expect(() =>
        fromUIToContestRegistrationRepositoryRequest(uiRequest)
      ).toThrowError(/El nombre del participante es requerido/);
    });

    it('should throw error when merchantName is empty', () => {
      // Arrange
      const uiRequest: ContestRegistrationUI = {
        fullName: 'John Doe',
        phone: '1234567890',
        region: 'CDMX',
        merchantName: '',
        branchId: 123,
      };

      // Act & Assert
      expect(() =>
        fromUIToContestRegistrationRepositoryRequest(uiRequest)
      ).toThrowError(/El nombre del comercio es requerido/);
    });

    it('should not include email when invalid format', () => {
      // Arrange
      const uiRequest: ContestRegistrationUI = {
        fullName: 'John Doe',
        phone: '1234567890',
        region: 'CDMX',
        merchantName: 'Test Store',
        branchId: 123,
        email: 'invalid-email',
      };

      // Act
      const result = fromUIToContestRegistrationRepositoryRequest(uiRequest);

      // Assert
      expect(result.userMail).toBeUndefined();
    });

    it('should not include role when invalid', () => {
      // Arrange
      const uiRequest: ContestRegistrationUI = {
        fullName: 'John Doe',
        phone: '1234567890',
        region: 'CDMX',
        merchantName: 'Test Store',
        branchId: 123,
        role: 'INVALID_ROLE' as PremiosAplazoRole,
      };

      // Act
      const result = fromUIToContestRegistrationRepositoryRequest(uiRequest);

      // Assert
      expect(result.participantRole).toBeUndefined();
    });
  });
});
