import { <PERSON><PERSON><PERSON> } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { NgxMaskDirective } from 'ngx-mask';
import {
  NOTIFICATION_CHANNELS,
  NotificationChannel,
} from '../../../core/domain/dtos';
import { Branch } from '../../../core/domain/entities';

@Component({
  standalone: true,
  selector: 'aplazo-sent-whatsapp-link-form',
  imports: [
    NgFor,
    AplazoFormFieldDirectives,
    ReactiveFormsModule,
    AplazoButtonComponent,
    NgxMaskDirective,
  ],
  template: `
    <form class="py-4" [formGroup]="phoneGroup" (ngSubmit)="sendLink()">
      <h2 class="text-lg font-medium">
        Ingrese el número al que se enviará el link de pago.
      </h2>
      <div class="my-4">
        <div class="my-4">
          <fieldset class="rounded flex items-center gap-x-6 p-2">
            <ng-container *ngFor="let opt of channelOptions">
              <div class="flex-shrink-0 flex-grow-0 flex items-center gap-x-2">
                <input
                  type="radio"
                  name="channel"
                  [id]="opt"
                  [value]="opt"
                  [formControl]="channelControl"
                  class="flex-shrink-0 p-1 w-4 h-4 aspect-square text-aplazo-aplazo bg-dark-tertiary border-dark-secondary focus:ring-aplazo-aplazo focus:ring-2" />

                <label
                  [for]="opt"
                  class="flex-shrink-0 flex-grow-0 ms-2 font-medium text-dark select-none">
                  {{ opt }}
                </label>
              </div>
            </ng-container>
          </fieldset>
        </div>
        <aplz-ui-form-field>
          <input
            aplzFormInput
            [formControl]="phoneControl"
            placeholder="00 0000 0000"
            mask="99 9999 9999"
            inputmode="tel" />
          <p aplzInputPrefix class="pt-1 pl-2">+52</p>
        </aplz-ui-form-field>
      </div>
      <div class="flex justify-end gap-x-4">
        <button
          aplzButton
          aplzAppearance="stroked"
          size="md"
          type="button"
          (click)="cancel()">
          Cancelar
        </button>
        <button
          aplzButton
          aplzAppearance="solid"
          size="md"
          aplzColor="dark"
          type="submit"
          [disabled]="isSentButtonDisabled">
          Enviar
        </button>
      </div>
    </form>
  `,
})
export class SentWhatsappLinkFormComponent implements OnInit {
  readonly phoneControl = new FormControl<string>('', {
    validators: [Validators.required, Validators.minLength(10)],
    nonNullable: true,
  });

  readonly channelControl = new FormControl<NotificationChannel>(
    NOTIFICATION_CHANNELS.SMS,
    {
      validators: Validators.required,
      nonNullable: true,
    }
  );

  readonly phoneGroup = new FormGroup<{
    phone: FormControl<string>;
    channel: FormControl<string>;
  }>({
    phone: this.phoneControl,
    channel: this.channelControl,
  });

  @Input()
  public branch: Branch | null;

  @Output()
  public sendLinkEvent = new EventEmitter<{ phone: number; target: string }>();

  @Output()
  public cancelClick = new EventEmitter<void>();

  public channelOptions: NotificationChannel[] = [];

  public get isSentButtonDisabled(): boolean {
    return this.phoneGroup.pristine || this.phoneGroup.invalid;
  }

  public ngOnInit(): void {
    if (this.branch?.shareLinkFlags?.isSMSAllowed) {
      this.channelOptions.push(NOTIFICATION_CHANNELS.SMS);
    }

    if (this.branch?.shareLinkFlags?.isWhatsappAllowed) {
      this.channelOptions.push(NOTIFICATION_CHANNELS.WHATSAPP);
    }
  }

  public sendLink(): void {
    this.phoneGroup.markAllAsTouched();

    if (this.phoneGroup.touched && this.phoneGroup.valid) {
      this.sendLinkEvent.emit({
        phone: parseInt(this.phoneControl.value),
        target: this.channelControl.value,
      });
      this.phoneGroup.reset();
    }
  }

  public cancel(): void {
    this.cancelClick.emit();
  }
}
