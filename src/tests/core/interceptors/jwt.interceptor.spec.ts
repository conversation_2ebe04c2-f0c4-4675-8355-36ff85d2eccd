import {
  HttpClient,
  provideHttpClient,
  withInterceptors,
} from '@angular/common/http';
import {
  HttpClientTestingModule,
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { StoreService } from 'src/app/core/application/services/store.service';
import { jwtInterceptor } from 'src/app/core/interceptors/jwt.interceptor';

const setup = (token: string | null = null) => {
  TestBed.configureTestingModule({
    imports: [HttpClientTestingModule],
    providers: [
      provideHttpClient(withInterceptors([jwtInterceptor])),
      provideHttpClientTesting(),
      {
        provide: StoreService,
        useValue: {
          token$: of(token),
        },
      },
    ],
  });
  const httpController = TestBed.inject(HttpTestingController);
  const http = TestBed.inject(HttpClient);
  const store = TestBed.inject(StoreService);

  return { httpController, http, store };
};

describe('jwtInterceptor', () => {
  it('should add Authorization header', () => {
    const testToken = 'tokenTest';
    const { httpController, http } = setup(testToken);

    http.get('/test').subscribe();

    const req = httpController.expectOne('/test');

    expect(req.request.headers.get('Authorization')).toBe(
      'Bearer ' + testToken
    );

    httpController.verify();
  });

  it('should not add Authorization header to svg resource', () => {
    const { httpController, http } = setup('token');

    http.get('/test.svg').subscribe();

    const req = httpController.expectOne('/test.svg');

    expect(req.request.headers.get('Authorization')).toBeNull();

    httpController.verify();
  });

  it('should not add Authorization header to json resource', () => {
    const { httpController, http } = setup('token');

    http.get('/test.json').subscribe();

    const req = httpController.expectOne('/test.json');

    expect(req.request.headers.get('Authorization')).toBeNull();

    httpController.verify();
  });

  it('should not add Authorization header if token is null', () => {
    const { httpController, http } = setup();

    http.get('/test').subscribe();

    const req = httpController.expectOne('/test');

    expect(req.request.headers.get('Authorization')).toBeNull();

    httpController.verify();
  });
});
