import { Injectable, inject } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  EMPTY,
  Observable,
  catchError,
  finalize,
  map,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { ContestRepository } from '../../domain/repositories/contest.repository';
import { ContestStoreService } from '../../infra/services/contest-store.service';

const ERROR_MESSAGES = {
  NO_PARTICIPANT: 'Información del participante no disponible',
  SEND_ERROR: 'No se pudo enviar el QR. Vuelve a intentarlo.',
  THROTTLE: 'Espera un momento antes de volver a enviar el QR',
};

@Injectable()
export class SendQRCodeUseCase implements BaseUsecase<void, Observable<void>> {
  readonly #store = inject(ContestStoreService);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #usecaseError = inject(UseCaseErrorHandler);
  readonly #contestRepository = inject(ContestRepository);

  execute(): Observable<void> {
    const idLoader = this.#loader.show();

    return this.#store.contestRanking$.pipe(
      take(1),
      switchMap(ranking => {
        if (!ranking?.participantId) {
          throw new RuntimeMerchantError(
            ERROR_MESSAGES.NO_PARTICIPANT,
            'SendQRCode::NoParticipant',
            'send-qr-code-usecase-error'
          );
        }
        return this.#contestRepository.resendParticipantQR(
          ranking.participantId
        );
      }),
      finalize(() => {
        this.#loader.hide(idLoader);
      }),
      catchError((error: unknown) => {
        if (error instanceof RuntimeMerchantError) {
          return this.#usecaseError.handle(error, EMPTY);
        }

        this.#notifier.error({
          title: 'Error al enviar QR',
          message: ERROR_MESSAGES.SEND_ERROR,
        });

        return EMPTY;
      }),
      tap(() => {
        this.#notifier.success({
          title: 'El QR ha sido enviado a tu número registrado',
        });
      }),
      map(() => void 0)
    );
  }
}
