import { GtmMerchantEventDescription } from '@aplazo/front-analytics/tag-manager';

export const GTM_MERCHANT_EVENT_NAMES = [
  'pageView',
  'buttonClick',
  'dateRange',
  'selection',
  'status',
  'search',
  'pagination',
  'tabSelection',
  'graphTypeSelection',
  'success',
  'failure',
  'customClick',
  'loginRefreshed',
  'balancePageView',
  'downloadSuccess',
  'downloadFailure',
] as const;

export type GTMMerchantEventName = (typeof GTM_MERCHANT_EVENT_NAMES)[number];

export abstract class AnalyticsService {
  abstract track(
    eventName: GTMMerchantEventName,
    data?: Partial<GtmMerchantEventDescription>
  ): void;
}
