import { TestBed } from '@angular/core/testing';
import {
  ConnectionStatusService,
  NotifierService,
} from '@aplazo/merchant/shared';
import { provideNotifierTesting } from '@aplazo/merchant/shared-testing';
import { of } from 'rxjs';
import { allowNavigationWhenOnline } from '../../../app/core/guards/disallow-navigation-when-offline.guard';

const setup = (isOnline = true) => {
  TestBed.configureTestingModule({
    providers: [
      {
        provide: ConnectionStatusService,
        useValue: {
          status$: of({
            isOnline,
            isOffline: !isOnline,
          }),
        },
      },
      provideNotifierTesting(),
    ],
  });

  const guard = () =>
    TestBed.runInInjectionContext(
      allowNavigationWhenOnline as () => Promise<boolean>
    );

  const notifier = TestBed.inject(NotifierService);

  const notifierSpy = spyOn(notifier, 'warning').and.callThrough();

  return { guard, notifierSpy };
};

describe('DisallowNavigationWhenOfflineGuard', () => {
  it('should allow navigation when online', async () => {
    const { guard, notifierSpy } = setup(true);

    const result = await guard();

    expect(result)
      .withContext('Expected to allow navigation when online')
      .toBeTrue();
    expect(notifierSpy).toHaveBeenCalledTimes(0);
  });

  it('should disallow navigation when offline', async () => {
    const { guard, notifierSpy } = setup(false);

    const result = await guard();

    expect(result)
      .withContext('Expected to disallow navigation when offline')
      .toBeFalse();
    expect(notifierSpy).toHaveBeenCalledTimes(1);
  });

  it('should disallow leave the route when offline', async () => {
    const { guard, notifierSpy } = setup(false);

    const result = await guard();

    expect(result)
      .withContext('Expected to disallow navigation when offline')
      .toBeFalse();
    expect(notifierSpy).toHaveBeenCalledTimes(1);
  });

  it('should allow leave the route when online', async () => {
    const { guard, notifierSpy } = setup(true);

    const result = await guard();

    expect(result)
      .withContext('Expected to allow leave the route when it is online')
      .toBeTrue();
    expect(notifierSpy).toHaveBeenCalledTimes(0);
  });
});
