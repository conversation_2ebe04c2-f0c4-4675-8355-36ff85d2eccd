import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { GetRefundInfoUsecase } from '../application/usecases/get-refund-info.usecase';
import { RefundsRepository } from '../domain/repositories/refunds.repository';
import { HttpRefundsRepository } from '../repositories/http-refunds.repository';

export function provideRefundsRepositories(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: RefundsRepository,
      useClass: HttpRefundsRepository,
    },
  ]);
}

export function provideRefundsUseCases(): EnvironmentProviders {
  return makeEnvironmentProviders([GetRefundInfoUsecase]);
}
