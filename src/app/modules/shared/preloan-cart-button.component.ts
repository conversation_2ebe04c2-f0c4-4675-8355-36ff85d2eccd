import { Component, inject } from '@angular/core';
import { RedirectionService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { ROUTE_CONFIG } from 'src/app/core/domain/config/app-routes.core';

@Component({
  standalone: true,
  selector: 'aplazo-preloan-cart-button',
  template: ` <button
    aplzButton
    aplzAppearance="solid"
    aplzColor="accent"
    size="md"
    (click)="goToPreloanCart()">
    Nueva orden prepago
  </button>`,
  imports: [AplazoButtonComponent],
})
export class PreloanCartButtonComponent {
  readonly #redirecter = inject(RedirectionService);

  goToPreloanCart(): void {
    this.#redirecter.internalNavigation([
      ROUTE_CONFIG.aplazoRoot,
      ROUTE_CONFIG.aplazoLayout,
      ROUTE_CONFIG.preloanCart,
    ]);
  }
}
