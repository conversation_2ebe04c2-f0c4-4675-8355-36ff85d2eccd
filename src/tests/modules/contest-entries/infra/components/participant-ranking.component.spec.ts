import { Async<PERSON>ipe } from '@angular/common';
import { fakeAsync, flush, TestBed } from '@angular/core/testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { lastValueFrom, take } from 'rxjs';
import { ParticipantRankUI } from '../../../../../app/modules/contest-entries/domain/contest';
import { ParticipantRankingComponent } from '../../../../../app/modules/contest-entries/infra/components/participant-ranking/participant-ranking.component';

// Mock AplazoIconRegistryService with the necessary methods
class MockAplazoIconRegistryService {
  private icons = new Map<string, string>();

  registerIcons(icons: any[]) {
    icons.forEach(icon => {
      this.icons.set(icon.name, icon.data);
    });
  }

  getIcon(name: string) {
    return this.icons.get(name);
  }
}

const mockParticipantRanks: ParticipantRankUI[] = [
  { rank: 1, totalRegistration: 100 },
  { rank: 2, totalRegistration: 95 },
  { rank: 3, totalRegistration: 90, matched: true },
  { rank: 4, totalRegistration: 85 },
  { rank: 5, totalRegistration: 80 },
  { rank: 6, totalRegistration: 75 },
];

const setup = (args?: { ranks?: ParticipantRankUI[] | null }) => {
  // Use our custom mock implementation
  const mockIconRegistry = new MockAplazoIconRegistryService();
  const registerIconsSpy = spyOn(
    mockIconRegistry,
    'registerIcons'
  ).and.callThrough();

  TestBed.configureTestingModule({
    imports: [
      AsyncPipe,
      AplazoIconComponent,
      AplazoButtonComponent,
      ParticipantRankingComponent,
    ],
    providers: [
      {
        provide: AplazoIconRegistryService,
        useValue: mockIconRegistry,
      },
    ],
  });

  const fixture = TestBed.createComponent(ParticipantRankingComponent);
  const component = fixture.componentInstance;

  if (args?.ranks !== undefined) {
    component.list = args.ranks;
  }

  fixture.detectChanges();

  return {
    fixture,
    component,
    registerIconsSpy,
  };
};

describe('ParticipantRankingComponent', () => {
  it('should create', () => {
    const { component } = setup();
    expect(component).toBeTruthy();
  });

  it('should register icons on component creation', () => {
    const { registerIconsSpy } = setup();
    expect(registerIconsSpy).toHaveBeenCalledTimes(1);
  });

  it('should handle valid participant list input', fakeAsync(() => {
    const { component } = setup({ ranks: mockParticipantRanks });

    let viewModel: any;
    component.vm$.subscribe(vm => {
      viewModel = vm;
    });

    flush();

    expect(viewModel.list.length).toBe(3);
    expect(viewModel.hasPrevious).toBeTrue();
    expect(viewModel.hasNext).toBeTrue();

    // Default pagination should center on the matched item
    expect(viewModel.list[0].rank).toBe(2);
    expect(viewModel.list[1].rank).toBe(3); // Matched item
    expect(viewModel.list[2].rank).toBe(4);
  }));

  it('should handle null or invalid list input', fakeAsync(() => {
    const { component } = setup({ ranks: null });

    let viewModel: any;
    component.vm$.subscribe(vm => {
      viewModel = vm;
    });

    flush();

    expect(viewModel.list.length).toBe(0);
    expect(viewModel.hasPrevious).toBeFalse();
    expect(viewModel.hasNext).toBeFalse();
  }));

  it('should handle list without a matched item', fakeAsync(() => {
    // Create a list without any matched items
    const noMatchList = mockParticipantRanks.map(item => ({
      ...item,
      matched: false,
    }));
    const { component } = setup({ ranks: noMatchList });

    let viewModel: any;
    component.vm$.subscribe(vm => {
      viewModel = vm;
    });

    flush();

    expect(viewModel.list.length).toBe(2);
    expect(viewModel.list[0].rank).toBe(1);
    expect(viewModel.list[1].rank).toBe(2);
    expect(viewModel.hasPrevious).toBeFalse();
    expect(viewModel.hasNext).toBeTrue();
  }));

  it('should scroll to previous items when calling scrollList with "lower"', fakeAsync(() => {
    const { component } = setup({ ranks: mockParticipantRanks });

    let viewModel: any;
    component.vm$.subscribe(vm => {
      viewModel = vm;
    });

    flush();

    // Initial state
    expect(viewModel.list[0].rank).toBe(2);
    expect(viewModel.list[1].rank).toBe(3);
    expect(viewModel.list[2].rank).toBe(4);

    // Scroll up
    component.scrollList('lower');
    flush();

    // Should show one item up in the list
    expect(viewModel.list[0].rank).toBe(1);
    expect(viewModel.list[1].rank).toBe(2);
    expect(viewModel.list[2].rank).toBe(3);
    expect(viewModel.hasPrevious).toBeFalse(); // At the top
    expect(viewModel.hasNext).toBeTrue();
  }));

  it('should scroll to next items when calling scrollList with "upper"', fakeAsync(() => {
    const { component } = setup({ ranks: mockParticipantRanks });

    let viewModel: any;
    component.vm$.subscribe(vm => {
      viewModel = vm;
    });

    flush();

    // Initial state
    expect(viewModel.list[0].rank).toBe(2);
    expect(viewModel.list[1].rank).toBe(3);
    expect(viewModel.list[2].rank).toBe(4);

    // Scroll down
    component.scrollList('upper');
    flush();

    // Should show one item down in the list
    expect(viewModel.list[0].rank).toBe(3);
    expect(viewModel.list[1].rank).toBe(4);
    expect(viewModel.list[2].rank).toBe(5);
    expect(viewModel.hasPrevious).toBeTrue();
    expect(viewModel.hasNext).toBeTrue();
  }));

  it('should not scroll past the end of the list', fakeAsync(() => {
    const { component } = setup({ ranks: mockParticipantRanks });

    let viewModel: any;
    component.vm$.subscribe(vm => {
      viewModel = vm;
    });

    flush();

    // Scroll down multiple times to reach the end
    component.scrollList('upper');
    component.scrollList('upper');
    component.scrollList('upper');
    flush();

    // Should be at the end of the list
    expect(viewModel.list[0].rank).toBe(4);
    expect(viewModel.list[1].rank).toBe(5);
    expect(viewModel.list[2].rank).toBe(6);
    expect(viewModel.hasPrevious).toBeTrue();
    expect(viewModel.hasNext).toBeFalse(); // No more items

    // Try to scroll past the end
    component.scrollList('upper');
    flush();

    // Should still be at the same position
    expect(viewModel.list[0].rank).toBe(4);
    expect(viewModel.list[1].rank).toBe(5);
    expect(viewModel.list[2].rank).toBe(6);
  }));

  it('should handle edge case with a single item list', fakeAsync(() => {
    const singleItemList = [{ rank: 1, totalRegistration: 100, matched: true }];
    const { component } = setup({ ranks: singleItemList });

    let viewModel: any;
    component.vm$.subscribe(vm => {
      viewModel = vm;
    });

    flush();

    expect(viewModel.list.length).toBe(1);
    expect(viewModel.list[0].rank).toBe(1);
    expect(viewModel.hasPrevious).toBeFalse();
    expect(viewModel.hasNext).toBeFalse();
  }));

  it('should get correct list slice from vm$ observable', fakeAsync(() => {
    const { component } = setup({ ranks: mockParticipantRanks });

    let list: ParticipantRankUI[] = [];
    let hasPrevious = false;
    let hasNext = false;

    lastValueFrom(component.vm$.pipe(take(1))).then(vm => {
      list = vm.list;
      hasPrevious = vm.hasPrevious;
      hasNext = vm.hasNext;
    });

    flush();

    expect(list.length).toBe(3);
    expect(list[0].rank).toBe(2);
    expect(list[1].rank).toBe(3);
    expect(list[2].rank).toBe(4);
    expect(hasPrevious).toBeTrue();
    expect(hasNext).toBeTrue();
  }));
});
