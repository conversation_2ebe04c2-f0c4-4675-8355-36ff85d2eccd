import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RedirectionService } from '@aplazo/merchant/shared';
import { LocalRedirecter } from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoIconComponent } from '@aplazo/shared-ui/icon';
import {
  POS_ENVIRONMENT_CORE,
  posEnvironmentCore,
} from '../../../app-core/domain/environments';
import { SuccessPageComponent } from '../../../app/modules/success-page/success-page.component';

describe('SuccessPageComponent', () => {
  let component: SuccessPageComponent;
  let fixture: ComponentFixture<SuccessPageComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: RedirectionService,
          useClass: LocalRedirecter,
        },
        {
          provide: POS_ENVIRONMENT_CORE,
          useValue: posEnvironmentCore,
        },
      ],
      imports: [AplazoIconComponent, AplazoButtonComponent],
    });
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SuccessPageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
