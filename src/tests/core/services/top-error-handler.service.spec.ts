import { TestBed } from '@angular/core/testing';
import { ObservabilityService } from '@aplazo/front-observability';
import {
  BrowserLocalStorageService,
  BrowserReloaderService,
} from '@aplazo/merchant/shared';
import { ToastrService } from 'ngx-toastr';
import { of } from 'rxjs';
import { TopErrorHandlerService } from '../../../app/core/services/top-error-handler.service';

describe('TopErrorHandlerService', () => {
  let service: TopErrorHandlerService;
  let storageSpy: jasmine.SpyObj<BrowserLocalStorageService>;
  let reloaderSpy: jasmine.SpyObj<BrowserReloaderService>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        TopErrorHandlerService,
        {
          provide: ToastrService,
          useValue: {
            warning: () => ({ onHidden: of(void 0) }),
          },
        },
        {
          provide: BrowserLocalStorageService,
          useValue: jasmine.createSpyObj('BrowserLocalStorageService', [
            'getItem',
            'setItem',
          ]),
        },
        {
          provide: BrowserReloaderService,
          useValue: jasmine.createSpyObj('BrowserReloaderService', [
            'executeWithAPI',
            'executeWithElement',
          ]),
        },
        {
          provide: ObservabilityService,
          useValue: jasmine.createSpyObj('ObservabilityService', [
            'sendCustomLog',
          ]),
        },
      ],
    });

    service = TestBed.inject(TopErrorHandlerService);
    storageSpy = TestBed.inject(
      BrowserLocalStorageService
    ) as jasmine.SpyObj<BrowserLocalStorageService>;
    reloaderSpy = TestBed.inject(
      BrowserReloaderService
    ) as jasmine.SpyObj<BrowserReloaderService>;
  });

  beforeAll(() => {
    window.onbeforeunload = () => 'here used to be a message';
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should have a correct ToastrService instance', () => {
    expect(service.toastr)
      .withContext('ToastrService instance should be defined')
      .toBeDefined();

    expect(service.toastr.warning)
      .withContext('ToastrService.warning method should be defined')
      .toBeDefined();
  });

  it('should have a correct BrowserLocalStorageService instance', () => {
    expect(service.localStorage)
      .withContext('BrowserLocalStorageService instance should be defined')
      .toBeDefined();

    expect(service.localStorage.getItem)
      .withContext(
        'BrowserLocalStorageService.getItem method should be defined'
      )
      .toBeDefined();

    expect(service.localStorage.setItem)
      .withContext(
        'BrowserLocalStorageService.setItem method should be defined'
      )
      .toBeDefined();
  });

  it('should have a correct BrowserReloaderService instance', () => {
    expect(service.reloader)
      .withContext('BrowserReloaderService instance should be defined')
      .toBeDefined();

    expect(service.reloader.executeWithAPI)
      .withContext('BrowserReloaderService.execute method should be defined')
      .toBeDefined();

    expect(service.reloader.executeWithElement)
      .withContext(
        'BrowserReloaderService.executeWithElement method should be defined'
      )
      .toBeDefined();
  });

  it('should handle loadChunk error when errror has message without storage', () => {
    storageSpy.getItem.and.returnValue(null);

    const error = new Error('Loading chunk 123 failed.');

    expect(() => service.handleError(error))
      .withContext('handleError method should not throw an error')
      .not.toThrow();

    expect(reloaderSpy.executeWithAPI)
      .withContext('ReloaderService.execute method should be called')
      .toHaveBeenCalledTimes(1);

    expect(storageSpy.setItem)
      .withContext('BrowserLocalStorageService.setItem method should be called')
      .toHaveBeenCalledTimes(1);
  });

  it('should handle loadChunk error when error is complex and has msg without storage', () => {
    storageSpy.getItem.and.returnValue(null);

    const error = {
      msg: 'Loading chunk 123 failed.',
    };

    expect(() => service.handleError(error))
      .withContext('handleError method should not throw an error')
      .not.toThrow();

    expect(reloaderSpy.executeWithAPI)
      .withContext('ReloaderService.execute method should be called')
      .toHaveBeenCalledTimes(1);
    expect(storageSpy.setItem)
      .withContext('BrowserLocalStorageService.setItem method should be called')
      .toHaveBeenCalledTimes(1);
  });

  it('should handle loadChunk error when error is complex and has nested msg without storage', () => {
    storageSpy.getItem.and.returnValue(null);

    const error = {
      error: {
        msg: 'Loading chunk 123 failed.',
      },
    };

    expect(() => service.handleError(error))
      .withContext('handleError method should not throw an error')
      .not.toThrow();

    expect(reloaderSpy.executeWithAPI)
      .withContext('ReloaderService.executeByLocation method should be called')
      .toHaveBeenCalledTimes(1);
    expect(storageSpy.setItem)
      .withContext('BrowserLocalStorageService.setItem method should be called')
      .toHaveBeenCalledTimes(1);
  });

  it('should handle loadChunk error when error is complex and has nested message without storage', () => {
    storageSpy.getItem.and.returnValue(null);

    const error = {
      error: 'Loading chunk 123 failed.',
    };

    expect(() => service.handleError(error))
      .withContext('handleError method should not throw an error')
      .not.toThrow();

    expect(reloaderSpy.executeWithAPI)
      .withContext('ReloaderService.executeByLocation method should be called')
      .toHaveBeenCalledTimes(1);
    expect(storageSpy.setItem)
      .withContext('BrowserLocalStorageService.setItem method should be called')
      .toHaveBeenCalledTimes(1);
  });

  it('should handle loadChunk error when error is a string without storage', () => {
    storageSpy.getItem.and.returnValue(null);

    const error = 'Loading chunk 123 failed.';

    expect(() => service.handleError(error))
      .withContext('handleError method should not throw an error')
      .not.toThrow();

    expect(reloaderSpy.executeWithAPI)
      .withContext('ReloaderService.execute method should be called')
      .toHaveBeenCalledTimes(1);
    expect(storageSpy.setItem)
      .withContext('BrowserLocalStorageService.setItem method should be called')
      .toHaveBeenCalledTimes(1);
  });

  it('should handle not loadChunk error', () => {
    const error = new Error('Some error');

    expect(() => service.handleError(error))
      .withContext('handleError method should not throw an error')
      .not.toThrow();
    expect(reloaderSpy.executeWithAPI)
      .withContext('ReloaderService.execute method should not be called')
      .toHaveBeenCalledTimes(0);
    expect(storageSpy.setItem)
      .withContext('BrowserLocalStorageService.setItem method should be called')
      .toHaveBeenCalledTimes(0);
  });

  it('should handle loadChunk error whit storage', () => {
    storageSpy.getItem.and.returnValue('true');

    const error = new Error('Loading chunk 123 failed.');

    expect(() => service.handleError(error))
      .withContext('handleError method should not throw an error')
      .not.toThrow();

    expect(reloaderSpy.executeWithAPI)
      .withContext('ReloaderService.executeByLocation method should be called')
      .toHaveBeenCalledTimes(1);
  });
});
