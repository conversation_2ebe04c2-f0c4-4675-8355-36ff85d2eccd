import { Injectable } from '@angular/core';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { BehaviorSubject, map } from 'rxjs';
import { ContestRankingUI } from '../../domain/contest';

@Injectable({ providedIn: 'root' })
export class ContestStoreService {
  readonly #ranking$ = new BehaviorSubject<ContestRankingUI>({
    campaignFinishDate: null,
    data: [],
    qrUrl: null,
    hasDetails: false,
    isFutureDate: false,
    tier: null,
  });

  readonly contestRanking$ = this.#ranking$.pipe();

  readonly hasDetails$ = this.#ranking$.pipe(
    map(({ hasDetails }) => hasDetails)
  );

  setNewRanking(ranking: ContestRankingUI): void {
    if (!ranking.campaignFinishDate) {
      throw new RuntimeMerchantError(
        'La fecha de finalización de la campaña no puede ser nula',
        'ContestStoreService::setNewRanking::emptyArgument'
      );
    }

    if (!ranking.qrUrl) {
      throw new RuntimeMerchantError(
        'La URL del código QR no puede ser nula',
        'ContestStoreService::setNewRanking::emptyArgument'
      );
    }

    this.#ranking$.next(ranking);
  }

  clearRanking(): void {
    this.#ranking$.next({
      campaignFinishDate: null,
      data: [],
      qrUrl: null,
      hasDetails: false,
      isFutureDate: false,
      tier: null,
    });
  }
}
