import { <PERSON><PERSON><PERSON><PERSON><PERSON>, I18nPluralPipe, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { TemporalService } from '@aplazo/merchant/shared';
import {
  AplazoMatchMediaService,
  ValidDynamicPipesNames,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormDatepickerComponent,
  AplazoFormFieldDirectives,
  AplazoSearchInputComponent,
  searchControlFactory,
} from '@aplazo/shared-ui/forms';
import {
  AplazoCommonMessageComponents,
  AplazoStatCardComponent,
} from '@aplazo/shared-ui/merchant';
import { StatsigModule } from '@statsig/angular-bindings';
import {
  MonoTypeOperatorFunction,
  Observable,
  Subject,
  combineLatest,
  distinctUntilChanged,
  filter,
  map,
  pipe,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
  withLatestFrom,
} from 'rxjs';
import { AnalyticsService } from '../../core/application/services/analytics.service';
import { StoreService } from '../../core/application/services/store.service';
import { HandleOrderGenerationUseCase } from '../cart-v1/application/handle-order-generation.usecase';
import {
  IOrdersTableInjectedTextUI,
  OrdersTableComponent,
} from '../shared/organisms/orders-table/orders-table.component';
import { PreloanCartButtonComponent } from '../shared/preloan-cart-button.component';
import { GetHistoricalOrdersUseCase } from './application/get-historical-orders.usecase';
import { GetHistoricalReportUseCase } from './application/get-historical-report.usecase';
import { GetHistoricalSummaryUseCase } from './application/get-historical-summary.usecase';

export interface IHistoricalSummaryInjectedTextUI {
  totalOrder: string;
  totalSale: string;
  avgPrice: string;
  [key: string]: string;
}
interface ICounterI18n {
  empty: string;
  singular: string;
  plural: string;
}

@Component({
  standalone: true,
  selector: 'aplazo-historic-v1',
  imports: [
    NgIf,
    NgFor,
    AsyncPipe,
    I18nPluralPipe,
    AplazoButtonComponent,
    OrdersTableComponent,
    AplazoCardComponent,
    AplazoStatCardComponent,
    AplazoCommonMessageComponents,
    AplazoFormFieldDirectives,
    AplazoSearchInputComponent,
    ReactiveFormsModule,
    AplazoFormDatepickerComponent,
    PreloanCartButtonComponent,
    StatsigModule,
  ],
  providers: [
    GetHistoricalOrdersUseCase,
    GetHistoricalReportUseCase,
    GetHistoricalSummaryUseCase,
  ],
  templateUrl: './historical-v1.component.html',
})
export class HistoricalV1Component implements OnDestroy {
  readonly #storeService = inject(StoreService);
  readonly #getHistoricalOrdersUseCase = inject(GetHistoricalOrdersUseCase);
  readonly #getHistoricalSummaryUseCase = inject(GetHistoricalSummaryUseCase);
  readonly #getHistoricalReportUseCase = inject(GetHistoricalReportUseCase);
  readonly #i18n = inject(I18NService);
  readonly #matchMedia = inject(AplazoMatchMediaService);
  readonly #analytics = inject(AnalyticsService);
  readonly #newOrderHandler = inject(HandleOrderGenerationUseCase);
  readonly #temporal = inject(TemporalService);

  readonly #destroy$: Subject<void> = new Subject<void>();
  readonly screenSize$ = this.#matchMedia.screenSize$();

  ordersCount = 0;
  readonly #summaryContent = ['totalSale', 'totalOrder', 'avgPrice'];
  readonly todayRawDateDayFirst = this.#temporal.todayRawDayFirst;
  readonly today = this.#temporal.today;

  readonly minLength = 3;

  readonly dateControl = new FormControl<Date[] | null>(
    [this.today, this.today],
    {
      validators: [Validators.required],
    }
  );

  branchId$: Observable<number | null> =
    this.#storeService.selectedBranch$.pipe(map(branch => branch?.id || null));

  searchControl = new FormControl<string>('');
  readonly #searchControlFac = searchControlFactory({
    minLength: this.minLength,
    debouncedTime: 450,
  });

  searchValue$ = this.searchControl.valueChanges.pipe(
    startWith(this.searchControl.value),
    this.#searchControlFac.debouncedValue(),
    this.#filterSearchStream(),
    tap(value => {
      if (value?.length && value.length > 0) {
        this.#analytics.track('search', {
          searchTerm: value,
        });
      }
    }),
    takeUntil(this.#destroy$)
  );
  isActiveSearch$: Observable<boolean> = this.searchControl.valueChanges.pipe(
    this.#searchControlFac.hasActiveSearch()
  );

  newOrderButtonTextTemplate$: Observable<{ label: string }> =
    this.#i18n.getTranslateObjectByKey<{ label: string }>({
      key: 'newOrderButton',
      scope: 'historical',
    });
  dateRangeTextTemplate$ = this.#i18n.getTranslateObjectByKey<{
    tooltip: string;
    intervalError?: string;
  }>({
    key: 'daterangePicker',
    scope: 'historical',
  });
  ordersTableInjectedText$ =
    this.#i18n.getTranslateObjectByKey<IOrdersTableInjectedTextUI>({
      key: 'table',
      scope: 'historical',
    });
  emptySearchInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
  }>({
    key: 'emptySearch',
    scope: 'historical',
  });
  emptyDateRangeInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
  }>({
    key: 'emptyDaterange',
    scope: 'historical',
  });
  historicalSummaryInjectedText$ =
    this.#i18n.getTranslateObjectByKey<IHistoricalSummaryInjectedTextUI>({
      key: 'summary',
      scope: 'historical',
    });
  searchbarInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    placeholder: string;
    minlengthError: string;
    requiredError: string;
  }>({
    key: 'searchbar',
    scope: 'historical',
    params: {
      minlengthError: { minLength: this.minLength },
      requiredError: { minLength: this.minLength },
    },
  });
  couterLabelTextTemplate$ = this.#i18n
    .getTranslateObjectByKey<ICounterI18n>({
      key: 'counterLabel',
      scope: 'historical',
    })
    .pipe(
      map(text => ({
        '=0': text.empty,
        '=1': '# ' + text.singular,
        other: '# ' + text.plural,
      }))
    );
  summaryTitleText$ = this.#i18n.getTranslateObjectByKey<{ label: string }>({
    key: 'summaryTitle',
    scope: 'historical',
  });

  newOrder(): void {
    this.#analytics.track('buttonClick', {
      buttonName: 'historicalNewOrder',
    });
    this.#newOrderHandler.execute().pipe(take(1)).subscribe();
  }

  downloadReport(): void {
    this.#getHistoricalReportUseCase
      .execute({
        startDate: this.dateControl.value![0],
        endDate: this.dateControl.value![1],
      })
      .pipe(
        take(1),
        tap(() => {
          this.#analytics.track('buttonClick', {
            buttonName: 'historicalDownloadReport',
          });
        })
      )
      .subscribe();
  }

  orders$ = combineLatest([
    this.dateControl.valueChanges.pipe(
      startWith(this.dateControl.value),
      filter(Boolean)
    ),
    this.branchId$.pipe(distinctUntilChanged()),
    this.searchValue$,
  ]).pipe(
    switchMap(([dateRange, branchId, searchValue]) =>
      this.#getHistoricalOrdersUseCase
        .execute({ startDate: dateRange[0], endDate: dateRange[1] })
        .pipe(
          map(response => {
            if (searchValue) {
              return response
                .filter(branch => branch.branchId === branchId)
                .filter(
                  branch =>
                    ('' + branch.loanId).includes(searchValue) ||
                    ('' + branch.sellsAgentId).includes(searchValue)
                );
            }

            return response.filter(branch => branch.branchId === branchId);
          })
        )
    ),
    tap(orders => {
      this.ordersCount = orders.length;
    }),
    takeUntil(this.#destroy$)
  );

  summary$ = combineLatest([
    this.dateControl.valueChanges.pipe(
      startWith(this.dateControl.value),
      filter(Boolean)
    ),
    this.searchValue$,
  ]).pipe(
    switchMap(([dateRange, searchValue]) => {
      if (searchValue) {
        return this.orders$.pipe(
          map(orders => {
            if (!orders || orders.length === 0) {
              return {
                totalOrder: 0,
                totalSale: 0,
                avgPrice: 0,
              };
            }

            const totalOrder = orders.length;
            const totalSale = orders.reduce(
              (acc, order) => acc + order.price,
              0
            );
            const avgPrice = totalSale / totalOrder;

            return {
              totalOrder,
              totalSale,
              avgPrice,
            };
          })
        );
      }

      return this.#getHistoricalSummaryUseCase.execute({
        startDate: dateRange[0],
        endDate: dateRange[1],
      });
    }),
    withLatestFrom(this.historicalSummaryInjectedText$),
    map(([summary, injectionText]) =>
      this.#summaryContent.map(item => {
        const pipeName: ValidDynamicPipesNames =
          item === 'totalOrder' ? 'decimal' : 'currency';
        return {
          isDarkMode: true,
          statCardTitle: injectionText[item],
          value: String(summary[item as keyof typeof summary]),
          pipeName,
        };
      })
    ),
    takeUntil(this.#destroy$)
  );

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  #filterSearchStream(): MonoTypeOperatorFunction<string> {
    return pipe(
      filter(
        searchValue =>
          !searchValue ||
          searchValue.length === 0 ||
          searchValue.length >= this.minLength
      )
    );
  }
}
