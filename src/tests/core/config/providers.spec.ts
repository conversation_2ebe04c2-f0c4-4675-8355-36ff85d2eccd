import { TestBed } from '@angular/core/testing';
import { TagManagerService } from '@aplazo/front-analytics/tag-manager';
import { AnalyticsService } from '../../../app/core/application/services/analytics.service';
import { StoreService } from '../../../app/core/application/services/store.service';
import { provideAnalyticsService } from '../../../app/core/config/providers';
import { AnalyticsWithGtmService } from '../../../app/core/services/analytics-with-gtm.service';
import { LocalStore } from '../services/impl/local-store.service';

describe('App Providers', () => {
  describe('AnalyticsService', () => {
    it('should be the correct instance', () => {
      TestBed.configureTestingModule({
        providers: [
          provideAnalyticsService(),
          { provide: StoreService, useClass: LocalStore },
          {
            provide: TagManagerService,
            useValue: { trackEvent: () => void 0 },
          },
        ],
      });

      const service = TestBed.inject(AnalyticsService);

      expect(service).toBeTruthy();
      expect(service).toBeInstanceOf(AnalyticsWithGtmService);
    });
  });
});
