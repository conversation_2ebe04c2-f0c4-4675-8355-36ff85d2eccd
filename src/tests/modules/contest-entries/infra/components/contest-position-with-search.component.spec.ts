import { CommonModule } from '@angular/common';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  flush,
  tick,
} from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { LoaderService, RuntimeMerchantError } from '@aplazo/merchant/shared';
import { BehaviorSubject, of, throwError } from 'rxjs';
import { AnalyticsService } from '../../../../../app/core/application/services/analytics.service';
import { GetOneParticipantWithDetailsUseCase } from '../../../../../app/modules/contest-entries/application/usecases/get-one-with-details.usecase';
import { ContestRankingUI } from '../../../../../app/modules/contest-entries/domain/contest';
import { AplazoContestPositionWithSearchComponent } from '../../../../../app/modules/contest-entries/infra/components/contest-position-with-search/contest-position-with-search.component';
import { ContestStoreService } from '../../../../../app/modules/contest-entries/infra/services/contest-store.service';

const mockRankingData: ContestRankingUI = {
  campaignFinishDate: '2023-12-31',
  data: [
    { rank: 1, totalRegistration: 10, matched: true },
    { rank: 2, totalRegistration: 8, matched: false },
  ],
  qrUrl: 'http://test.qr',
  hasDetails: true,
  isFutureDate: true,
  tier: null,
};

describe('AplazoContestPositionWithSearchComponent', () => {
  let component: AplazoContestPositionWithSearchComponent;
  let fixture: ComponentFixture<AplazoContestPositionWithSearchComponent>;
  let mockGetOneUseCase: jasmine.SpyObj<GetOneParticipantWithDetailsUseCase>;
  let mockStoreService: jasmine.SpyObj<ContestStoreService>;
  let mockLoaderService: jasmine.SpyObj<LoaderService>;
  let mockAnalyticsService: jasmine.SpyObj<AnalyticsService>;
  let hasDetails$: BehaviorSubject<boolean>;
  let isLoading$: BehaviorSubject<boolean>;

  beforeEach(() => {
    hasDetails$ = new BehaviorSubject<boolean>(false);
    isLoading$ = new BehaviorSubject<boolean>(false);

    mockGetOneUseCase = jasmine.createSpyObj(
      'GetOneParticipantWithDetailsUseCase',
      ['execute']
    );
    mockGetOneUseCase.execute.and.returnValue(of(mockRankingData));

    mockStoreService = jasmine.createSpyObj('ContestStoreService', [
      'setNewRanking',
      'clearRanking',
    ]);
    Object.defineProperty(mockStoreService, 'hasDetails$', {
      get: () => hasDetails$.asObservable(),
    });

    mockLoaderService = jasmine.createSpyObj('LoaderService', ['show', 'hide']);
    Object.defineProperty(mockLoaderService, 'isLoading$', {
      get: () => isLoading$.asObservable(),
    });

    mockAnalyticsService = jasmine.createSpyObj('AnalyticsService', ['track']);

    TestBed.configureTestingModule({
      imports: [
        CommonModule,
        ReactiveFormsModule,
        AplazoContestPositionWithSearchComponent,
      ],
      providers: [
        {
          provide: GetOneParticipantWithDetailsUseCase,
          useValue: mockGetOneUseCase,
        },
        { provide: ContestStoreService, useValue: mockStoreService },
        { provide: LoaderService, useValue: mockLoaderService },
        { provide: AnalyticsService, useValue: mockAnalyticsService },
      ],
      schemas: [NO_ERRORS_SCHEMA], // This ignores unknown elements and attributes
    });

    fixture = TestBed.createComponent(AplazoContestPositionWithSearchComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form with empty search control', () => {
    expect(component.search.value).toBe('');
    expect(component.form.valid).toBeFalse();
  });

  describe('search validation', () => {
    it('should mark search as invalid when empty', () => {
      component.search.setValue('');
      expect(component.search.valid).toBeFalse();
      expect(component.search.errors?.['required']).toBeTruthy();
    });

    it('should mark search as invalid when less than minLength', () => {
      component.search.setValue('12');
      expect(component.search.valid).toBeFalse();
      expect(component.search.errors?.['minlength']).toBeTruthy();
    });

    it('should mark search as valid when length >= minLength', () => {
      component.search.setValue('123');
      expect(component.search.valid).toBeTrue();
    });
  });

  describe('retrieveParticipantInfo', () => {
    it('should not call getOneUseCase when form is invalid', fakeAsync(() => {
      component.search.setValue('');
      component.retrieveParticipantInfo();
      tick();

      expect(mockGetOneUseCase.execute).not.toHaveBeenCalled();
    }));

    it('should not call getOneUseCase when search is less than minLength', fakeAsync(() => {
      component.search.setValue('12');
      component.retrieveParticipantInfo();
      tick();

      expect(mockGetOneUseCase.execute).not.toHaveBeenCalled();
    }));

    it('should call getOneUseCase when form is valid', fakeAsync(() => {
      component.search.setValue('123');
      component.retrieveParticipantInfo();
      tick();

      expect(mockGetOneUseCase.execute).toHaveBeenCalledTimes(1);
      expect(mockGetOneUseCase.execute).toHaveBeenCalledWith('123');
    }));

    it('should call analytics.track when search is performed', fakeAsync(() => {
      component.search.setValue('123');
      component.retrieveParticipantInfo();
      tick();

      expect(mockAnalyticsService.track).toHaveBeenCalledTimes(1);
      expect(mockAnalyticsService.track).toHaveBeenCalledWith('search', {
        category: 'premios_contest',
        timestamp: jasmine.any(Number),
        label: 'searchPremiosParticipant',
        searchTerm: '123',
      });
    }));

    it('should update store with ranking data when API returns success', fakeAsync(() => {
      component.search.setValue('123');
      component.retrieveParticipantInfo();
      tick();

      expect(mockStoreService.setNewRanking).toHaveBeenCalledTimes(1);
      expect(mockStoreService.setNewRanking).toHaveBeenCalledWith(
        mockRankingData
      );
    }));

    it('should handle participantNotFound error', fakeAsync(() => {
      const error = new RuntimeMerchantError(
        'El código del participante no existe',
        'GetOneWithDetailsUseCase::participantNotFound'
      );

      mockGetOneUseCase.execute.and.returnValue(throwError(() => error));

      component.search.setValue('123');
      component.retrieveParticipantInfo();
      tick();

      expect(
        component.search.errors?.[component.participantErrorKey]
      ).toBeTruthy();
    }));

    it('should not update store when API returns an error', fakeAsync(() => {
      const error = new RuntimeMerchantError(
        'El código del participante no existe',
        'GetOneWithDetailsUseCase::participantNotFound'
      );

      mockGetOneUseCase.execute.and.returnValue(throwError(() => error));

      component.search.setValue('123');
      component.retrieveParticipantInfo();
      tick();

      expect(mockStoreService.setNewRanking).not.toHaveBeenCalled();
    }));
  });

  describe('clear', () => {
    it('should reset form and clear store ranking', () => {
      component.search.setValue('123');
      component.clear();

      expect(component.search.value).toBe('');
      expect(mockStoreService.clearRanking).toHaveBeenCalledTimes(1);
    });
  });

  describe('ngOnDestroy', () => {
    it('should call clear method when component is destroyed', () => {
      spyOn(component, 'clear');
      component.ngOnDestroy();

      expect(component.clear).toHaveBeenCalledTimes(1);
    });
  });

  describe('reactive streams', () => {
    it('should filter and trim search values', fakeAsync(() => {
      let searchValue = '';

      component.search$.subscribe(value => {
        searchValue = value;
      });

      component.search.setValue('  test  ');
      flush();

      expect(searchValue).toBe('test');
    }));

    it('should update isActiveSearch$ when search length changes', fakeAsync(() => {
      let isActiveSearch = false;

      component.isActiveSearch$.subscribe(value => {
        isActiveSearch = value;
      });

      component.search.setValue('12');
      flush();
      expect(isActiveSearch).toBeFalse();

      component.search.setValue('123');
      flush();
      expect(isActiveSearch).toBeTrue();
    }));

    it('should combine hasDetails and isLoading in vm$', fakeAsync(() => {
      hasDetails$.next(true);
      isLoading$.next(true);

      let vm: any;
      component.vm$.subscribe(value => {
        vm = value;
      });

      flush();

      expect(vm.hasDetails).toBeTrue();
      expect(vm.isLoading).toBeTrue();
    }));
  });
});
