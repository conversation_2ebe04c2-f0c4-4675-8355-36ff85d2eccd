import { <PERSON><PERSON><PERSON> } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { SentWhatsappLinkFormComponent } from '../../../app/modules/share-payment-link/sent-whatsapp-link-form/sent-whatsapp-link-form.component';

describe('SentWhatsappLinkFormComponent', () => {
  let component: SentWhatsappLinkFormComponent;
  let fixture: ComponentFixture<SentWhatsappLinkFormComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        NgFor,
        AplazoFormFieldDirectives,
        ReactiveFormsModule,
        AplazoButtonComponent,
        NgxMaskDirective,
      ],
      providers: [provideNgxMask()],
    });
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(SentWhatsappLinkFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
