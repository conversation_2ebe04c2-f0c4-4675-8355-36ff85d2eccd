import { Injectable, inject } from '@angular/core';
import { DialogService } from '@ngneat/dialog';
import { switchMap, take } from 'rxjs';
import { StoreService } from '../../core/application/services/store.service';
import { Order } from '../../core/domain/order.interface';
import { RuntimePosOfflineError } from '../../core/domain/runtime-error';
import { ShowPaymentLinkDialogService } from './application/services/show-payment-link-dialog.service';
import { SharePaymentLinkComponent } from './share-payment-link.component';

@Injectable({ providedIn: 'root' })
export class PaymentLinkWithNgneatDialogService
  implements ShowPaymentLinkDialogService
{
  readonly #storeService = inject(StoreService);
  readonly #dialog = inject(DialogService);

  execute(loan: Order): void {
    this.#storeService.selectedBranch$
      .pipe(
        switchMap(branch => {
          if (!branch) {
            throw new RuntimePosOfflineError(
              'Diálogo de métodos de pago, no hay sucursal seleccionada. Intente cerrar sesión y volver a iniciarla',
              'PaymentLinkDialog::EmptyBranch',
              'payment-link-dialog-error'
            );
          }

          return this.#dialog.open(SharePaymentLinkComponent, {
            data: {
              branch,
              loan,
            },
            maxWidth: '448px',
            enableClose: false,
          }).afterClosed$;
        }),

        take(1)
      )
      .subscribe();
  }
}
