import { Component, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By, DomSanitizer } from '@angular/platform-browser';
import { RedirectionService } from '@aplazo/merchant/shared';
import { provideRedirecterTesting } from '@aplazo/merchant/shared-testing';
import { AnalyticsService } from 'src/app/core/application/services/analytics.service';
import { DynamicBannerContentUI } from 'src/app/modules/dynamic-banners/domain/entities/banner';
import { DynamicBannerComponent } from '../../../../../app/modules/dynamic-banners/infra/components/dynamic-banner.component';

const content: DynamicBannerContentUI = {
  id: 1,
  title: 'Test Banner',
  description: 'Test Message',
  imageUrl: 'https://example.com/image.png',
  ctaText: 'Click Me',
  ctaUrl: 'https://example.com',
  isInternalRedirect: false,
};

@Component({
  standalone: true,
  template: `
    <aplazo-dynamic-banner [content]="content"></aplazo-dynamic-banner>
  `,
  imports: [DynamicBannerComponent],
})
class TestComponent {
  content = content;
}

describe('DynamicBannerComponent', () => {
  let fixture: ComponentFixture<TestComponent>;
  let component: DebugElement;
  let analyticsSpy: jasmine.SpyObj<AnalyticsService>;
  let internalNavigationSpy: jasmine.Spy;
  let externalNavigationSpy: jasmine.Spy;

  beforeEach(() => {
    const analyticsServiceSpy = jasmine.createSpyObj('AnalyticsService', [
      'track',
    ]);

    TestBed.configureTestingModule({
      providers: [
        {
          provide: DomSanitizer,
          useValue: {
            sanitize: () => content.ctaUrl,
          },
        },
        {
          provide: AnalyticsService,
          useValue: analyticsServiceSpy,
        },
        provideRedirecterTesting(),
      ],
    });

    fixture = TestBed.createComponent(TestComponent);

    component = fixture.debugElement.query(
      By.directive(DynamicBannerComponent)
    );

    fixture.detectChanges();

    analyticsSpy = TestBed.inject(
      AnalyticsService
    ) as jasmine.SpyObj<AnalyticsService>;
    const redirecter = TestBed.inject(RedirectionService);

    internalNavigationSpy = spyOn(
      redirecter,
      'internalNavigation'
    ).and.callThrough();
    externalNavigationSpy = spyOn(
      redirecter,
      'externalNavigation'
    ).and.callThrough();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should call the analytics service when the button is clicked', () => {
    component.componentInstance.goToCta();

    expect(analyticsSpy.track).toHaveBeenCalledWith('buttonClick', {
      buttonName: 'posui_buttonbanner_click',
      genericInfo: 'https://example.com',
      label: 'Click Me',
      loanId: 1,
      timestamp: jasmine.any(Number),
    });

    expect(internalNavigationSpy).not.toHaveBeenCalled();
    expect(externalNavigationSpy).toHaveBeenCalledWith(
      content.ctaUrl,
      '_blank'
    );
  });
});
