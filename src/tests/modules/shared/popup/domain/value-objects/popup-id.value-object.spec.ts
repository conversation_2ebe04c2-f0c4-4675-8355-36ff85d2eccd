import { PopupId } from 'src/app/modules/shared/popup/domain/value-objects/popup-id.value-object';

describe('PopupId', () => {
  describe('constructor', () => {
    it('should create PopupId with valid string value', () => {
      const id = new PopupId('test-id-123');
      expect(id.getValue()).toBe('test-id-123');
    });

    it('should throw error when creating PopupId with empty string', () => {
      expect(() => new PopupId('')).toThrowError(
        'Invalid PopupId: ID must be a non-empty string'
      );
    });

    it('should throw error when creating PopupId with whitespace only', () => {
      expect(() => new PopupId('   ')).toThrowError(
        'Invalid PopupId: ID must be a non-empty string'
      );
    });

    it('should throw error when creating PopupId with null', () => {
      expect(() => new PopupId(null as any)).toThrowError(
        'Invalid PopupId: ID must be a non-empty string'
      );
    });

    it('should throw error when creating PopupId with undefined', () => {
      expect(() => new PopupId(undefined as any)).toThrowError(
        'Invalid PopupId: ID must be a non-empty string'
      );
    });
  });

  describe('getValue', () => {
    it('should return the string value', () => {
      const id = new PopupId('test-id');
      expect(id.getValue()).toBe('test-id');
    });
  });

  describe('equals', () => {
    it('should return true when comparing identical PopupIds', () => {
      const id1 = new PopupId('test-id');
      const id2 = new PopupId('test-id');
      expect(id1.equals(id2)).toBe(true);
    });

    it('should return false when comparing different PopupIds', () => {
      const id1 = new PopupId('test-id-1');
      const id2 = new PopupId('test-id-2');
      expect(id1.equals(id2)).toBe(false);
    });
  });

  describe('toString', () => {
    it('should return the string representation', () => {
      const id = new PopupId('test-id');
      expect(id.toString()).toBe('test-id');
    });
  });

  describe('fromString', () => {
    it('should create PopupId from string', () => {
      const id = PopupId.fromString('test-id');
      expect(id.getValue()).toBe('test-id');
    });

    it('should throw error when creating from invalid string', () => {
      expect(() => PopupId.fromString('')).toThrowError(
        'Invalid PopupId: ID must be a non-empty string'
      );
    });
  });

  describe('random', () => {
    it('should create PopupId with random UUID', () => {
      const id = PopupId.random();
      expect(id.getValue()).toMatch(
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/
      );
    });

    it('should create different PopupIds on multiple calls', () => {
      const id1 = PopupId.random();
      const id2 = PopupId.random();
      expect(id1.equals(id2)).toBe(false);
    });
  });
});
