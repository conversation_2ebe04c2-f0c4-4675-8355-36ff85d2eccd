import { NgIf } from '@angular/common';
import {
  Component,
  HostBinding,
  Injector,
  Input,
  OnDestroy,
  OnInit,
  inject,
} from '@angular/core';
import {
  AbstractControl,
  FormControl,
  NgControl,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import {
  AplazoFormFieldDirectives,
  NoopValueAccesorDirective,
} from '@aplazo/shared-ui/forms';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { Subject, takeUntil, tap } from 'rxjs';
import { AnalyticsService } from '../../core/application/services/analytics.service';

export interface IAgentIdTextTemplateUI {
  inputLabel: string;
  inputPlaceholder: string;
  checkboxLabel: string;
  errorRequired: string;
  errorInvalidId: string;
}

export function validateAgentId(): ValidatorFn {
  return (control: AbstractControl): ValidationErrors | null => {
    if (isNaN(+control.value)) {
      return { invalidAgentId: 'El id de agente debe ser un número' };
    }

    if (+control.value <= 0) {
      return { invalidAgentId: 'El id de agente debe ser mayor a 0' };
    }

    if (+control.value >= 10000000) {
      return { invalidAgentId: 'El id de agente debe ser menor a 10,000,000' };
    }

    return null;
  };
}

@Component({
  standalone: true,
  selector: 'aplazo-agent-id',
  imports: [
    ReactiveFormsModule,
    NgxMaskDirective,
    NgIf,
    AplazoFormFieldDirectives,
  ],
  hostDirectives: [NoopValueAccesorDirective],
  providers: [provideNgxMask()],
  template: `
    <aplz-ui-form-field [hideRequiredMarker]="inputControl.disabled">
      <aplz-ui-form-label>
        {{ injectedText.inputLabel }}
      </aplz-ui-form-label>
      <input
        aplzFormInput
        [formControl]="inputControl"
        [placeholder]="injectedText.inputPlaceholder"
        mask="separator.0"
        [validation]="true"
        inputmode="numeric" />

      <p *ngIf="hasInputFormRequiredError" aplzFormError>
        {{ injectedText.errorRequired }}
      </p>
      <p
        *ngIf="!hasInputFormRequiredError && hasInvalidAgentIdError"
        aplzFormError>
        {{ injectedText.errorInvalidId }}
      </p>
    </aplz-ui-form-field>

    <div class="flex items-center gap-x-2">
      <input
        id="agent-id"
        type="checkbox"
        class="w-4 h-4 text-aplazo-aplazo bg-light border-dark-background rounded focus:ring-aplazo-aplazo focus:ring-2"
        [formControl]="checkboxControl" />
      <label for="agent-id">
        {{ injectedText.checkboxLabel }}
      </label>
    </div>
  `,
})
export class AgentIdComponent implements OnInit, OnDestroy {
  readonly #analyticsService = inject(AnalyticsService);

  #injector = inject(Injector);
  #destroy$ = new Subject<void>();

  readonly #maxAgenId = 10000000;

  get ngControl(): NgControl {
    return this.#injector.get(NgControl);
  }

  checkboxControl = new FormControl<boolean>(false);
  inputControl = new FormControl<string>('');

  value: string | null = null;

  @HostBinding('class')
  class = 'inline-flex flex-col';

  @Input()
  injectedText: IAgentIdTextTemplateUI;

  get hasInputFormRequiredError(): boolean {
    return (
      (this.inputControl.hasError('required') && this.inputControl.touched) ||
      false
    );
  }

  get hasInvalidAgentIdError(): boolean {
    return (
      (this.inputControl.hasError('invalidAgentId') &&
        this.inputControl.touched) ||
      false
    );
  }

  ngOnInit(): void {
    this.inputControl.setValidators([Validators.required, validateAgentId()]);

    this.inputControl.updateValueAndValidity();

    this.checkboxControl.valueChanges
      .pipe(
        tap(isChecked => {
          this.inputControl.reset();
          this.ngControl.control?.reset();
          if (isChecked) {
            this.inputControl.disable();
            this.ngControl.control?.disable();
            this.#analyticsService.track('buttonClick', {
              buttonName: 'cartDisableSellAgent',
            });
            return;
          }

          this.inputControl.setValue('');
          this.inputControl.updateValueAndValidity();
          this.inputControl.enable();
          this.ngControl.control?.enable();
          this.#analyticsService.track('buttonClick', {
            buttonName: 'cartEnableSellAgent',
          });
        }),
        takeUntil(this.#destroy$)
      )
      .subscribe();

    this.inputControl.valueChanges
      .pipe(
        tap(value => {
          this.value = value;
          this.ngControl.control?.setValue(value);
          this.ngControl.valueAccessor?.registerOnChange(value);

          if (
            value &&
            !isNaN(+value) &&
            (parseInt(value) >= this.#maxAgenId || String(value).length > 8)
          ) {
            this.ngControl.control?.setErrors({
              ...this.ngControl.errors,
              invalidAgentId: true,
            });
          } else {
            const hasErrors =
              this.ngControl.errors != null &&
              Object.keys(this.ngControl.errors).length > 0;

            this.ngControl.control?.setErrors(
              hasErrors ? { ...this.ngControl.errors } : null
            );
          }
        }),
        takeUntil(this.#destroy$)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
