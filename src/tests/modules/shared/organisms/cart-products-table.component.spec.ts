import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AplazoDynamicPipe, AplazoMatchMediaService } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoDropdownComponents } from '@aplazo/shared-ui/dropdown';
import { AplazoIconComponent } from '@aplazo/shared-ui/icon';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';
import { CartProductsTableComponent } from '../../../../app/modules/shared/organisms/cart-products-table/cart-products-table.component';

describe('CartProductsTableComponent', () => {
  let component: CartProductsTableComponent;
  let fixture: ComponentFixture<CartProductsTableComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        AplazoSimpleTableComponents,
        AplazoIconComponent,
        AplazoButtonComponent,
        AplazoDropdownComponents,
        AplazoTooltipDirective,
        AplazoCardComponent,
        NgIf,
        NgFor,
        NgClass,
        AsyncPipe,
        AplazoDynamicPipe,
      ],
      providers: [AplazoMatchMediaService],
    });
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CartProductsTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
