import { Injectable } from '@angular/core';

interface PopupShownRecord {
  popupId: string;
  branchId: string;
  date: string;
  timestamp: number;
  permanentDismiss?: boolean; // ✅ NEW: Flag for permanent dismiss
}

/**
 * Servicio para gestionar el estado de popups mostrados.
 * Almacena información sobre qué popups han sido mostrados por branch y fecha.
 */
@Injectable({ providedIn: 'root' })
export class PopupStorageService {
  private readonly STORAGE_KEY = 'aplazo_popups_shown';

  /**
   * Verifica si un popup fue mostrado hoy para una branch específica.
   * @param popupId - ID del popup
   * @param branchId - ID de la branch
   * @param date - Fecha en formato string (YYYY-MM-DD)
   * @returns True si el popup fue mostrado hoy
   */
  wasPopupShownToday(popupId: string, branchId: string, date: string): boolean {
    const records = this.getShownRecordsInternal();
    return records.some(
      record =>
        record.popupId === popupId &&
        record.branchId === branchId &&
        record.date === date
    );
  }

  /**
   * Marca un popup como mostrado para una branch y fecha específica.
   * @param popupId - ID del popup
   * @param branchId - ID de la branch
   * @param date - Fecha en formato string (YYYY-MM-DD)
   */
  markPopupAsShown(popupId: string, branchId: string, date: string): void {
    const records = this.getShownRecordsInternal();

    // Evitar duplicados
    const exists = records.some(
      record =>
        record.popupId === popupId &&
        record.branchId === branchId &&
        record.date === date
    );

    if (!exists) {
      const newRecord: PopupShownRecord = {
        popupId,
        branchId,
        date,
        timestamp: Date.now(),
      };

      records.push(newRecord);
      this.saveShownRecords(records);
    }
  }

  /**
   * Limpia registros antiguos (más de 30 días).
   */
  cleanupOldRecords(): void {
    const records = this.getShownRecordsInternal();
    const thirtyDaysAgo = Date.now() - 30 * 24 * 60 * 60 * 1000;

    const filteredRecords = records.filter(
      record => record.timestamp > thirtyDaysAgo
    );

    if (filteredRecords.length !== records.length) {
      this.saveShownRecords(filteredRecords);
    }
  }

  /**
   * Obtiene todos los registros de popups mostrados.
   * @returns Array de registros
   */
  private getShownRecordsInternal(): PopupShownRecord[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('Error reading popup storage:', error);
      return [];
    }
  }

  /**
   * Obtiene todos los registros de popups mostrados (método público para demo).
   * @returns Array de registros
   */
  getShownRecords(): PopupShownRecord[] {
    return this.getShownRecordsInternal();
  }

  /**
   * Guarda los registros de popups mostrados.
   * @param records - Array de registros a guardar
   */
  private saveShownRecords(records: PopupShownRecord[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(records));
    } catch (error) {
      console.warn('Error saving popup storage:', error);
    }
  }

  /**
   * Limpia todos los registros (útil para testing).
   */
  clearAllRecords(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }

  /**
   * Marca un popup como permanentemente descartado para una branch específica.
   * @param popupId - ID del popup
   * @param branchId - ID de la branch
   */
  markPopupAsPermanentlyDismissed(popupId: string, branchId: string): void {
    if (!popupId || !branchId) {
      console.warn(
        '❌ [PopupStorageService] Invalid parameters for permanent dismiss:',
        { popupId, branchId }
      );
      return;
    }

    const records = this.getShownRecordsInternal();
    const today = new Date().toISOString().split('T')[0];

    // Buscar registro existente para este popup y branch
    const existingRecordIndex = records.findIndex(
      record => record.popupId === popupId && record.branchId === branchId
    );

    if (existingRecordIndex >= 0) {
      // Actualizar registro existente
      records[existingRecordIndex].permanentDismiss = true;
    } else {
      // Crear nuevo registro con permanent dismiss
      const newRecord: PopupShownRecord = {
        popupId,
        branchId,
        date: today,
        timestamp: Date.now(),
        permanentDismiss: true,
      };
      records.push(newRecord);
    }

    this.saveShownRecords(records);
  }

  /**
   * Verifica si un popup está permanentemente descartado para una branch específica.
   * @param popupId - ID del popup
   * @param branchId - ID de la branch
   * @returns True si el popup está permanentemente descartado
   */
  isPopupPermanentlyDismissed(popupId: string, branchId: string): boolean {
    if (!popupId || !branchId) {
      return false;
    }

    try {
      const records = this.getShownRecordsInternal();
      return records.some(
        record =>
          record.popupId === popupId &&
          record.branchId === branchId &&
          record.permanentDismiss === true
      );
    } catch (error) {
      console.warn(
        '❌ [PopupStorageService] Error checking permanent dismiss:',
        error
      );
      return false;
    }
  }

  /**
   * Limpia el estado de permanent dismiss para un popup específico.
   * @param popupId - ID del popup
   * @param branchId - ID de la branch
   */
  clearPermanentDismiss(popupId: string, branchId: string): void {
    if (!popupId || !branchId) {
      return;
    }

    const records = this.getShownRecordsInternal();
    const updatedRecords = records.map(record => {
      if (record.popupId === popupId && record.branchId === branchId) {
        return { ...record, permanentDismiss: false };
      }
      return record;
    });

    this.saveShownRecords(updatedRecords);
  }

  /**
   * Verifica si un popup debe mostrarse considerando tanto el límite diario como el permanent dismiss.
   * @param popupId - ID del popup
   * @param branchId - ID de la branch
   * @returns True si el popup debe mostrarse
   */
  shouldShowPopup(popupId: string, branchId: string): boolean {
    if (!popupId || !branchId) {
      return false;
    }

    // Check permanent dismiss first
    if (this.isPopupPermanentlyDismissed(popupId, branchId)) {
      return false;
    }

    // Check daily limit
    const today = new Date().toISOString().split('T')[0];
    const shownToday = this.wasPopupShownToday(popupId, branchId, today);

    if (shownToday) {
      return false;
    }

    return true;
  }

  /**
   * Limpia registros para una fecha específica (útil para testing).
   * @param date - Fecha en formato YYYY-MM-DD
   */
  clearRecordsForDate(date: string): void {
    const records = this.getShownRecords();
    const filteredRecords = records.filter(record => record.date !== date);
    this.saveShownRecords(filteredRecords);
  }
}
