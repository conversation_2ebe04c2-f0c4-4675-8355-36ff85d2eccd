import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { GetNotificationsByTypeUsecase } from 'src/app/core/application/get-notifications.usecase';
import { GetTrainingContentMediaUsecase } from 'src/app/core/application/get-training-media-content.usecase';
import {
  INotificationsWithNewOnesUIDto,
  ITrainingMediaContentUIDto,
  MappedMediaContentTags,
} from 'src/app/core/domain/notification';
import { NotificationsHandlerService } from 'src/app/core/services/notifications-handler.service';

describe('NotificationsHandlerService', () => {
  let service: NotificationsHandlerService;
  let getNotificationsMock: jasmine.SpyObj<GetNotificationsByTypeUsecase>;
  let getTrainingContentMock: jasmine.SpyObj<GetTrainingContentMediaUsecase>;

  beforeEach(() => {
    const getNotificationsSpy = jasmine.createSpyObj(
      'GetNotificationsByTypeUsecase',
      ['execute']
    );
    const getTrainingContentSpy = jasmine.createSpyObj(
      'GetTrainingContentMediaUsecase',
      ['execute']
    );

    TestBed.configureTestingModule({
      providers: [
        NotificationsHandlerService,
        {
          provide: GetNotificationsByTypeUsecase,
          useValue: getNotificationsSpy,
        },
        {
          provide: GetTrainingContentMediaUsecase,
          useValue: getTrainingContentSpy,
        },
      ],
    });

    service = TestBed.inject(NotificationsHandlerService);
    getNotificationsMock = TestBed.inject(
      GetNotificationsByTypeUsecase
    ) as jasmine.SpyObj<GetNotificationsByTypeUsecase>;
    getTrainingContentMock = TestBed.inject(
      GetTrainingContentMediaUsecase
    ) as jasmine.SpyObj<GetTrainingContentMediaUsecase>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(NotificationsHandlerService);
  });

  describe('getNotifications$', () => {
    it('should combine notifications from all types', done => {
      // Mock data
      const promoNotifications: INotificationsWithNewOnesUIDto = {
        type: 'PROMO',
        hasNewOnes: true,
        newOnesCounter: 2,
        newOnesIds: [1, 2],
        notifications: new Map(),
      };

      const featureNotifications: INotificationsWithNewOnesUIDto = {
        type: 'FEATURE',
        hasNewOnes: false,
        newOnesCounter: 0,
        newOnesIds: [],
        notifications: new Map(),
      };

      const infoNotifications: INotificationsWithNewOnesUIDto = {
        type: 'INFO',
        hasNewOnes: true,
        newOnesCounter: 1,
        newOnesIds: [3],
        notifications: new Map(),
      };

      // Setup mocks
      getNotificationsMock.execute
        .withArgs('PROMO')
        .and.returnValue(of(promoNotifications));
      getNotificationsMock.execute
        .withArgs('FEATURE')
        .and.returnValue(of(featureNotifications));
      getNotificationsMock.execute
        .withArgs('INFO')
        .and.returnValue(of(infoNotifications));

      // Call the service
      service.getNotifications$().subscribe({
        next: result => {
          // Verify the result
          expect(result).toBeTruthy();
          expect(result.length).toBe(3);
          expect(result).toContain(promoNotifications);
          expect(result).toContain(featureNotifications);
          expect(result).toContain(infoNotifications);

          // Verify the mocks were called
          expect(getNotificationsMock.execute).toHaveBeenCalledWith('PROMO');
          expect(getNotificationsMock.execute).toHaveBeenCalledWith('FEATURE');
          expect(getNotificationsMock.execute).toHaveBeenCalledWith('INFO');
          expect(getNotificationsMock.execute).toHaveBeenCalledTimes(3);

          done();
        },
        error: fail,
      });
    });

    it('should filter out null notifications', done => {
      // Mock data
      const promoNotifications: INotificationsWithNewOnesUIDto = {
        type: 'PROMO',
        hasNewOnes: true,
        newOnesCounter: 2,
        newOnesIds: [1, 2],
        notifications: new Map(),
      };

      // Setup mocks
      getNotificationsMock.execute
        .withArgs('PROMO')
        .and.returnValue(of(promoNotifications));
      getNotificationsMock.execute
        .withArgs('FEATURE')
        .and.returnValue(of(null));
      getNotificationsMock.execute.withArgs('INFO').and.returnValue(of(null));

      // Call the service
      service.getNotifications$().subscribe({
        next: result => {
          // Verify the result
          expect(result).toBeTruthy();
          expect(result.length).toBe(1);
          expect(result).toContain(promoNotifications);

          // Verify the mocks were called
          expect(getNotificationsMock.execute).toHaveBeenCalledWith('PROMO');
          expect(getNotificationsMock.execute).toHaveBeenCalledWith('FEATURE');
          expect(getNotificationsMock.execute).toHaveBeenCalledWith('INFO');
          expect(getNotificationsMock.execute).toHaveBeenCalledTimes(3);

          done();
        },
        error: fail,
      });
    });
  });

  describe('getTrainingContent$', () => {
    it('should return training content', done => {
      // Mock data
      const trainingContent: ITrainingMediaContentUIDto = {
        tags: [MappedMediaContentTags.CUSTOMER, MappedMediaContentTags.SALES],
        content: [
          {
            id: 1,
            title: 'Test Video',
            description: 'Test Description',
            isValidVideoUrl: true,
            urlVideo: 'https://example.com/video.mp4',
            urlThumbnail: 'https://example.com/thumbnail.jpg',
            tags: [MappedMediaContentTags.CUSTOMER],
          },
        ],
      };

      // Setup mocks
      getTrainingContentMock.execute.and.returnValue(of(trainingContent));

      // Call the service
      service.getTrainingContent$().subscribe({
        next: result => {
          // Verify the result
          expect(result).toBeTruthy();
          expect(result).toEqual(trainingContent);

          // Verify the mocks were called
          expect(getTrainingContentMock.execute).toHaveBeenCalledTimes(1);

          done();
        },
        error: fail,
      });
    });

    it('should return empty content when training content is null', done => {
      // Setup mocks
      getTrainingContentMock.execute.and.returnValue(of(null));

      // Call the service
      service.getTrainingContent$().subscribe({
        next: result => {
          // Verify the result
          expect(result).toBeTruthy();
          expect(result).toEqual({
            tags: [],
            content: [],
          });

          // Verify the mocks were called
          expect(getTrainingContentMock.execute).toHaveBeenCalledTimes(1);

          done();
        },
        error: fail,
      });
    });
  });
});
