<div class="bg-white p-4 rounded-2xl border border-[#FBFBFB]">
  <div class="my-3 w-full" *ngIf="challenge.currentScore > 0">
    <p class="text-sm mb-2 text-right">
      {{
        challenge.condition.goalType === 'AMOUNT'
          ? (challenge.currentScore | currency: 'MXN')
          : challenge.currentScore
      }}
      /
      {{
        challenge.condition.goalType === 'AMOUNT'
          ? (challenge.condition.goalValue | currency: 'MXN')
          : challenge.condition.goalValue
      }}
    </p>

    <div class="w-full">
      <div class="bg-[#E5E5E7] rounded-full h-2.5 overflow-hidden">
        <div
          class="bg-[#00E6F5] h-full"
          style="width: {{
            (challenge.currentScore / challenge.condition.goalValue) * 100
          }}%"></div>
      </div>
    </div>
  </div>

  <p class="text-md lg:text-lg mb-4">
    {{ challenge.description }}
  </p>

  <p class="text-sm mb-3">
    Al participar en las actividades que ofrecen recompensas, aceptas nuestros
    <strong>Términos y Condiciones</strong>
  </p>

  <div class="flex justify-end gap-2 items-center">
    <ng-container [ngSwitch]="challenge.status">
      <ng-container *ngSwitchCase="'Active'">
        <ng-container *ngIf="isExpired()">
          <p
            class="rounded bg-[#ECD45E] text-sm px-2 py-1 font-semibold uppercase">
            Vencido
          </p>
        </ng-container>

        <ng-container *ngIf="!isExpired()">
          <p
            class="rounded bg-[#64E9F7] text-sm px-2 py-1 font-semibold uppercase"
            *ngIf="challenge.currentScore === 0">
            Nuevo
          </p>

          <p
            class="rounded bg-[#B2A9F4] text-sm px-2 py-1 font-semibold uppercase"
            *ngIf="challenge.currentScore > 0">
            Activo
          </p>
        </ng-container>
      </ng-container>

      <p
        class="rounded bg-[#74C9A2] text-sm px-2 py-1 font-semibold uppercase"
        *ngSwitchCase="'Completed'">
        Completado
      </p>
    </ng-container>

    <p
      aplazoCountdown
      [date]="challenge.end"
      class="rounded bg-[#E7F4F6] text-sm px-2 py-1"
      *ngIf="!isExpired()"></p>
  </div>
</div>
