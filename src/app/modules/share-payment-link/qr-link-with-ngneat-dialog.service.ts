import { Injectable, inject } from '@angular/core';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import { DialogService } from '@ngneat/dialog';
import { lastValueFrom, take } from 'rxjs';
import { StoreService } from '../../core/application/services/store.service';
import { Order } from '../../core/domain/order.interface';
import { ShowOnlyQrDialogUsecase } from './application/show-only-qr-dialog.usecase';
import { ShareOnlyQrLinkComponent } from './share-only-qr-link.component';

@Injectable({
  providedIn: 'root',
})
export class QrLinkWithNgneatDialogService implements ShowOnlyQrDialogUsecase {
  readonly #storeService = inject(StoreService);
  readonly #dialog = inject(DialogService);

  async execute(loan: Order, showAlternativeHeader = false): Promise<void> {
    const branch = await lastValueFrom(
      this.#storeService.selectedBranch$.pipe(take(1))
    );

    if (!branch) {
      throw new RuntimeMerchantError(
        'Diálogo de métodos de pago, no hay sucursal seleccionada. Intente cerrar sesión y volver a iniciarla',
        'QrPaymentLink::emptyBranch',
        'qr-payment-link-dialog-error'
      );
    }

    this.#dialog.open(ShareOnlyQrLinkComponent, {
      data: {
        branch,
        loan,
        showAlternativeHeader,
      },
      maxWidth: '448px',
      enableClose: false,
    });
  }
}
