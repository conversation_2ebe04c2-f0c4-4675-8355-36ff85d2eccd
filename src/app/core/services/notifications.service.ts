import { HttpClient, HttpParams } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  POS_ENVIRONMENT_CORE,
  PosEnvironmentCoreType,
} from '../../../app-core/domain/environments';
import {
  INotificationResponseDto,
  NotificationType,
} from '../domain/notification';
import { NotificationsRepository } from '../domain/repositories/notifications.repository';

@Injectable({ providedIn: 'root' })
export class NOtificationsService implements NotificationsRepository {
  constructor(
    @Inject(POS_ENVIRONMENT_CORE)
    private environment: PosEnvironmentCoreType,
    private httpClient: HttpClient
  ) {}

  getNotificationsByTypeAndBranchId(
    type: NotificationType,
    branchId?: number | string
  ): Observable<INotificationResponseDto[]> {
    let params: HttpParams = new HttpParams();
    params = params.append('type', type);
    if (branchId != null) {
      params = params.append('branchId', branchId);
    }

    return this.httpClient.get<INotificationResponseDto[]>(
      `${this.environment.promoApiUrl}api/v1/promotions`,
      {
        params,
      }
    );
  }

  getNotificationsByTypeAndMerchantId(
    type: NotificationType,
    merchantId?: number | string
  ): Observable<INotificationResponseDto[]> {
    let params: HttpParams = new HttpParams();
    params = params.append('type', type);

    if (merchantId != null && !isNaN(+merchantId) && +merchantId > 0) {
      params = params.append('merchantId', `${merchantId}`);
    }

    return this.httpClient.get<INotificationResponseDto[]>(
      `${this.environment.promoApiUrl}api/v1/promotions`,
      {
        params,
      }
    );
  }
}
