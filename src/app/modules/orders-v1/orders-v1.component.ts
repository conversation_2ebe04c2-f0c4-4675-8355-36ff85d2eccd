import { AsyncPipe, I18nPluralPipe } from '@angular/common';
import { Component, OnDestroy, inject } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { LoaderService, NotifierService } from '@aplazo/merchant/shared';
import {
  AplazoMatchMediaService,
  ValidDynamicPipesNames,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormFieldDirectives,
  AplazoSearchInputComponent,
  AplazoSelectComponents,
  searchControlFactory,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponents,
  AplazoConfirmDialogComponent,
  AplazoStatCardComponent,
} from '@aplazo/shared-ui/merchant';
import { iconArrowPath, iconChevronDown } from '@aplazo/ui-icons';
import { DialogService } from '@ngneat/dialog';
import { StatsigModule } from '@statsig/angular-bindings';
import {
  BehaviorSubject,
  EMPTY,
  MonoTypeOperatorFunction,
  Observable,
  Subject,
  catchError,
  combineLatest,
  combineLatestWith,
  delay,
  distinctUntilChanged,
  filter,
  firstValueFrom,
  map,
  of,
  pipe,
  skipUntil,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
  timer,
  withLatestFrom,
} from 'rxjs';
import { AnalyticsService } from '../../core/application/services/analytics.service';
import { StoreService } from '../../core/application/services/store.service';
import { Order } from '../../core/domain/order.interface';
import { EventService } from '../../core/domain/repositories/events-service';
import { VALID_ORDER_STATUS } from '../../core/domain/valid-order-status';
import { OrdersWithSocketService } from '../../core/services/orders-with-socket.service';
import { HandleOrderGenerationUseCase } from '../cart-v1/application/handle-order-generation.usecase';
import { GetActiveBannerUsecase } from '../dynamic-banners/application/get-active-banner.usecase';
import { DynamicBannerComponent } from '../dynamic-banners/infra/components/dynamic-banner.component';
import { ShowPaymentLinkDialogService } from '../share-payment-link/application/services/show-payment-link-dialog.service';
import { PosUiBranchFfDirective } from '../shared/directives/pos-ui-branch-ff.directive';
import {
  IOrdersTableInjectedTextUI,
  OrdersTableComponent,
} from '../shared/organisms/orders-table/orders-table.component';
import { PreloanCartButtonComponent } from '../shared/preloan-cart-button.component';
import { CancelOrderUseCase } from './application/usecases/cancel-order.usecase';
import { CheckStatusOrderUsecase } from './application/usecases/check-status-order.usecase';
import { GetRefundInfoUsecase } from './application/usecases/get-refund-info.usecase';
import { GetReportUsecase } from './application/usecases/get-report.usecase';
import { RefundInfoComponent } from './refund-info.component';

export interface IOrdersStatsInjectedTextUI {
  totalAmount: string;
  totalOrders: string;
  avgAmount: string;

  [key: string]: string;
}

export interface ICounterI18n {
  empty: string;
  singular: string;
  plural: string;
}

interface IStatsUI {
  statCardTitle: string;
  value: string;
  isDarkMode: boolean;
  pipeName?: ValidDynamicPipesNames;
  helpTooltip?: string;
  comparison?: {
    label: string;
    value: string;
    isDarkMode: boolean;
    pipeName?: ValidDynamicPipesNames;
  };
}

const getPrice = (orders: Order[]) =>
  orders.reduce((acc, curr) => acc + curr.price, 0);
const getOrders = (orders: Order[]) => orders.length;
const getAvg = (orders: Order[]) => {
  if (orders.length > 0) {
    return (
      orders.reduce((acc, curr) => acc + Math.abs(curr.price), 0) /
      orders.length
    );
  }

  return 0;
};

const STATS_CONTENT = {
  totalAmount: getPrice,
  totalOrders: getOrders,
  avgAmount: getAvg,
} as const;

const ORDER_TYPES = {
  Todos: '',
  Aprobados: VALID_ORDER_STATUS.active,
  Pendientes: VALID_ORDER_STATUS.created,
} as const;

type OrderTypeLabel = keyof typeof ORDER_TYPES;

@Component({
  standalone: true,
  selector: 'aplazo-orders-v1',
  templateUrl: './orders-v1.component.html',
  imports: [
    AsyncPipe,
    I18nPluralPipe,
    AplazoButtonComponent,
    AplazoCardComponent,
    AplazoStatCardComponent,
    AplazoIconComponent,
    AplazoCommonMessageComponents,
    AplazoFormFieldDirectives,
    AplazoSelectComponents,
    AplazoSearchInputComponent,
    ReactiveFormsModule,
    OrdersTableComponent,
    PreloanCartButtonComponent,
    PosUiBranchFfDirective,
    DynamicBannerComponent,
    StatsigModule,
  ],
})
export class OrdersV1Component implements OnDestroy {
  readonly #loaderService = inject(LoaderService);
  readonly #ordersService = inject(OrdersWithSocketService);
  readonly #storeService = inject(StoreService);
  readonly #showPaymentLink = inject(ShowPaymentLinkDialogService);
  readonly #dialog = inject(DialogService);
  readonly #cancelOrderUseCase = inject(CancelOrderUseCase);
  readonly #getReportUseCase = inject(GetReportUsecase);
  readonly #verifyOrderStatusUseCase = inject(CheckStatusOrderUsecase);
  readonly #getRefundInfoUseCase = inject(GetRefundInfoUsecase);
  readonly #i18n = inject(I18NService);
  readonly #scope = 'orders';
  readonly #registryIconService = inject(AplazoIconRegistryService);
  readonly #matchMedia = inject(AplazoMatchMediaService);
  readonly #newOrderHandler = inject(HandleOrderGenerationUseCase);
  readonly #eventService: EventService = inject(EventService);
  readonly #analyticsService = inject(AnalyticsService);
  readonly #getBannerContentUsecase = inject(GetActiveBannerUsecase);

  readonly screenSize$ = this.#matchMedia.screenSize$();

  readonly minLength = 3;
  #loaderId: string | undefined;
  ordersCount: number;

  #destroy$ = new Subject<void>();
  refreshSocket$: Subject<void> = new Subject<void>();

  readonly #isRefreshing$ = new BehaviorSubject<boolean>(false);

  readonly #orders$ = this.#ordersService.orders$;

  readonly emptyMessageTextTemplate$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
    button: {
      label: string;
    };
  }>({
    key: 'emptyMessage',
    scope: this.#scope,
  });
  readonly counterLabelTextTemplate$ = this.#i18n
    .getTranslateObjectByKey<ICounterI18n>({
      key: 'counterLabel',
      scope: this.#scope,
    })
    .pipe(
      map(text => ({
        '=0': text.empty,
        '=1': '# ' + text.singular,
        other: '# ' + text.plural,
      }))
    );

  readonly filterTextTemplate$ = this.#i18n.getTranslateObjectByKey<{
    placeholder: string;
    tooltip?: string;
  }>({
    key: 'orderFilter',
    scope: this.#scope,
  });

  readonly ordersTableInjectedText$ =
    this.#i18n.getTranslateObjectByKey<IOrdersTableInjectedTextUI>({
      key: 'table',
      scope: this.#scope,
    });
  readonly emptySearchInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
  }>({
    key: 'emptySearch',
    scope: this.#scope,
  });
  readonly ordersStatsInjectedText$ =
    this.#i18n.getTranslateObjectByKey<IOrdersStatsInjectedTextUI>({
      key: 'stats',
      scope: this.#scope,
    });
  readonly searchbarInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    placeholder: string;
    minlengthError: string;
    requiredError: string;
  }>({
    key: 'searchbar',
    scope: this.#scope,
    params: {
      minlengthError: { minLength: this.minLength },
      requiredError: { minLength: this.minLength },
    },
  });
  readonly newOrderLabelText$ = this.#i18n.getTranslateObjectByKey<{
    buttonLabel: string;
  }>({
    key: 'newOrder',
    scope: this.#scope,
  });

  searchControl = new FormControl<string | null>('');
  readonly #searchUtilitiesFactory = searchControlFactory({
    minLength: this.minLength,
    debouncedTime: 450,
  });
  readonly orderTypes = Object.keys(ORDER_TYPES) as OrderTypeLabel[];
  orderTypeControl = new FormControl<OrderTypeLabel>(this.orderTypes[0], {
    nonNullable: true,
  });
  currentOrderType$: Observable<OrderTypeLabel> =
    this.orderTypeControl.valueChanges.pipe(
      startWith(this.orderTypeControl.value ?? this.orderTypes[0]),
      takeUntil(this.#destroy$)
    );
  searchValue$ = this.searchControl.valueChanges.pipe(
    startWith(this.searchControl.value),
    this.#searchUtilitiesFactory.debouncedValue(),
    this.#filterSearchStream(),
    takeUntil(this.#destroy$)
  );
  isLoadingReport$: Observable<boolean> = this.#loaderService.isLoading$.pipe(
    combineLatestWith(this.#isRefreshing$),
    map(([isLoading, isRefreshing]) => isLoading || isRefreshing)
  );
  integrationType$: Observable<string> = this.#storeService.integrationType$;
  branchId$: Observable<number | null> =
    this.#storeService.selectedBranch$.pipe(map(branch => branch?.id || null));

  readonly merchantId$ = this.#storeService.getMerchantId$();

  readonly bannerContentUI$ = combineLatest({
    merchantId: this.merchantId$.pipe(take(1)),
    branchId: this.branchId$.pipe(take(1)),
  }).pipe(
    switchMap(({ merchantId, branchId }) =>
      this.#getBannerContentUsecase.execute({
        merchantId: merchantId ?? 0,
        branchId: branchId ?? 0,
      })
    ),
    takeUntil(this.#destroy$)
  );

  orders$: Observable<Order[]> = this.getOrders$();

  ordersStatsContent$: Observable<IStatsUI[]> = this.getOrderStatsContet();
  refresh$: Observable<boolean> = this.refreshSocket$.pipe(
    skipUntil(
      this.isLoadingReport$.pipe(
        map(isDisabled => !isDisabled),
        filter(Boolean)
      )
    ),
    map(() => true),
    tap(() => {
      this.#loaderId = this.#loaderService.show();
    }),
    tap(() => {
      this.#ordersService.refreshOrdersList();
    }),
    delay(1850),
    tap(() => {
      this.#isRefreshing$.next(false);
      this.#loaderService.hide(this.#loaderId as string);
    })
  );

  readonly #notifier = inject(NotifierService);

  readonly paymentError$ = this.#ordersService.paymentFailure$.pipe(
    takeUntil(this.#destroy$),
    distinctUntilChanged((prev, curr) => prev?.createdAt === curr?.createdAt),
    withLatestFrom(this.branchId$),
    tap(([error, branchIdValue]) => {
      if (error?.cashierMessage && Number(error.branchId) === branchIdValue) {
        this.#notifier.warning({
          title: 'Alerta',
          message: `[Orden #${error.loanId}] ${error.cashierMessage}`,
        });
      }
    }),
    map(([error]) => error),
    startWith(null)
  );

  vm$ = combineLatest({
    orders: this.orders$,
    ordersStatsContent: this.ordersStatsContent$,
    screenSize: this.screenSize$,
    searchValue: this.searchValue$,
    orderTypeControl: this.orderTypeControl.valueChanges.pipe(
      startWith(this.orderTypeControl.value)
    ),
    branchId: this.branchId$,
    integrationType: this.integrationType$,
    emptyMessage: this.emptyMessageTextTemplate$,
    counterLabelTextTemplate: this.counterLabelTextTemplate$,
    filterTextTemplate: this.filterTextTemplate$,
    ordersTableInjectedText: this.ordersTableInjectedText$,
    emptySearchInjectedText: this.emptySearchInjectedText$,
    searchbarInjectedText: this.searchbarInjectedText$,
    isLoadingReport: this.isLoadingReport$,
    bannerContentUI: this.bannerContentUI$,
  });

  constructor() {
    this.#registryIconService.registerIcons([iconArrowPath, iconChevronDown]);
    this.paymentError$
      .pipe(
        catchError(err => {
          console.error('PaymentError::', err);
          return EMPTY;
        })
      )
      .subscribe();
  }

  refreshOrdersList(): void {
    this.#isRefreshing$.next(true);
    this.refreshSocket$.next();
    this.#analyticsService.track('buttonClick', {
      buttonName: 'refreshOrdersList',
    });
  }

  resetSearch(): void {
    const id = this.#loaderService.show();
    this.searchControl.reset();
    timer(0, 650)
      .pipe(take(1))
      .subscribe(() => {
        this.#loaderService.hide(id);
      });
  }

  cancelOrder(order: Order): void {
    this.#i18n
      .getTranslateObjectByKey<{
        cancelDialog: {
          title: string;
          message: string;
        };
      }>({
        key: 'cancelDialog',
        scope: this.#scope,
        params: { dynamicMessage: { message: `Orden: ${order.loanId}` } },
      })
      .pipe(
        switchMap(
          data =>
            this.#dialog.open(AplazoConfirmDialogComponent, {
              data,
              maxWidth: '320px',
            }).afterClosed$
        ),
        switchMap(dialogResult => {
          if (dialogResult?.confirmation) {
            this.#eventService
              .cancelOrder({
                order_id: order.loanId,
              })
              .subscribe();
            return this.#cancelOrderUseCase.execute(order).pipe(
              tap(() => {
                this.#analyticsService.track('buttonClick', {
                  buttonName: 'cancelLoan',
                  loanId: order.loanId,
                });
              })
            );
          }

          return of(null);
        }),
        take(1)
      )
      .subscribe();
  }

  sharePaymentLink(order: Order): void {
    this.#analyticsService.track('buttonClick', {
      buttonName: 'shareCheckoutLink',
      loanId: order.loanId,
    });
    this.#showPaymentLink.execute(order);
  }

  showRefundInfo(order: Order): void {
    this.#getRefundInfoUseCase
      .execute(String(order.loanId))
      .pipe(
        take(1),
        switchMap(
          data =>
            this.#dialog.open(RefundInfoComponent, {
              data,
            }).afterClosed$
        ),
        take(1)
      )
      .subscribe();
  }

  checkOrderStatus(order: Order) {
    this.#verifyOrderStatusUseCase.execute(order).subscribe();
  }

  async newOrder(): Promise<void> {
    const branch = await firstValueFrom(
      this.#storeService.selectedBranch$.pipe(take(1))
    );

    await firstValueFrom(this.#newOrderHandler.execute().pipe(take(1)));

    this.#eventService
      .newOrder({
        storeFrontId: branch?.id || 0,
      })
      .subscribe();

    this.#analyticsService.track('buttonClick', {
      buttonName: 'todayNewOrder',
    });
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  generateNewReport() {
    this.#getReportUseCase.execute();
  }

  private getOrders$(): Observable<Order[]> {
    return combineLatest([
      this.#orders$,
      this.searchValue$,
      this.currentOrderType$,
      this.branchId$,
      this.integrationType$,
    ]).pipe(
      map(([response, search, orderType, branchId, integrationType]) => {
        if (search) {
          return response
            .filter(branch => branch.branchId === branchId)
            .filter(branch => branch.status.includes(ORDER_TYPES[orderType]))
            .filter(
              branch =>
                String(branch.loanId).includes(search) ||
                String(branch.sellsAgentId).includes(search)
            )
            .map(ord => ({
              ...ord,
              isWalmartOrder: integrationType === 'WM_APLAZO',
            }));
        }
        return response
          .filter(branch => branch.branchId === branchId)
          .filter(branch => branch.status.includes(ORDER_TYPES[orderType]))
          .map(ord => ({
            ...ord,
            isWalmartOrder: integrationType === 'WM_APLAZO',
          }));
      }),
      withLatestFrom(this.#loaderService.isLoading$),
      map(([orders, isLoading]) => {
        this.ordersCount = orders.length;
        if (this.#loaderId && isLoading) {
          this.#loaderService.hide(this.#loaderId);
        }
        return orders;
      }),
      takeUntil(this.#destroy$)
    );
  }

  private getOrderStatsContet(): Observable<IStatsUI[]> {
    return combineLatest([
      this.#orders$,
      this.ordersStatsInjectedText$,
      this.branchId$,
    ]).pipe(
      filter(([response]) => Array.isArray(response)),

      map(([response, ordersStatsInjectedText, branchId]) => {
        const filteredOrders = response
          .filter(branch => branch.branchId === branchId)
          .filter(item => item.status.toLowerCase() === 'activo');

        return Object.entries(STATS_CONTENT).map(([key, fn]) => {
          return {
            isDarkMode: true,
            statCardTitle: ordersStatsInjectedText[key],
            value: `${fn(filteredOrders)}`,
            pipeName: (key === 'totalOrders'
              ? 'decimal'
              : 'currency') as ValidDynamicPipesNames,
          };
        });
      }),
      takeUntil(this.#destroy$)
    );
  }

  #filterSearchStream(): MonoTypeOperatorFunction<string | null> {
    return pipe(
      filter(
        searchValue =>
          !searchValue ||
          searchValue.length === 0 ||
          searchValue.length >= this.minLength
      )
    );
  }
}
