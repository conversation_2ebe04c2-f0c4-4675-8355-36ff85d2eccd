import { Injectable, inject } from '@angular/core';
import { Observable, of, timer } from 'rxjs';
import { switchMap, take, tap, catchError } from 'rxjs/operators';
import { OrdersWithSocketService } from 'src/app/core/services/orders-with-socket.service';
import { PosOfflineService } from 'src/app/core/services/pos-offline.service';
import { Order } from 'src/app/core/domain/order.interface';
import { ShowPopupUseCase } from './show-popup.usecase';

export interface PaymentSuccessEvent {
  loanId: number;
  branchId: number;
  status: string;
  timestamp: Date;
}

/**
 * Use case para detectar pagos exitosos y trigger de popups.
 * Monitorea cambios de estado en órdenes y detecta cuando una orden
 * cambia a estado "Activo" (equivalente a "Pagado").
 */
@Injectable({ providedIn: 'root' })
export class CheckPaymentSuccessUseCase {
  private ordersService = inject(OrdersWithSocketService);
  private posOfflineService = inject(PosOfflineService);
  private showPopupUseCase = inject(ShowPopupUseCase);

  /**
   * Inicia el monitoreo de pagos exitosos.
   * Se ejecuta automáticamente cuando se detecta un cambio de estado a "Activo".
   */
  startMonitoring(): void {
    this.ordersService.orders$
      .pipe(
        switchMap(orders => {
          // Buscar órdenes que cambiaron a estado "Activo" recientemente
          const activeOrders = orders.filter(
            order => order.status.toLowerCase() === 'activo'
          );

          if (activeOrders.length === 0) {
            return of(null);
          }

          // Verificar si alguna orden cambió recientemente a "Activo"
          return this.checkRecentStatusChanges(activeOrders);
        }),
        catchError(error => {
          console.warn('Error monitoring payment success:', error);
          return of(null);
        })
      )
      .subscribe();
  }

  /**
   * Verifica si hay órdenes que cambiaron recientemente a estado "Activo".
   * @param activeOrders - Lista de órdenes activas
   * @returns Observable con la orden que cambió recientemente
   */
  private checkRecentStatusChanges(
    activeOrders: Order[]
  ): Observable<PaymentSuccessEvent | null> {
    // Por simplicidad, tomamos la primera orden activa
    // En una implementación real, se podría verificar el timestamp de cambio
    const recentActiveOrder = activeOrders[0];

    if (!recentActiveOrder) {
      return of(null);
    }

    // Verificar que la orden tenga un branchId válido
    const branchId = recentActiveOrder.branchId || recentActiveOrder.branch?.id;
    if (!branchId) {
      return of(null);
    }

    const paymentEvent: PaymentSuccessEvent = {
      loanId: recentActiveOrder.loanId,
      branchId: branchId,
      status: recentActiveOrder.status,
      timestamp: new Date(),
    };

    // Trigger del popup después de 7 segundos
    return timer(7000).pipe(
      take(1),
      tap(() => {
        console.log(
          'Payment success detected, triggering popup:',
          paymentEvent
        );
        this.triggerPopupAfterPaymentSuccess();
      }),
      switchMap(() => of(paymentEvent))
    );
  }

  /**
   * Trigger para mostrar popup después de un pago exitoso.
   * Se ejecuta 7 segundos después de detectar el pago exitoso.
   */
  private triggerPopupAfterPaymentSuccess(): void {
    this.showPopupUseCase.execute().subscribe({
      next: result => {
        if (result.success) {
          console.log('Popup shown successfully after payment success');
        } else {
          console.log('No popup available to show:', result.reason);
        }
      },
      error: error => {
        console.error('Error showing popup after payment success:', error);
      },
    });
  }

  /**
   * Verifica manualmente el estado de una orden específica.
   * @param loanId - ID de la orden a verificar
   * @returns Observable con el resultado de la verificación
   */
  checkOrderStatus(loanId: number): Observable<PaymentSuccessEvent | null> {
    return this.posOfflineService.checkOrderStatus(loanId).pipe(
      switchMap(response => {
        if (!response.content) {
          return of(null);
        }

        const order = response.content;
        const branchId = order.branchId || order.branch?.id;

        if (order.status.toLowerCase() === 'activo' && branchId) {
          const paymentEvent: PaymentSuccessEvent = {
            loanId: order.loanId,
            branchId: branchId,
            status: order.status,
            timestamp: new Date(),
          };

          // Trigger del popup después de 7 segundos
          return timer(7000).pipe(
            take(1),
            tap(() => {
              this.triggerPopupAfterPaymentSuccess();
            }),
            switchMap(() => of(paymentEvent))
          );
        }

        return of(null);
      }),
      catchError(error => {
        console.error('Error checking order status:', error);
        return of(null);
      })
    );
  }
}
