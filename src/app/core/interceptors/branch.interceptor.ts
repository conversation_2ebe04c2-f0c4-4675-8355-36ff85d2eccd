import { HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { map, mergeMap, take } from 'rxjs';
import { StoreService } from '../application/services/store.service';
import { parseUrl } from '../domain/url-parser';

export const branchInterceptor: HttpInterceptorFn = (request, next) => {
  const store = inject(StoreService);
  const isSvgResource = request.url.match(/\.svg$|\.json$/);
  const url = parseUrl(request.url);
  const isSSE = url?.pathname.endsWith('sse') ?? false;

  if (isSvgResource || isSSE) {
    return next(request);
  }

  return store.selectedBranch$.pipe(
    take(1),
    map(branch => {
      if (!branch) {
        return request;
      }

      return request.clone({
        headers: request.headers.set('x-selected-branch', `${branch.id}`),
      });
    }),
    mergeMap(newReq => next(newReq))
  );
};
