import { Injectable } from '@angular/core';
import { LoaderService, NotifierService } from '@aplazo/merchant/shared';
import {
  MonoTypeOperatorFunction,
  Observable,
  catchError,
  finalize,
  map,
  pipe,
  take,
  throwError,
} from 'rxjs';
import { PaymentsRepository } from '../../../../app/core/domain/repositories/payments.repository';

@Injectable({ providedIn: 'root' })
export class RequestAuthorizationCodeUsecase {
  constructor(
    private paymentsService: PaymentsRepository,
    private loader: LoaderService,
    private notifier: NotifierService
  ) {}

  public execute(phone: number, loanToken: string): Observable<any> {
    const loaderId = this.loader.show();
    const phoneWithInternationalCode = `52${phone}`;

    if (phoneWithInternationalCode.length !== 12) {
      this.loader.hide(loaderId);
      this.notifier.warning({
        title: 'Número de teléfono inválido',
        message: 'El número de teléfono debe tener 10 dígitos',
      });
      return throwError(() =>
        Error('El número de teléfono debe tener 10 dígitos')
      );
    }
    console.log('%s %s', phoneWithInternationalCode, loanToken);
    return this.paymentsService
      .requestCustomerAuthorizationCode(phoneWithInternationalCode, loanToken)
      .pipe(this.#showSuccessOrError(loaderId));
  }

  #showSuccessOrError(loaderId: string): MonoTypeOperatorFunction<any> {
    return pipe(
      take(1),

      map(checkoutData => {
        this.notifier.success({
          title: 'Código de autorización enviado',
        });
        const schemes = checkoutData.schemes as any[];
        const defaultScheme = schemes.length
          ? schemes.find(scheme => scheme.installments === 5) || schemes[0]
          : { id: -1 };

        return defaultScheme.id as number;
      }),

      catchError(error => {
        console.error(error);

        this.notifier.warning({
          title: '¡Ups! Algo salió mal',
          message: 'No se pudo enviar el código de autorización',
        });

        return throwError(() => Error(JSON.stringify(error)));
      }),

      finalize(() => {
        this.loader.hide(loaderId);
      })
    );
  }
}
