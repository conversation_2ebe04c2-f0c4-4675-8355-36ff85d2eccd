import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  RawDateYearFirstWithHyphen,
  RuntimeMerchantError,
  TemporalService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { intlFormatDistance, isAfter } from 'date-fns';
import { catchError, finalize, map, Observable, take, throwError } from 'rxjs';
import {
  ContestRankingUI,
  fromParticipantDetailResponseToContestRankingUI,
} from '../../domain/contest';
import { ContestRepository } from '../../domain/repositories/contest.repository';

@Injectable({ providedIn: 'root' })
export class GetOneParticipantWithDetailsUseCase
  implements BaseUsecase<string, Observable<ContestRankingUI>>
{
  readonly #repository = inject(ContestRepository);
  readonly #loader = inject(LoaderService);
  readonly #errorHandler = inject(UseCaseErrorHandler);
  readonly #temporal = inject(TemporalService);

  execute(participantCode: string): Observable<ContestRankingUI> {
    const idLoader = this.#loader.show();

    try {
      if (
        !Guard.againstNullOrUndefined({ code: participantCode }, 'code')
          .succeeded
      ) {
        throw new RuntimeMerchantError(
          'El código del participante es requerido',
          'GetOneWithDetailsUseCase::emptyParticipantCode'
        );
      }

      return this.#repository.getOneWithDetails(participantCode).pipe(
        map(fromParticipantDetailResponseToContestRankingUI),

        map(ranking => {
          const toCompareDate = this.#temporal.fromStringToDate(
            ranking.campaignFinishDate as RawDateYearFirstWithHyphen
          );

          toCompareDate.setHours(23, 59, 59, 999);

          const isAfterToday = isAfter(toCompareDate, this.#temporal.today);

          const humanReadable = intlFormatDistance(
            toCompareDate,
            this.#temporal.today,
            {
              locale: 'es-MX',
            }
          );

          return {
            ...ranking,
            campaignFinishDate: humanReadable,
            isFutureDate: isAfterToday,
          };
        }),

        catchError(err => {
          if (
            err instanceof HttpErrorResponse &&
            err.status === 400 &&
            err.error.code === 'APZCMS004'
          ) {
            throw new RuntimeMerchantError(
              'El código del participante no existe',
              'GetOneWithDetailsUseCase::participantNotFound'
            );
          }

          return this.#errorHandler.handle<never>(err);
        }),

        take(1),

        finalize(() => {
          this.#loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.#loader.hide(idLoader);
      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
