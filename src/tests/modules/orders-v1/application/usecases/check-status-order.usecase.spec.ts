import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { LoaderService, NotifierService } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { AnalyticsService } from '../../../../../app/core/application/services/analytics.service';
import { Order } from '../../../../../app/core/domain/order.interface';
import {
  CommonPosOfflineResponse,
  PosOfflineRepository,
} from '../../../../../app/core/domain/repositories/pos-offline.repository';
import { CheckStatusOrderUsecase } from '../../../../../app/modules/orders-v1/application/usecases/check-status-order.usecase';

describe('CheckStatusOrderUsecase', () => {
  let usecase: CheckStatusOrderUsecase;
  let repository: jasmine.SpyObj<PosOfflineRepository>;
  let analyticsTrackSpy: jasmine.SpyObj<AnalyticsService>;
  let successNotifierSpy: jasmine.Spy;
  let warnNotifierSpy: jasmine.Spy;
  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        CheckStatusOrderUsecase,
        provideLoaderTesting(),
        provideNotifierTesting(),
        {
          provide: PosOfflineRepository,
          useValue: jasmine.createSpyObj('PosOfflineRepository', [
            'checkOrderStatus',
          ]),
        },
        {
          provide: AnalyticsService,
          useValue: jasmine.createSpyObj('AnalyticsService', ['track']),
        },
      ],
    });

    usecase = TestBed.inject(CheckStatusOrderUsecase);
    repository = TestBed.inject(
      PosOfflineRepository
    ) as jasmine.SpyObj<PosOfflineRepository>;
    analyticsTrackSpy = TestBed.inject(
      AnalyticsService
    ) as jasmine.SpyObj<AnalyticsService>;
    const loader = TestBed.inject(LoaderService);
    const notifier = TestBed.inject(NotifierService);

    successNotifierSpy = spyOn(notifier, 'success').and.callThrough();
    warnNotifierSpy = spyOn(notifier, 'warning').and.callThrough();
    showLoaderSpy = spyOn(loader, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(CheckStatusOrderUsecase);
  });

  it('should notify the success case', fakeAsync(() => {
    const response: CommonPosOfflineResponse<Order> = {
      content: {
        status: 'Activo',
      } as any,
      error: null,
      code: 200,
    };

    repository.checkOrderStatus.and.returnValue(of(response));

    let result: any;

    usecase.execute({ status: 'created', loanId: 123 } as any).subscribe({
      next: res => {
        result = res;
      },
      error: fail,
    });

    tick();

    expect(result).toEqual(response);
    expect(repository.checkOrderStatus).toHaveBeenCalledTimes(1);
    expect(repository.checkOrderStatus).toHaveBeenCalledOnceWith(123);
    expect(analyticsTrackSpy.track).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(1);
  }));

  it('should propagate the error', fakeAsync(() => {
    const error = new HttpErrorResponse({
      status: 500,
      statusText: 'Internal Server Error',
    });

    repository.checkOrderStatus.and.returnValue(throwError(() => error));

    let result: any;

    usecase.execute({ status: 'created', loanId: 123 } as any).subscribe({
      next: fail,
      error: err => {
        result = err;
      },
    });

    tick();

    expect(result).toEqual(error);
    expect(repository.checkOrderStatus).toHaveBeenCalledTimes(1);
    expect(repository.checkOrderStatus).toHaveBeenCalledOnceWith(123);
    expect(analyticsTrackSpy.track).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(warnNotifierSpy).toHaveBeenCalledTimes(1);
  }));

  it('should notify when the order status has not changed', fakeAsync(() => {
    const response: CommonPosOfflineResponse<Order> = {
      content: {
        status: 'created',
      } as any,
      error: null,
      code: 200,
    };

    repository.checkOrderStatus.and.returnValue(of(response));

    let result: any;

    usecase.execute({ status: 'created', loanId: 123 } as any).subscribe({
      next: res => {
        result = res;
      },
      error: fail,
    });

    tick();

    expect(result).toEqual(response);
    expect(repository.checkOrderStatus).toHaveBeenCalledTimes(1);
    expect(repository.checkOrderStatus).toHaveBeenCalledOnceWith(123);
    expect(analyticsTrackSpy.track).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
    expect(warnNotifierSpy).toHaveBeenCalledTimes(1);
    expect(successNotifierSpy).toHaveBeenCalledTimes(0);
  }));
});
