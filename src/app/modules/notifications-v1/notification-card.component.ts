import { NgClass } from '@angular/common';
import { Component, Input } from '@angular/core';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { endOfDay, isWithinInterval, startOfDay, sub } from 'date-fns';
import { NotificationUI } from '../../core/domain/notification';

const today = endOfDay(new Date());

const yesterday = startOfDay(sub(today, { days: 1 }));

const dateRangeToValidate = {
  start: yesterday,
  end: today,
};

const hasDateWithinRange = (date: Date) => {
  if (
    date == null ||
    date instanceof Date === false ||
    isFinite(date.getTime()) === false
  ) {
    return false;
  }
  return isWithinInterval(date, dateRangeToValidate);
};

@Component({
  selector: 'aplazo-notification-card',
  standalone: true,
  imports: [AplazoDynamicPipe, NgClass],
  template: `
    <article
      class="relative pb-12 flex w-full"
      [ngClass]="{
        'opacity-70': !isNew,
        'opacity-100': isNew,
        'before:absolute before:top-0 before:left-8 before:w-1 before:h-full before:border-l before:border-dark-tertiary before:-z-[1]':
          isDashed
      }">
      <div
        class="flex flex-shrink-0 items-center justify-center w-16 h-16 aspect-square mr-8 rounded-full bg-aplazo-aplazo text-lg font-medium">
        {{ content.lastUpdate | aplzDynamicPipe: 'dateDays' }}
      </div>

      <div>
        <div class="text-dark-secondary">
          {{ content.lastUpdate | aplzDynamicPipe: 'timeShort' }}
        </div>

        <h3 class="text-xl font-medium">
          {{ content.title }}
        </h3>

        <p class="text-base">
          {{ content.description }}
        </p>
      </div>
    </article>
  `,
})
export class AplazoNotificationCardComponent {
  @Input()
  set notification(value: NotificationUI | null) {
    if (!value) {
      throw new Error('AplazoNotification::emptyError');
    }
    this.#content = value;
  }
  get content(): NotificationUI {
    return this.#content;
  }
  #content!: NotificationUI;

  @Input()
  set newOne(value: boolean | null) {
    const isNewByBussinesDomain = hasDateWithinRange(this.content.lastUpdate);
    this.#isNew = !!value || isNewByBussinesDomain;
  }
  get isNew(): boolean {
    return this.#isNew;
  }
  #isNew = false;

  @Input()
  set dashed(value: boolean | null) {
    this.#isDashed = !!value;
  }
  get isDashed(): boolean {
    return this.#isDashed;
  }
  #isDashed = false;
}
