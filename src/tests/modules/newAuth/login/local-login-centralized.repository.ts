import { Observable, of, throwError } from 'rxjs';
import usersWithEmail from '../../../local-user-with-email.db.json';
import usersWithoutEmail from '../../../local-user-without-email.db.json';
import { CredentialsV2 } from 'src/app/core/domain/credentialsV2.interface';
import { LoginCentralizedRepository } from 'src/app/modules/newAuth/login/domain/login-centralized.repository';

export class LocalLoginCentralizedRepository
  implements LoginCentralizedRepository
{
  private readonly usersWithEmail = usersWithEmail;
  private readonly usersWithoutEmail = usersWithoutEmail;

  public execute(credentials: CredentialsV2): Observable<string> {
    const userWithoutEmail = this.usersWithoutEmail.find(
      item =>
        item.sub === credentials.merchantUsername &&
        item.password === credentials.merchantPassword
    );

    const userWithEmail = this.usersWithEmail.find(
      item =>
        item.sub === credentials.merchantUsername &&
        item.password === credentials.merchantPassword
    );

    if (!userWithoutEmail && !userWithEmail) {
      return throwError(() => new Error('User not found'));
    }

    const user = userWithoutEmail ?? userWithEmail;
    const { password, ...userWithoutPassword } = user as { password?: string };

    return of(JSON.stringify(userWithoutPassword));
  }
  public getMerchantDetails(token: string): Observable<{ name: string }> {
    const user = this.usersWithEmail.find(item => item.sub === token);
    return of({ name: user?.name ?? '' });
  }
}
