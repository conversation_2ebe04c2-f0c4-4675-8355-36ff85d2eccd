import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { shareReplay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class TogglePaymentLinkDialogService {
  #isOpenDialog$: BehaviorSubject<boolean> = new BehaviorSubject(false);

  private toggleDialog(args: { isOpen: boolean }) {
    this.#isOpenDialog$.next(args.isOpen);
  }

  public get isOpenDialog$(): Observable<boolean> {
    return this.#isOpenDialog$.pipe(shareReplay(1));
  }

  public notifyDialogOpen() {
    this.toggleDialog({ isOpen: true });
  }

  public requestDialogClose() {
    this.toggleDialog({ isOpen: false });
  }
}
