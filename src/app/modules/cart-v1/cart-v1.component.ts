import { Async<PERSON>ipe, NgIf } from '@angular/common';
import {
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  inject,
} from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { I18NService } from '@aplazo/i18n';
import { RedirectionService } from '@aplazo/merchant/shared';
import {
  AplazoDynamicPipe,
  AplazoLettersNumbersDirective,
  AplazoMatchMediaService,
  AplazoNoWhiteSpaceDirective,
  AplazoTruncateLengthDirective,
  AplazoUppercaseDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconArrowLeft } from '@aplazo/ui-icons';
import { DialogService } from '@ngneat/dialog';
import { StatsigService } from '@statsig/angular-bindings';
import {
  BehaviorSubject,
  Observable,
  Subject,
  combineLatest,
  debounceTime,
  map,
  of,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { AnalyticsService } from '../../core/application/services/analytics.service';
import { StoreService } from '../../core/application/services/store.service';
import {
  POS_APP_ROUTES,
  ROUTE_CONFIG,
} from '../../core/domain/config/app-routes.core';
import { IProductRequestUIDto } from '../../core/domain/product-request';
import { EventService } from '../../core/domain/repositories/events-service';
import {
  CartProductsTableComponent,
  ICartProductsTableInjectedTextUI,
} from '../../modules/shared/organisms/cart-products-table/cart-products-table.component';
import { AgentIdComponent, IAgentIdTextTemplateUI } from './agent-id.component';
import { FinishOrderWithPreloanUseCase } from './application/finish-order-with-preloan.usecase';
import { FinishOrderUseCase } from './application/finish-order.usecase';
import {
  IProductFormInjectedTextUI,
  ProductFormComponent,
} from './product-form/product-form.component';

@Component({
  standalone: true,
  selector: 'aplazo-cart-v1',
  templateUrl: './cart-v1.component.html',
  imports: [
    NgIf,
    AsyncPipe,
    AplazoDynamicPipe,
    CartProductsTableComponent,
    ReactiveFormsModule,
    AgentIdComponent,
    AplazoIconComponent,
    AplazoButtonComponent,
    AplazoCardComponent,
    AplazoFormFieldDirectives,
    AplazoUppercaseDirective,
    AplazoLettersNumbersDirective,
    AplazoNoWhiteSpaceDirective,
    AplazoTruncateLengthDirective,
  ],
})
export class CartV1Component implements OnInit, OnDestroy {
  readonly #dialog = inject(DialogService);
  readonly #redirectionService = inject(RedirectionService);
  readonly #changeDetector = inject(ChangeDetectorRef);
  readonly #finishOrderUseCase = inject(FinishOrderUseCase);
  readonly #storeService = inject(StoreService);
  readonly #i18n = inject(I18NService);
  readonly #appRoutes = inject(POS_APP_ROUTES);
  readonly #registryIconService = inject(AplazoIconRegistryService);
  readonly #matchMedia = inject(AplazoMatchMediaService);
  readonly #eventService = inject(EventService);
  readonly #analyticsService = inject(AnalyticsService);
  readonly #route = inject(ActivatedRoute);
  readonly #flags = inject(StatsigService);
  readonly #finishWithPreloanUseCase = inject(FinishOrderWithPreloanUseCase);

  readonly #scope = 'cart';

  readonly #destroy$ = new Subject<void>();

  readonly agentIdControl = new FormControl<string>('');
  readonly preloanCodeControl = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.minLength(6), Validators.maxLength(6)],
  });

  isReadyToFinishOrder = false;

  readonly #products$ = new BehaviorSubject<IProductRequestUIDto[]>([]);
  readonly products$ = this.#products$.asObservable();

  readonly isMobileScreen$ = this.#matchMedia.matchLgScreen$.pipe(
    map(isLargeScreen => {
      return { isMobile: !isLargeScreen?.matches };
    })
  );
  readonly isSellAgentIdEnable$ =
    this.#storeService.selectedBranchFeatureFlags$.pipe(
      map(featureFlags => featureFlags?.isSellAgentTrackEnable || false)
    );

  readonly isPreloanEnabled$ = of(
    this.#flags.checkGate('b2b_front_posui_preloan_enabled')
  ).pipe(
    map(flag => {
      const isPreloanRoute =
        this.#route.snapshot.routeConfig?.path === ROUTE_CONFIG.preloanCart;

      return flag && isPreloanRoute;
    }),
    take(1)
  );

  readonly productsTableInjectedText$: Observable<ICartProductsTableInjectedTextUI> =
    this.#i18n.getTranslateObjectByKey<ICartProductsTableInjectedTextUI>({
      key: 'table',
      scope: this.#scope,
    });
  readonly totalPriceInjectedText$: Observable<{ label: string }> =
    this.#i18n.getTranslateObjectByKey<{ label: string }>({
      key: 'totalPrice',
      scope: this.#scope,
    });
  readonly agentIdTextTemplate$: Observable<IAgentIdTextTemplateUI> =
    this.#i18n.getTranslateObjectByKey<IAgentIdTextTemplateUI>({
      key: 'agentId',
      scope: this.#scope,
    });
  readonly productsFormTextTemplate$: Observable<IProductFormInjectedTextUI> =
    this.#i18n.getTranslateObjectByKey<IProductFormInjectedTextUI>({
      key: 'form',
      scope: this.#scope,
    });
  readonly actionsText$ = this.#i18n.getTranslateObjectByKey<{
    addProduct: { label: string };
    finishOrder: { label: string };
  }>({
    key: 'actions',
    scope: this.#scope,
  });
  readonly preloanText$ = this.#i18n.getTranslateObjectByKey<{
    label: string;
    errors: {
      minlengthError: string;
    };
  }>({
    key: 'preloan',
    scope: this.#scope,
    params: {
      errors: { minlengthError: { minLength: 6 } },
    },
  });

  get totalPrice(): number {
    return this.#products$
      .getValue()
      .reduce(
        (acc, curr) => acc + Number(curr.price) * Number(curr.quantity),
        0
      );
  }

  constructor() {
    this.#registryIconService.registerIcons([iconArrowLeft]);
  }

  setProducts(value: IProductRequestUIDto[]) {
    if (Array.isArray(value)) {
      this.#products$.next(value);

      return;
    }

    throw new Error("Can't set products to cart. Products must be an array.");
  }

  addProductsToCart(): void {
    this.productsFormTextTemplate$
      .pipe(
        take(1),

        tap(() => {
          this.#analyticsService.track('buttonClick', {
            buttonName: 'cartAddProduct',
          });
        }),

        switchMap(injectedText =>
          this.#dialog
            .open(ProductFormComponent, {
              data: {
                injectedText,
              },
              enableClose: false,
            })
            .afterClosed$.pipe(take(1))
        )
      )
      .subscribe(productOrUndefined => {
        if (!productOrUndefined) {
          return;
        }

        this.#analyticsService.track('buttonClick', {
          buttonName: 'cartSaveProduct',
        });

        this.setProducts([...this.#products$.getValue(), productOrUndefined]);

        window.scrollTo(0, 0);

        this.#eventService
          .addProduct({
            id: productOrUndefined.sku,
            name: productOrUndefined.name,
            price: +productOrUndefined.price,
            qty: productOrUndefined.quantity,
          })
          .subscribe();
      });
  }

  editProduct(product: IProductRequestUIDto): void {
    const indexCurrentProduct = this.#products$
      .getValue()
      .findIndex(prod => prod.productId === product.productId);

    this.productsFormTextTemplate$
      .pipe(
        take(1),

        tap(() => {
          this.#analyticsService.track('buttonClick', {
            buttonName: 'cartEditProduct',
          });
        }),

        switchMap(injectedText =>
          this.#dialog
            .open(ProductFormComponent, {
              data: {
                injectedText,
                product: { ...product },
              },
              enableClose: false,
            })
            .afterClosed$.pipe(take(1))
        )
      )
      .subscribe(productOrUndefined => {
        if (!!productOrUndefined && indexCurrentProduct >= 0) {
          const productsToReplace = [...this.#products$.getValue()];
          productsToReplace.splice(indexCurrentProduct, 1, productOrUndefined);

          this.setProducts(productsToReplace);

          this.#analyticsService.track('buttonClick', {
            buttonName: 'cartSaveEditProduct',
          });
        }
      });
  }

  deleteProduct(product: IProductRequestUIDto): void {
    const indexCurrentProduct = this.#products$
      .getValue()
      .findIndex(prod => prod.productId === product.productId);

    if (indexCurrentProduct >= 0) {
      const productsToReplace = [...this.#products$.getValue()];
      productsToReplace.splice(indexCurrentProduct, 1);

      this.setProducts(productsToReplace);
    }
  }

  async finishOrder(): Promise<void> {
    if (!this.isReadyToFinishOrder) {
      return;
    }

    const isPreloanRoute =
      this.#route.snapshot.routeConfig?.path === ROUTE_CONFIG.preloanCart;

    const isPreloanEnabled = this.#flags.checkGate(
      'b2b_front_posui_preloan_enabled'
    );

    if (isPreloanRoute && isPreloanEnabled) {
      this.#finishWithPreloanUseCase
        .execute({
          products: this.#products$.getValue(),
          sellsAgentId: this.agentIdControl.value ?? '',
          totalPrice: String(this.totalPrice),
          preloanCode: this.preloanCodeControl.value,
        })
        .pipe(take(1))
        .subscribe();
      return;
    }

    this.#finishOrderUseCase
      .execute({
        products: this.#products$.getValue(),
        sellsAgentId: this.agentIdControl.value ?? '',
        totalPrice: String(this.totalPrice),
      })
      .pipe(
        tap(() => {
          this.#analyticsService.track('buttonClick', {
            buttonName: 'cartProcessOrder',
          });
        }),
        take(1)
      )
      .subscribe();
  }

  goToOrdersRoute(): void {
    this.#redirectionService.internalNavigation([
      this.#appRoutes.aplazoRoot,
      this.#appRoutes.aplazoLayout,
      this.#appRoutes.securedOrders,
    ]);
  }

  ngOnInit(): void {
    this.setProducts([]);

    this.getReadyToFinishOrderStream()
      .pipe(takeUntil(this.#destroy$))
      .subscribe(isReadyToFinish => {
        this.isReadyToFinishOrder = isReadyToFinish;
      });

    this.#changeDetector.detectChanges();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  private getReadyToFinishOrderStream(): Observable<boolean> {
    return combineLatest([
      this.products$,
      this.#storeService.minimumOrderAmount$,
      this.isSellAgentIdEnable$,
      this.isPreloanEnabled$,
      this.preloanCodeControl.valueChanges.pipe(
        startWith(this.preloanCodeControl.value)
      ),
      // for the valueChanges we are not using the result stream
      // to calculate the mapped result
      // instead within the map we are using the reference to the
      // value of the form control
      // but it is importante leave this observable because
      // on every change we have a new emission of the stream
      this.agentIdControl.valueChanges.pipe(
        startWith(this.agentIdControl.value),
        debounceTime(450),
        tap(value => {
          if (value) {
            this.#analyticsService.track('search', {
              searchTerm: value,
              genericInfo: 'sellAgentId',
            });
          }
        })
      ),
    ]).pipe(
      map(
        ([
          products,
          minimumOrderAmountOrNull,
          isSellAgentIdEnable,
          isPreloanEnabled,
        ]) => {
          const minValidOrderAmount = minimumOrderAmountOrNull || 0;

          const hasHigherPriceThanMin = this.totalPrice >= minValidOrderAmount;

          const isIdValidAndEnabled =
            this.agentIdControl.value != null &&
            this.agentIdControl.value !== '' &&
            +this.agentIdControl.value > 0;

          const isIdDisabled = this.agentIdControl.value == null;

          const validPreloanCode = isPreloanEnabled
            ? this.preloanCodeControl.valid &&
              this.preloanCodeControl.value.length === 6
            : true;

          if (isSellAgentIdEnable) {
            return (
              products.length > 0 &&
              hasHigherPriceThanMin &&
              (isIdValidAndEnabled || isIdDisabled) &&
              validPreloanCode
            );
          }
          return (
            products.length > 0 && hasHigherPriceThanMin && validPreloanCode
          );
        }
      )
    );
  }
}
