<ng-container *ngIf="vm$ | async as context">
  <article class="px-4 lg:px-10 pt-5 pb-10 bg-dark-background">
    <h2 class="text-xl font-medium mb-3 uppercase">Resumen de hoy</h2>

    <aplz-ui-card appearance="flat" [dark]="true">
      <div class="grid gap-2 grid-cols-1 lg:grid-cols-3 md:grid-cols-2">
        <aplz-ui-stat-card
          *ngFor="let stat of context.stats; let even = even; let last = last"
          [I18nText]="{ statCardTitle: stat.statCardTitle }"
          [pipeName]="stat.pipeName ?? null"
          [principalAmount]="stat.value"
          [hasBorder]="
            (!last &&
              ['lg', 'xl', '2xl', '3xl', '4xl', '5xl'].includes(
                context.screenSize
              )) ||
            (even && ['md'].includes(context.screenSize))
          ">
        </aplz-ui-stat-card>
      </div>
    </aplz-ui-card>
  </article>

  <section
    class="px-4 lg:px-10 pb-10 bg-dark-background"
    *stgCheckGate="'b2b_front_posui_challenges'">
    <aplazo-challenges-banner></aplazo-challenges-banner>
  </section>

  <section class="px-4 lg:px-10 pb-[30vh]">
    <ng-container *ngIf="context.orders">
      <aplz-ui-card
        class="mt-6"
        appearance="flat"
        *ngIf="
          context.orders.length === 0 &&
            !searchControl.value &&
            orderTypeControl.value === 'Todos';
          else ordersList
        ">
        <aplz-ui-common-message
          [i18Text]="{
            title: context.emptyMessage.title,
            description: context.emptyMessage.description
          }"
          imgName="emptyLoans"
          *ngIf="context.emptyMessage">
          <aplz-ui-message-actions>
            <div class="grid gap-4">
              <button
                aplzButton
                aplzAppearance="solid"
                aplzColor="dark"
                size="md"
                class="mx-auto"
                (click)="newOrder()">
                {{ context.emptyMessage.button.label }}
              </button>

              <aplazo-preloan-cart-button
                class="mx-auto"
                *stgCheckGate="
                  'b2b_front_posui_preloan_enabled'
                "></aplazo-preloan-cart-button>
            </div>
          </aplz-ui-message-actions>
        </aplz-ui-common-message>
      </aplz-ui-card>

      <ng-template #ordersList>
        <div
          class="grid justify-center gap-y-4 w-full py-6 lg:py-0 lg:flex lg:items-center lg:gap-x-4 lg:flex-wrap">
          <div
            class="mx-auto lg:mx-0 lg:text-left w-[125px] flex-shrink-0 flex-grow-0 leading-[0rem]">
            <span class="inline-block font-medium text-lg truncate w-full">
              {{ context.orders.length | i18nPlural: context.counterLabel }}
            </span>
          </div>

          <div class="mx-auto">
            <aplz-ui-select [formControl]="orderTypeControl">
              <aplz-ui-option
                *ngFor="let opt of orderTypes"
                [ngValue]="opt"
                [label]="opt">
              </aplz-ui-option>
            </aplz-ui-select>
          </div>

          <div class="lg:mt-8 max-w-full md:max-w-[260px]">
            <aplz-ui-search
              [textUI]="context.searchbar"
              [formControl]="searchControl"
              [minLength]="minLength">
            </aplz-ui-search>
          </div>

          <div
            class="flex items-end justify-center lg:justify-end gap-x-4 flex-grow flex-shrink-0">
            <button
              aplzButton
              size="sm"
              aplzAppearance="stroked"
              [disabled]="context.isLoadingReport === true"
              (click)="refreshOrdersList()">
              <aplz-ui-icon name="arrow-path" size="md"></aplz-ui-icon>
            </button>

            <button
              aplzButton
              aplzAppearance="solid"
              aplzColor="dark"
              size="md"
              (click)="newOrder()">
              {{ context.newOrderLabelText.buttonLabel }}
            </button>
            <aplazo-preloan-cart-button
              *stgCheckGate="
                'b2b_front_posui_preloan_enabled'
              "></aplazo-preloan-cart-button>
            <button
              aplzButton
              aplzAppearance="solid"
              aplzColor="dark"
              size="md"
              *aplazoPosUiBranchFf="'isTodayReportEnable'"
              [disabled]="context.isLoadingReport === true"
              (click)="generateNewReport()">
              Reporte Del Día
            </button>
          </div>
        </div>

        <ng-container
          *ngIf="
            context.orders.length === 0 &&
              (searchControl.value || orderTypeControl.value !== 'Todos');
            else list
          ">
          <aplz-ui-card
            class="mt-6"
            [appearance]="
              ['xs', 'sm', 'md'].includes(context.screenSize)
                ? 'borderless'
                : 'flat'
            ">
            <div class="pt-14">
              <aplz-ui-common-message [i18Text]="context.emptySearch">
              </aplz-ui-common-message>
            </div>
          </aplz-ui-card>
        </ng-container>
      </ng-template>

      <ng-template #list>
        <div class="w-full mt-6">
          <aplazo-orders-table
            [orders]="context.orders"
            [i18text]="context.ordersTableInjectedText"
            (cancelOrder)="cancelOrder($event)"
            (shareOrder)="sharePaymentLink($event)"
            (checkOrderStatus)="checkOrderStatus($event)"
            (showRefundInfo)="showRefundInfo($event)"></aplazo-orders-table>
        </div>
      </ng-template>
    </ng-container>
  </section>
</ng-container>
