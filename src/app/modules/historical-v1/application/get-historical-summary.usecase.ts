import { Injectable } from '@angular/core';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, map, of, take } from 'rxjs';
import { PosOfflineRepository } from '../../../core/domain/repositories/pos-offline.repository';
import { DateMapper } from '../date.mapper';

const emptySummary: {
  totalOrder: number;
  totalSale: number;
  avgPrice: number;
} = {
  totalOrder: 0,
  totalSale: 0,
  avgPrice: 0,
};

@Injectable()
export class GetHistoricalSummaryUseCase {
  constructor(
    private readonly offlineService: PosOfflineRepository,
    private readonly loader: LoaderService,
    private readonly dateMapper: DateMapper,
    private readonly errorHandler: UseCaseErrorHandler
  ) {}

  execute(dateRange: { startDate: Date; endDate: Date }): Observable<{
    totalOrder: number;
    totalSale: number;
    avgPrice: number;
  }> {
    const idLoader = this.loader.show();

    try {
      const dates =
        this.dateMapper.fromDateRangeToRawDateYearFistWithHyphen(dateRange);

      return this.offlineService.getHistoricalSummaryByDate(dates).pipe(
        map(sum => {
          if (!sum) {
            return emptySummary;
          }

          return sum;
        }),
        catchError(error => this.errorHandler.handle<never>(error)),
        take(1),
        finalize(() => {
          this.loader.hide(idLoader);
        })
      );
    } catch (error) {
      this.loader.hide(idLoader);
      return this.errorHandler.handle(error, of(emptySummary));
    }
  }
}
