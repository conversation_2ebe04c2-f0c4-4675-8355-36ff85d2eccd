import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';

export type NotificationType = 'PROMO' | 'INFO' | 'FEATURE' | 'TRAINING';

export type NotificationStatus = 'A' | 'I'; // Active, Inactive

export type NotificationPlatform = 'P' | 'D'; // POS, Dashboard

export type MediaContentTags = 'CUSTOMER' | 'SALES' | 'FAQ';

export enum MappedMediaContentTags {
  'CUSTOMER' = 'Clientes',
  'SALES' = 'Ventas',
  'FAQ' = 'Preguntas Frecuentes',
}

export interface INotificationsWithNewOnesUIDto {
  notifications: Map<string, NotificationUI[]>;
  hasNewOnes: boolean;
  newOnesCounter: number;
  type: NotificationType;
  newOnesIds: number[];
}

export interface INotificationResponseDto {
  id: number;
  commType: string;
  title: string;
  description: string;
  status: string;
  platform: NotificationPlatform;
  merchants: string;
  lastUpdate: string; // Date ISO string
  applyAll: boolean;
  isNewOne?: boolean;
  complement?: IMediaContentResponseDto;
}

export interface CreateNotificationDto
  extends Omit<INotificationResponseDto, 'lastUpdate'> {
  lastUpdate: Date | null;
}

export interface ITrainingMediaContentUIDto {
  tags: MappedMediaContentTags[];
  content: IMediaContentUIDto[];
}

export interface IMediaContentResponseDto {
  urlVideo: string;
  urlThumbnail: string;
  tags: MediaContentTags[];
}

export interface IMediaContentUIDto {
  id: number;
  title: string;
  description: string;
  isValidVideoUrl: boolean;
  urlVideo: string;
  urlThumbnail: string;
  tags: MappedMediaContentTags[];
}

export class NotificationUI {
  readonly id: number;
  readonly commType: NotificationType;
  readonly status: NotificationStatus;
  readonly platform: NotificationPlatform;
  readonly title: string;
  readonly description: string;
  readonly merchants: number[];
  readonly applyAll: boolean;
  readonly lastUpdate: Date;
  readonly lastUpdateTimestamp: number;
  readonly isNewOne: boolean;
  readonly complement: IMediaContentResponseDto | undefined;

  private constructor(promo: NotificationUI) {
    this.id = promo.id;
    this.commType = promo.commType;
    this.status = promo.status;
    this.platform = promo.platform;
    this.title = promo.title;
    this.description = promo.description;
    this.merchants = promo.merchants;
    this.applyAll = promo.applyAll;
    this.lastUpdate = promo.lastUpdate;
    this.lastUpdateTimestamp = promo.lastUpdateTimestamp;
    this.isNewOne = promo.isNewOne;
    this.complement = promo.complement;
  }

  public static create(args: CreateNotificationDto): NotificationUI {
    Guard.againstInvalidNumbers(
      Number(args.id),
      'NotificationUI::create',
      'El id de la notificación no es un número'
    );

    if (Number(args.id) < 0) {
      throw new RuntimeMerchantError(
        'El id de la notificación no puede ser negativo',
        'NotificationUI::create::lessThanZero'
      );
    }

    if (
      args.commType !== 'PROMO' &&
      args.commType !== 'INFO' &&
      args.commType !== 'TRAINING' &&
      args.commType !== 'FEATURE'
    ) {
      throw new RuntimeMerchantError(
        'El tipo de notificación no es válido',
        'NotificationUI::create::notificationType::unknown'
      );
    }

    if (args.status !== 'A' && args.status !== 'I') {
      throw new RuntimeMerchantError(
        'El estado de la notificación no es válido',
        'NotificationUI::create::notificationStatus::unknown'
      );
    }

    if (args.platform !== 'P' && args.platform !== 'D') {
      throw new RuntimeMerchantError(
        'La plataforma de la notificación no es válida',
        'NotificationUI::create::notificationPlatform::unknown'
      );
    }

    const parsedMerchants: number[] = Object.prototype.hasOwnProperty.call(
      args,
      'merchants'
    )
      ? args.merchants.split(',').map(item => (isNaN(+item) ? 0 : +item))
      : [];

    const date = args.lastUpdate;

    Guard.againstInvalidDate({
      date: date,
      errorMessage: 'La fecha de la notificación no es válida',
      origin: 'NotificationUI::create::lastUpdate::invalidDate',
    });

    return new NotificationUI({
      id: +args.id,
      commType: args.commType,
      status: args.status,
      platform: args.platform,
      title: args.title,
      description: args.description,
      merchants: parsedMerchants,
      applyAll: args.applyAll,
      lastUpdate: date as Date,
      lastUpdateTimestamp: (date as Date).getTime(),
      isNewOne: args.isNewOne ?? false,
      complement: args.complement,
    });
  }
}
