import { Async<PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import {
  ReactiveFormsModule,
  UntypedFormControl,
  Validators,
} from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { LoaderService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { UpdateMerchantConfigDTO } from '../../core/domain/dtos';
import { NewBranchAddUseCase } from './application/new-branch-add.usecase';
import { NewBranchLogoutUseCase } from './application/new-branch-logout.usecase';

export interface NewBranchTextTemplate {
  title: string;
  legend: string;
  input: { placeholder: string; label: string; errorRequired: string };
  actions: {
    cancel: string;
    save: string;
  };
}

@Component({
  standalone: true,
  selector: 'aplazo-new-branch',
  imports: [
    AplazoButtonComponent,
    AplazoLogoComponent,
    AplazoFormFieldDirectives,
    NgIf,
    ReactiveFormsModule,
    AsyncPipe,
  ],
  templateUrl: './new-branch.component.html',
})
export class NewBranchComponent implements OnInit {
  readonly #loader = inject(LoaderService);
  readonly #addBranchUseCase = inject(NewBranchAddUseCase);
  readonly #logoutUseCase = inject(NewBranchLogoutUseCase);
  readonly #i18n = inject(I18NService);

  readonly injectedText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    legend: string;
    input: {
      label: string;
      placeholder: string;
      errorRequired: string;
    };
    actions: {
      save: string;
      cancel: string;
    };
  }>({
    key: 'content',
    scope: 'new-branch',
  });

  readonly branchNameControl = new UntypedFormControl('', [
    Validators.required,
  ]);

  saveBranch(): void {
    if (this.branchNameControl.valid) {
      const newBranch: UpdateMerchantConfigDTO = {
        branches: [this.branchNameControl.value],
        LogoUrl: '',
      };

      this.#addBranchUseCase.execute(newBranch);

      this.branchNameControl.reset();
    }
  }

  logOut(): void {
    this.#logoutUseCase.execute();
  }

  ngOnInit(): void {
    this.#loader.forceHide();
  }

  private getNewBranchTextTemplate(): NewBranchTextTemplate {
    return {
      title: 'NEW-BRANCH.TITLE',
      legend: 'NEW-BRANCH.LEGEND',
      input: {
        placeholder: 'NEW-BRANCH.INPUT.PLACEHOLDER',
        label: 'NEW-BRANCH.INPUT.LABEL',
        errorRequired: 'ERRORS.INPUTS.required',
      },
      actions: {
        cancel: 'NEW-BRANCH.ACTIONS.CANCEL',
        save: 'NEW-BRANCH.ACTIONS.SAVE',
      },
    };
  }
}
