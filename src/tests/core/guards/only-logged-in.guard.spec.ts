import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { lastValueFrom } from 'rxjs';
import { StoreService } from '../../../app/core/application/services/store.service';
import { OnlyLoggedInGuard } from '../../../app/core/guards/only-logged-in.guard';
import { LocalStore } from '../services/impl/local-store.service';

const routerSpy = jasmine.createSpyObj('Router', ['parseUrl']);

describe('OnlyLoggedInGuard', () => {
  let guard: OnlyLoggedInGuard;
  let store: StoreService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: StoreService,
          useClass: LocalStore,
        },
        {
          provide: Router,
          useValue: routerSpy,
        },
      ],
    });

    store = TestBed.inject(StoreService);
    guard = TestBed.inject(OnlyLoggedInGuard);

    store.setToken('token');
  });

  afterEach(() => {
    routerSpy.parseUrl.calls.reset();
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should allow navigation when isLoggedIn$ streams true', async () => {
    await lastValueFrom(guard.canActivate()).then(result => {
      expect(routerSpy.parseUrl).not.toHaveBeenCalled();
      expect(result).toBeTruthy();
    });
  });

  it('should deny navigation when isLoggedIn$ streams false', async () => {
    store.setToken(null);
    await lastValueFrom(guard.canActivate()).then(result => {
      expect(routerSpy.parseUrl).toHaveBeenCalledTimes(1);
      expect(result).toBeFalsy();
    });
  });
});
