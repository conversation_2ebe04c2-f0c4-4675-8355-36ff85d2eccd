<section class="w-full px-4 lg:px-8 pt-4 pb-[30vh]">
  <header class="mb-2">
    <button
      aplzButton
      aplzAppearance="basic"
      size="lg"
      (click)="goToOrdersRoute()">
      <aplz-ui-icon name="arrow-left" size="sm"> </aplz-ui-icon>
      <span class="ml-2"> Regresar </span>
    </button>
  </header>

  <main class="">
    <aplazo-cart-products-table
      *ngIf="productsTableInjectedText$ | async as productsTableInjectedText"
      [products]="(products$ | async)!"
      [i18Text]="productsTableInjectedText"
      (editEvent)="editProduct($event)"
      (deleteEvent)="deleteProduct($event)">
    </aplazo-cart-products-table>

    <aplz-ui-card
      class="my-6 text-right"
      appearance="flat"
      *ngIf="totalPriceInjectedText$ | async as totalPriceInjectedText">
      <span class="text-base font-medium uppercase mr-4">
        {{ totalPriceInjectedText.label }}
      </span>

      <span class="font-semibold text-lg">
        {{ totalPrice | aplzDynamicPipe: 'currency' }}
      </span>
    </aplz-ui-card>
  </main>

  <footer
    class="flex flex-wrap justify-end py-6"
    [class.desktop]="((isMobileScreen$ | async)?.isMobile ?? false) === true">
    <ng-container *ngIf="(isSellAgentIdEnable$ | async) === true">
      <div class="mb-8 flex-1 min-w-[220px]">
        <aplazo-agent-id
          *ngIf="agentIdTextTemplate$ | async as agentIdTextTemplate"
          [injectedText]="agentIdTextTemplate"
          [formControl]="agentIdControl"></aplazo-agent-id>
      </div>
    </ng-container>
    <ng-container *ngIf="(isPreloanEnabled$ | async) === true">
      <div class="mb-8 flex-1 min-w-[220px]">
        <ng-container *ngIf="preloanText$ | async as preloanText">
          <aplz-ui-form-field>
            <aplz-ui-form-label>
              {{ preloanText.label }}
            </aplz-ui-form-label>
            <input
              aplazoLettersNumbers
              aplazoUppercase
              aplazoNoWhiteSpace
              [aplazoTruncateLength]="6"
              type="text"
              aplzFormInput
              [formControl]="preloanCodeControl" />
            <p
              *ngIf="
                preloanCodeControl.touched &&
                (preloanCodeControl.hasError('minlength') ||
                  preloanCodeControl.hasError('maxlength'))
              "
              aplzFormError>
              {{ preloanText.errors.minlengthError }}
            </p>
          </aplz-ui-form-field>
        </ng-container>
      </div>
    </ng-container>

    <div
      class="flex-1 flex flex-wrap justify-center items-start md:justify-end gap-4">
      <ng-container *ngIf="actionsText$ | async as actionsText">
        <button
          aplzButton
          aplzAppearance="stroked"
          size="md"
          (click)="addProductsToCart()">
          {{ actionsText.addProduct.label }}
        </button>

        <button
          aplzButton
          aplzAppearance="solid"
          size="md"
          aplzColor="dark"
          [disabled]="!isReadyToFinishOrder"
          (click)="finishOrder()">
          {{ actionsText.finishOrder.label }}
        </button>
      </ng-container>
    </div>
  </footer>
</section>
