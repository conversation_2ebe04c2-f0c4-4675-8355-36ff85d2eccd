import { HttpErrorResponse } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import {
  EMPTY,
  MonoTypeOperatorFunction,
  OperatorFunction,
  Subject,
  catchError,
  map,
  of,
  pipe,
  retry,
  scan,
  shareReplay,
  switchMap,
  withLatestFrom,
} from 'rxjs';
import { Order } from 'src/app/core/domain/order.interface';
import { PosOfflineRepository } from 'src/app/core/domain/repositories/pos-offline.repository';
import { StoreService } from '../../../../core/application/services/store.service';
import { RuntimePosOfflineError } from '../../../../core/domain/runtime-error';
import { OrdersWithSSE, SSEPosEvent } from './orders.repository';

@Injectable({ providedIn: 'root' })
export class GetOrdersWithSSEService {
  readonly #sseRepository = inject(OrdersWithSSE);
  readonly #posService = inject(PosOfflineRepository);
  readonly #store = inject(StoreService);

  readonly #updater$ = new Subject<void>();

  orders$ = this.#updater$.pipe(
    switchMap(() => this.#posService.getTodayOrders()),
    this.#getEvents(),
    shareReplay(1)
  );

  refresh(): void {
    this.#updater$.next();
  }

  dispose(): void {
    this.#sseRepository.close();
  }

  #getEvents(): MonoTypeOperatorFunction<Order[]> {
    return pipe(
      withLatestFrom(this.#store.selectedBranch$),
      switchMap(([orders, branch]) =>
        this.#sseRepository.listen(branch?.id ?? 0).pipe(
          this.#parsedResponse(orders, branch?.id ?? 0),

          retry({
            resetOnSuccess: true,
            delay: err => {
              /** Handle timeout usecase and complete when the
               * error is untracked
               */
              if (
                err instanceof RuntimePosOfflineError &&
                err.code === 'SSE::ConnectionError'
              ) {
                return of(true);
              }
              if (err instanceof HttpErrorResponse && err.status === 0) {
                return of(true);
              }

              return EMPTY;
            },
          }),

          catchError(err => {
            console.warn('GetOrdersWithSSEUseCase::Unknown');
            console.error(err);

            return EMPTY;
          })
        )
      )
    );
  }

  #parsedResponse(
    orders: Order[],
    branchId: number
  ): OperatorFunction<SSEPosEvent, Order[]> {
    return pipe(
      map<
        SSEPosEvent,
        {
          order: Partial<Order>;
          eventType: 'created' | 'activo' | 'delete';
        } | null
      >(({ message, error }) => {
        if (error) {
          throw new RuntimePosOfflineError(
            'Error de conexión con el servidor',
            'SSE::ConnectionError',
            'GetOrdersWithSSEService'
          );
        }

        if (!message) {
          return null;
        }

        if (message.data === 'heartbeat') {
          return null;
        }

        if (message.type === 'message' && message.data) {
          try {
            const data = JSON.parse(message.data);

            const isCreateEvent = Object.prototype.hasOwnProperty.call(
              data,
              'order'
            );
            const isDeleteEvent = Object.prototype.hasOwnProperty.call(
              data,
              'event'
            );
            const isActivoEvent = Object.prototype.hasOwnProperty.call(
              data,
              'status'
            );

            if (isCreateEvent) {
              return {
                order: data.order,
                eventType: 'created',
              };
            }

            if (isDeleteEvent) {
              return {
                order: {
                  // The received data is the loan ID of the order **NOT loanId**
                  id: data.orderId,
                },
                eventType: 'delete',
              };
            }

            if (isActivoEvent) {
              return {
                order: {
                  // CAUTION: The received data is the loanId <--
                  id: data.orderId,
                },
                eventType: 'activo',
              };
            }

            return null;
          } catch (error) {
            console.warn('GetOrdersWithSSEUseCase::JSONParseError');

            return null;
          }
        }

        return null;
      }),

      scan(
        (
          state: Order[],
          current: {
            order: Partial<Order>;
            eventType: 'created' | 'activo' | 'delete';
          } | null
        ) => {
          if (!current) {
            return state;
          }

          let cloned = [...state];

          if (current.eventType === 'created') {
            const idx = cloned.findIndex(
              order => order.id === current.order?.id
            );
            if (idx === -1) {
              cloned = [...cloned, current.order as Order];
            }
          }

          if (current.eventType === 'delete') {
            const idx = cloned.findIndex(
              order => order.id === current.order?.id
            );
            if (idx !== -1) {
              cloned = cloned.filter(order => order.id !== current.order?.id);
            }
          }

          if (current.eventType === 'activo') {
            const idx = cloned.findIndex(
              order => order.loanId === current.order?.id
            );
            if (idx !== -1) {
              const modifiedOrder = { ...cloned[idx] };
              modifiedOrder.status = 'Activo';

              const transitive = [...cloned];
              transitive.splice(idx, 1, modifiedOrder);

              cloned = [...transitive];
            }
          }
          return cloned;
        },
        [...orders]
      ),

      map(orders =>
        orders
          .filter(order => order.branchId === branchId)
          .sort((a, b) => b.id - a.id)
      )
    );
  }
}
