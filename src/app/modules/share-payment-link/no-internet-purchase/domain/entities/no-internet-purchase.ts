export const NO_INTERNET_PURCHASE_ERRORS = {
  ocl0001: 'APZ_OCL_0001',
  ocl0002: 'APZ_OCL_0002',
  ocl0003: 'APZ_OCL_0003',
  ocl0004: 'APZ_OCL_0004',
  ocl0005: 'APZ_OCL_0005',
  ocl0006: 'APZ_OCL_0006',
  ocl0007: 'APZ_OCL_0007',
  ocl0008: 'APZ_OCL_0008',
} as const;

export const NO_INTERNET_PURCHASE_ERROR_MESSAGES = {
  APZ_OCL_0001:
    'No podemos generar un código para este pedido. Por favor, genera uno nuevo y vuelve a intentarlo.',
  APZ_OCL_0002:
    'No podemos generar un código para este pedido. Por favor, genera uno nuevo y vuelve a intentarlo.',
  APZ_OCL_0003:
    'Este número no pertenece a un usuario Aplazo. Por favor, introduce uno válido.',
  APZ_OCL_0004: '<PERSON><PERSON><PERSON> expirado. Intente con una nueva orden.',
  APZ_OCL_0005: 'Código bloqueado. Intente con una nueva orden.',
  APZ_OCL_0006:
    'Error al procesar la transacción. Intenta con las opciones de QR o Teléfono.',
  APZ_OCL_0007: 'Código Invalido. Intente nuevamente o con un nuevo código.',
  APZ_OCL_0008: 'Código usado. Intente con una nueva orden.',
} as const;

export type NoInternetPurchaseErrorKey =
  keyof typeof NO_INTERNET_PURCHASE_ERRORS;
export type NoInternetPurchaseError =
  (typeof NO_INTERNET_PURCHASE_ERRORS)[NoInternetPurchaseErrorKey];
export type NoInternetPurchaseErrorMessage =
  (typeof NO_INTERNET_PURCHASE_ERROR_MESSAGES)[NoInternetPurchaseError];

export interface NoInternetPurchaseOTPRequest {
  phoneNumber: string;
  loanId: number;
}
export interface NoInternetPurchaseValidateOTPRequest
  extends NoInternetPurchaseOTPRequest {
  code: string;
  sessionId?: string;
}
