import { Injectable } from '@angular/core';
import { LoaderService } from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, map, of } from 'rxjs';
import { RuntimePosOfflineError } from '../../../../core/domain/runtime-error';
import { Refund } from '../../domain/entities/refund';
import { RefundsRepository } from '../../domain/repositories/refunds.repository';

@Injectable({ providedIn: 'root' })
export class GetRefundInfoUsecase {
  constructor(
    private repository: RefundsRepository,
    private loader: LoaderService
  ) {}

  execute(loanId: string): Observable<Refund | null> {
    const idLoader = this.loader.show();

    return this.repository.getInfoById(loanId).pipe(
      map(refs => {
        if (refs.length === 0) {
          throw new RuntimePosOfflineError(
            'Orden sin reembolsos',
            'RefundInfo::emptyRefunds',
            'get-refunds-error'
          );
        }
        return refs[0];
      }),

      catchError((error: unknown) => {
        if (error instanceof RuntimePosOfflineError) {
          console.warn(error.code);
        } else {
          console.error(error);
        }

        return of(null);
      }),

      finalize(() => {
        this.loader.hide(idLoader);
      })
    );
  }
}
