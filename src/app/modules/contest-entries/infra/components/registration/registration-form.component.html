<div class="mt-10 mb-6">
  <aplz-ui-logo size="md"></aplz-ui-logo>
</div>

<form [formGroup]="form" class="py-4 px-3" (ngSubmit)="save()">
  <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
    <aplz-ui-form-field>
      <aplz-ui-form-label>Nombre y Apellido</aplz-ui-form-label>
      <input
        aplzFormInput
        aplazoTrimSpaces
        aplazoOnlyLetters
        formControlName="fullName"
        id="full-name"
        type="text" />
      <p
        aplzFormError
        *ngIf="
          fullName.touched && fullName.invalid && fullName.hasError('required')
        ">
        Por favor, introduce un nombre valido.
      </p>
      <p
        aplzFormError
        *ngIf="
          fullName.touched &&
          fullName.invalid &&
          !fullName.hasError('required') &&
          fullName.hasError('minlength')
        ">
        Por favor, introduce un nombre de 2 caracteres o más.
      </p>
    </aplz-ui-form-field>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
    <aplz-ui-form-field>
      <aplz-ui-form-label>Número Telefónico</aplz-ui-form-label>
      <input
        aplzFormInput
        aplazoOnlyNumbers
        [aplazoTruncateLength]="10"
        formControlName="phone"
        type="text"
        inputMode="tel"
        id="phone" />
      <p
        aplzFormError
        *ngIf="phone.touched && phone.invalid && phone.hasError('required')">
        Este campo es requerido
      </p>
      <p
        aplzFormError
        *ngIf="
          phone.touched &&
          phone.invalid &&
          (phone.hasError('minlength') || phone.hasError('maxlength'))
        ">
        Por favor introduce un numero de teléfono valido.
      </p>
      <p aplzFormError *ngIf="phone.hasError(this.phoneAsyncErrorKey)">
        El número de teléfono suministrado ya se encuentra registrado. Por favor
        introduce un nuevo número telefónico para el registro
      </p>
    </aplz-ui-form-field>

    <aplz-ui-form-field [hideRequiredMarker]="true">
      <aplz-ui-form-label>Correo electrónico</aplz-ui-form-label>
      <input
        aplzFormInput
        aplazoNoWhiteSpace
        aplazoLowercase
        formControlName="email"
        type="text"
        inputMode="email"
        id="email" />
      <p
        aplzFormError
        *ngIf="email.touched && email.invalid && email.hasError('email')">
        El correo electrónico suministrado no tiene un formato valido.
      </p>
    </aplz-ui-form-field>
  </div>
  <div class="grid grid-cols-1 md:grid-cols-2 gap-x-4">
    <fieldset
      class="flex flex-col justify-center border-dark-secondary border rounded-lg -mt-2 h-fit mb-8"
      [class.border-special-danger]="region.touched && region.invalid">
      <legend
        class="select-none text-sm font-light text-dark px-2 ml-3"
        [class.text-special-danger]="region.touched && region.invalid">
        Región *
      </legend>
      <aplz-ui-select formControlName="region" class="-mt-2">
        <aplz-ui-option
          [ngValue]="SELECTION_LABEL"
          [label]="SELECTION_LABEL"></aplz-ui-option>
        <aplz-ui-option
          *ngFor="let item of regionsList$ | async"
          [ngValue]="item.state_c_code"
          [label]="item.state_name"></aplz-ui-option>
      </aplz-ui-select>
      <div class="relative h-0">
        <span
          class="absolute text-special-danger text-sm ml-2 top-0 left-0"
          *ngIf="
            region.touched &&
            region.invalid &&
            region.hasError('invalidBadRegion')
          ">
          Por favor selecciona una opción de la lista
        </span>
      </div>
    </fieldset>

    <fieldset
      class="flex gap-2 items-center border-dark-secondary border rounded-lg -mt-2 h-fit mb-8">
      <legend class="select-none text-sm font-light text-dark px-2 ml-3">
        Puesto
      </legend>
      <aplz-ui-select formControlName="role" class="-mt-2">
        <aplz-ui-option
          [ngValue]="SELECTION_LABEL"
          [label]="SELECTION_LABEL"></aplz-ui-option>
        <aplz-ui-option
          *ngFor="let item of roleLabels"
          [ngValue]="item"
          [label]="item"></aplz-ui-option>
      </aplz-ui-select>
      <div class="relative h-0">
        <span
          class="absolute text-special-danger text-sm ml-2 top-0 left-0"
          *ngIf="
            region.touched && region.invalid && region.hasError('invalidRole')
          ">
          Por favor selecciona una opción de la lista
        </span>
      </div>
    </fieldset>
  </div>

  <div class="flex justify-end gap-x-6 flex-wrap">
    <button
      aplzButton
      type="reset"
      aplzAppearance="stroked"
      aplzColor="light"
      size="md"
      (click)="cancel()"
      [disabled]="(isLoading$ | async) === true">
      <span class="mr-2"> Cancelar </span>
      <aplz-ui-icon
        name="spin-circle"
        size="sm"
        *ngIf="(isLoading$ | async) === true"></aplz-ui-icon>
    </button>
    <button
      aplzButton
      type="submit"
      aplzAppearance="solid"
      aplzColor="dark"
      size="md"
      [disabled]="(isLoading$ | async) === true">
      <span class="mr-2">
        {{ (isLoading$ | async) === true ? 'Enviando...' : 'Enviar' }}
      </span>
      <aplz-ui-icon
        name="spin-circle"
        size="sm"
        *ngIf="(isLoading$ | async) === true"></aplz-ui-icon>
    </button>
  </div>
</form>
