import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import {
  Component,
  Input,
  OnDestroy,
  TemplateRef,
  ViewChild,
  inject,
} from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { TransactionBehaviorService } from '@aplazo/front-analytics/kount';
import { I18NService } from '@aplazo/i18n';
import { NotifierService } from '@aplazo/merchant/shared';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoDetailsComponent } from '@aplazo/shared-ui/details';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconSpinCircle } from '@aplazo/ui-icons';
import { DialogRef } from '@ngneat/dialog';
import { NgxMaskDirective } from 'ngx-mask';
import {
  BehaviorSubject,
  EMPTY,
  Subject,
  catchError,
  combineLatestWith,
  finalize,
  map,
  of,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { DynamicTokenService } from './dynamic-token.service';
import { DynamicTokenErrorsService } from './errors.service';
import { PaymentsResponseUIDto } from './payment';

const STEPS = [
  'getCalendarToken',
  'finishLoanToken',
  'calendar',
  'message',
] as const;

type Step = (typeof STEPS)[number];

@Component({
  standalone: true,
  selector: 'aplazo-dynamic-token-container',
  imports: [
    ReactiveFormsModule,
    AplazoFormFieldDirectives,
    NgTemplateOutlet,
    NgIf,
    NgFor,
    AsyncPipe,
    AplazoButtonComponent,
    AplazoIconComponent,
    AplazoCardComponent,
    AplazoDetailsComponent,
    AplazoDynamicPipe,
    NgxMaskDirective,
  ],
  template: `
    <div class="my-4 mx-0 md:mx-2">
      <ng-container [ngTemplateOutlet]="content$ | async"> </ng-container>
    </div>

    <div class="flex items-center justify-around my-6">
      <button
        aplzButton
        size="md"
        aplzAppearance="stroked"
        aplzColor="light"
        type="button"
        [disabled]="isLoading"
        (click)="cancel()">
        Cancelar
      </button>

      <button
        aplzButton
        size="md"
        aplzAppearance="solid"
        aplzColor="dark"
        type="button"
        [disabled]="isLoading || hasError || hasFormError"
        (click)="changeStep()">
        Continuar
      </button>
    </div>

    <ng-template #tokenForm>
      <form [formGroup]="dynamicTokenForm">
        <h2 class="text-base text-dark-secondary py-4 text-center my-2">
          {{
            (tokenTexUI$ | async)?.title ??
              'Ingrese el código generado en la aplicación del cliente'
          }}
        </h2>

        <aplz-ui-form-field>
          <aplz-ui-form-label> Teléfono </aplz-ui-form-label>
          <input
            type="text"
            aplzFormInput
            [formControl]="customerIdentifier"
            placeholder="00 0000 0000"
            mask="99 9999 9999"
            inputmode="tel" />
        </aplz-ui-form-field>

        <aplz-ui-form-field>
          <aplz-ui-form-label> Token </aplz-ui-form-label>
          <input
            type="text"
            aplzFormInput
            [formControl]="dynamicToken"
            placeholder="000 000"
            mask="999 999"
            inputmode="tel" />
        </aplz-ui-form-field>
      </form>
    </ng-template>
    <ng-template #loading>
      <div class="flex items-center justify-center py-6">
        <aplz-ui-icon name="spin-circle" size="xl" [spin]="true"></aplz-ui-icon>
      </div>
    </ng-template>
    <ng-template #calendar>
      <section
        class="my-4 mx-0 md:mx-2"
        *ngIf="paymentResponseUI$ | async as response">
        <aplz-ui-card>
          <aplz-ui-details>
            <summary aplzDetailsHeader>
              <div class="flex">
                <span class="ml-7 text-lg font-medium">
                  Detalles de compra
                </span>
              </div>
            </summary>

            <p
              class="flex items-center my-4 mx-0 md:mx-4 hover:text-dark-background">
              <span
                class="text-dark-secondary flex-grow flex-shrink-0 min-w-[150px]">
                Subtotal(IVA incluido)
              </span>

              <span
                class="flex-1 text-right font-medium text-dark text-lg tabular-nums">
                {{ response.purchase_amount | aplzDynamicPipe: 'currency' }}
              </span>
            </p>

            <p
              class="flex items-center my-4 mx-0 md:mx-4 hover:text-dark-background">
              <span
                class="text-dark-secondary flex-grow flex-shrink-0 min-w-[150px]">
                Comisión por servicio
              </span>

              <span
                class="flex-1 text-right font-medium text-dark text-lg tabular-nums">
                {{
                  response.schemes.interest_amount | aplzDynamicPipe: 'currency'
                }}
              </span>
            </p>

            <p
              class="flex items-center my-4 mx-0 md:mx-4 hover:text-dark-background">
              <span
                class="font-medium text-dark flex-grow flex-shrink-0 min-w-[150px]">
                Total de tu compra
              </span>

              <span
                class="flex-1 text-right font-medium text-dark text-lg tabular-nums">
                {{ response.schemes.total | aplzDynamicPipe: 'currency' }}
              </span>
            </p>
          </aplz-ui-details>
        </aplz-ui-card>
        <p class="my-3"></p>
        <aplz-ui-card>
          <ul class="w-full flex flex-col gap-y-3 ">
            <li
              class="flex items-center mx-4 hover:bg-dark-background"
              *ngFor="
                let installment of response.schemes.calendar;
                first as fst
              ">
              <span class="text-dark-secondary flex-grow flex-shrink-0">
                {{
                  fst
                    ? 'Paga hoy'
                    : (installment | aplzDynamicPipe: 'shortDate')
                }}
              </span>

              <span
                class="flex-1 text-right font-medium text-dark-black text-lg tabular-nums">
                {{
                  response.schemes.total_first_installment
                    | aplzDynamicPipe: 'currency'
                }}
              </span>
            </li>
          </ul>
        </aplz-ui-card>
      </section>
    </ng-template>

    <ng-template #message>
      <section class="my-4 mx-2">
        <aplz-ui-card *ngIf="error$ | async as error">
          <div class="m-4">
            <h2 class="font-medium text-lg text-dark-secondary text-center">
              {{ error.title }}
            </h2>

            <p class="text-dark text-base text-center">{{ error.message }}</p>
          </div>
        </aplz-ui-card>
      </section>
    </ng-template>
  `,
})
export class DynamicTokenComponent implements OnDestroy {
  readonly #dialogRef: DialogRef<any, any> = inject(DialogRef);
  readonly #paymentsService = inject(DynamicTokenService);
  readonly #registerIcon = inject(AplazoIconRegistryService);
  readonly #errorsService = inject(DynamicTokenErrorsService);
  readonly #i18n = inject(I18NService);
  readonly #kountService = inject(TransactionBehaviorService);
  readonly #notifier = inject(NotifierService);
  readonly #scope = 'errors';

  @Input()
  loanId: number | undefined;

  @ViewChild('tokenForm', { read: TemplateRef, static: true })
  _tokenFormTemp!: TemplateRef<any>;

  @ViewChild('loading', { read: TemplateRef, static: true })
  _loadingTemp!: TemplateRef<any>;

  @ViewChild('calendar', { read: TemplateRef, static: true })
  _calendarTemp!: TemplateRef<any>;

  @ViewChild('message', { read: TemplateRef, static: true })
  _messageTemp!: TemplateRef<any>;

  customerIdentifier = new FormControl<string>('', {
    validators: [Validators.required, Validators.minLength(10)],
    nonNullable: true,
  });
  dynamicToken = new FormControl<string>('', {
    validators: [Validators.required, Validators.minLength(6)],
    nonNullable: true,
  });

  dynamicTokenForm = new FormGroup({
    customerIdentifier: this.customerIdentifier,
    dynamicToken: this.dynamicToken,
  });

  #paymentResponse$ = new BehaviorSubject<PaymentsResponseUIDto | null>(null);

  paymentResponseUI$ = this.#paymentResponse$.asObservable();

  readonly #destroy$ = new Subject<void>();
  readonly #step$ = new BehaviorSubject<Step>('getCalendarToken');
  readonly #isLoading$ = new BehaviorSubject<boolean>(false);
  readonly content$ = this.#step$.pipe(
    combineLatestWith(this.#isLoading$),
    map(([step, isLoading]) => {
      if (isLoading) {
        return this._loadingTemp;
      }

      switch (step) {
        case 'calendar':
          return this._calendarTemp;
        case 'message':
          return this._messageTemp;
        case 'getCalendarToken':
        case 'finishLoanToken':
        default:
          return this._tokenFormTemp;
      }
    })
  );
  readonly #errorCode$ = new BehaviorSubject<any>(null);
  readonly error$ = this.#errorCode$.pipe(
    map(error => {
      if (!error) {
        return null;
      }

      return this.#errorsService.getErrorByCode(error);
    }),
    switchMap(code => {
      if (!code) {
        return of(null);
      }
      return this.#i18n.getTranslateObjectByKey<{
        title: string;
        message: string;
      }>({
        key: code,
        scope: this.#scope,
      });
    })
  );
  readonly tokenTexUI$ = this.#step$.pipe(
    map(step => {
      switch (step) {
        case 'getCalendarToken':
          return {
            title:
              'Ingrese el código generado en la aplicación del cliente para generar el calendario de pagos',
          };

        case 'finishLoanToken':
          return {
            title:
              'Ingrese el código generado en la aplicación del cliente para confirmar la compra',
          };
        default:
          return {
            title: '',
          };
      }
    })
  );

  get hasError() {
    return this.#errorCode$.value !== null;
  }

  get hasFormError() {
    if (['getCalendarToken', 'finishLoanToken'].includes(this.#step$.value)) {
      return this.dynamicTokenForm.invalid;
    }
    return false;
  }

  get isLoading() {
    return this.#isLoading$.value;
  }

  changeStep(): void {
    if (
      ['getCalendarToken', 'finishLoanToken'].includes(this.#step$.value) &&
      (!this.dynamicTokenForm.valid || this.dynamicTokenForm.pristine)
    ) {
      return;
    }

    if (
      this.#step$.value === 'getCalendarToken' &&
      this.dynamicTokenForm.valid &&
      this.loanId
    ) {
      this.#isLoading$.next(true);
      this.#paymentsService
        .getCalendar({
          phone: `${this.customerIdentifier.value}`,
          code: this.dynamicToken.value,
          loanId: this.loanId,
        })
        .pipe(
          take(1),
          tap(response => {
            // TODO: validate if credit limit needs validation or not
            this.#paymentResponse$.next(response);
            this.#step$.next('calendar');
            this.#errorCode$.next(null);
          }),
          catchError(err => {
            if (err?.status === 422 || err?.codeStatus === 422) {
              this.#notifier.warning({
                title: 'El Código dinámico no es válido',
                message: 'Por favor, proporciona un nuevo código dinámico',
              });

              return EMPTY;
            }

            this.#errorCode$.next(err);
            this.#step$.next('message');
            this.#paymentResponse$.next(null);
            return EMPTY;
          }),
          finalize(() => {
            this.dynamicToken.reset();
            this.#isLoading$.next(false);
          })
        )
        .subscribe();
      return;
    }

    if (
      this.#step$.value === 'finishLoanToken' &&
      this.dynamicTokenForm.valid
    ) {
      this.#isLoading$.next(true);

      this.#paymentsService
        .finishOrder({
          loandId: `${this.loanId}`,
          phone: `${this.customerIdentifier.value}`,
          code: `${this.dynamicToken.value}`,
          sessionId: this.#kountService.sessionId,
          schemeId: this.#paymentResponse$.value?.schemes.id as number,
        })
        .pipe(
          take(1),
          tap(() => {
            this.dynamicTokenForm.reset();
            this.#errorCode$.next(null);
            this.#notifier.success({
              title: 'Pago realizado con éxito',
            });
            this.#dialogRef.close();
          }),

          catchError(err => {
            if (err?.status === 422 || err?.codeStatus === 422) {
              this.#notifier.warning({
                title: 'El Código dinámico no es válido',
                message: 'Por favor, proporciona un nuevo código dinámico',
              });

              return EMPTY;
            }

            this.#errorCode$.next(err);
            this.#step$.next('message');
            this.#paymentResponse$.next(null);
            this.dynamicTokenForm.reset();
            return EMPTY;
          }),

          finalize(() => {
            this.dynamicToken.reset();
            this.#isLoading$.next(false);
          })
        )
        .subscribe();

      return;
    }

    if (this.#step$.value === 'calendar') {
      this.#step$.next('finishLoanToken');
    }
  }

  cancel(): void {
    if (this.#step$.value === 'finishLoanToken') {
      this.#step$.next('calendar');
      return;
    }
    this.#dialogRef.close();
    this.dynamicTokenForm.reset();
  }

  constructor() {
    this.#registerIcon.registerIcons([iconSpinCircle]);
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
