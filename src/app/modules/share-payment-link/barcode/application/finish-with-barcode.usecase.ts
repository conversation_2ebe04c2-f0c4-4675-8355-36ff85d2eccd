import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  catchError,
  delay,
  finalize,
  map,
  Observable,
  of,
  take,
  throwError,
} from 'rxjs';
import { BarCodeRepository } from '../domain/barcode.repository';

@Injectable()
export class FinishWithBarcodeUsecase
  implements
    BaseUsecase<
      {
        customerId: string | null;
        loanId: number | null;
        successMessage: string;
      },
      Observable<void>
    >
{
  readonly #repository = inject(BarCodeRepository);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(args: {
    customerId: string | null;
    loanId: number | null;
    successMessage: string;
  }): Observable<void> {
    const id = this.#loader.show();

    try {
      const errors = ['customerId', 'loanId']
        .map(key => {
          const error = Guard.againstNullOrUndefined(args, key);

          return error.succeeded ? null : error.message;
        })
        .filter(Boolean);

      if (errors.length) {
        throw new RuntimeMerchantError(
          errors.join(', '),
          'FinishWithBarcodeUsecase::execute::emptyArgument'
        );
      }

      const customerId = args.customerId;
      const loanId = args.loanId;

      Guard.againstInvalidNumbers(
        +customerId!,
        'FinishWithBarcodeUsecase::execute::invalidCustomerId',
        'El ID del cliente tiene que ser un número'
      );

      Guard.againstInvalidNumbers(
        +loanId!,
        'FinishWithBarcodeUsecase::execute::invalidLoanId',
        'El ID de la orden tiene que ser un número'
      );

      return this.#repository
        .payLoan({ customerId: Number(customerId!), loanId: loanId! })
        .pipe(
          // slow down the payment process flow
          delay(2500),
          map(() => {
            this.#notifier.info({
              title:
                args.successMessage ||
                'El cliente continuará el proceso en su dispositivo',
            });

            return void 0;
          }),

          catchError(e => this.#errorHandler.handle(e, of(void 0))),

          finalize(() => {
            this.#loader.hide(id);
          }),
          take(1)
        );
    } catch (error) {
      this.#loader.hide(id);

      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
