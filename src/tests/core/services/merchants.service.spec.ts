import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { POS_ENVIRONMENT_CORE } from '../../../app-core/domain/environments';
import { MerchantsService } from '../../../app/core/services/merchants.service';
import configData from '../merchant-config.db.json';

const setup = () => {
  TestBed.configureTestingModule({
    imports: [HttpClientTestingModule],
    providers: [
      MerchantsService,
      {
        provide: POS_ENVIRONMENT_CORE,
        useValue: {
          posApiUrl: 'https://pos.aplazo.net',
        },
      },
    ],
  });

  const httpTestingController = TestBed.inject(HttpTestingController);
  const merchantService = TestBed.inject(MerchantsService);
  const httpClient = TestBed.inject(HttpClient);
  const spyhttpGet = spyOn(httpClient, 'get').and.callThrough();
  const spyhttpPut = spyOn(httpClient, 'put').and.callThrough();

  return { httpTestingController, merchantService, spyhttpGet, spyhttpPut };
};

describe('MerchantsService', () => {
  describe('getMerchantConfig', () => {
    let httpController: HttpTestingController;
    let service: MerchantsService;
    let spyGetHttp: jasmine.Spy;

    beforeEach(() => {
      const { httpTestingController, merchantService, spyhttpGet } = setup();

      httpController = httpTestingController;
      service = merchantService;
      spyGetHttp = spyhttpGet;
    });

    afterEach(() => {
      httpController.verify();
    });

    it('should be created', () => {
      expect(service).toBeTruthy();
      expect(service).toBeInstanceOf(MerchantsService);
    });

    it('should retrieve merchantConfigs successfully', () => {
      const expectedConfigResponse = configData.content;

      service.getMerchantConfig().subscribe({
        next: response => {
          expect(response).toEqual(expectedConfigResponse);
          expect(spyGetHttp).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

      const req = httpController.expectOne(
        'https://pos.aplazo.net/api/merchant_configs'
      );

      expect(req.request.method).toBe('GET');

      req.flush(expectedConfigResponse);
    });

    it('should retrieve empty merchantConfigs on error', () => {
      service.getMerchantConfig().subscribe({
        next: response => {
          expect(response).toEqual({} as any);
          expect(spyGetHttp).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

      const req = httpController.expectOne(
        'https://pos.aplazo.net/api/merchant_configs'
      );

      expect(req.request.method).toBe('GET');

      req.flush('Deliberate error', {
        status: 500,
        statusText: 'Internal Server Error',
      });
    });
  });

  describe('getMerchantDetails', () => {
    let httpController: HttpTestingController;
    let service: MerchantsService;
    let spyGetHttp: jasmine.Spy;

    beforeEach(() => {
      const { httpTestingController, merchantService, spyhttpGet } = setup();

      httpController = httpTestingController;
      service = merchantService;
      spyGetHttp = spyhttpGet;
    });

    afterEach(() => {
      httpController.verify();
    });

    it('should be created', () => {
      expect(service).toBeTruthy();
      expect(service).toBeInstanceOf(MerchantsService);
    });

    it('should retrieve merchantDetails successfully', () => {
      const expectedDetailsResponse = {
        content: {
          name: 'QA WOLF',
        },
        error: null,
        code: 200,
      };

      service.getMerchantDetails().subscribe({
        next: response => {
          expect(response).toEqual(expectedDetailsResponse.content);
          expect(spyGetHttp).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

      const req = httpController.expectOne(
        'https://pos.aplazo.net/api/merchant_configs/merchant-details'
      );

      expect(req.request.method).toBe('GET');

      req.flush(expectedDetailsResponse);
    });

    it('should retrieve empty merchantDetails on error', () => {
      service.getMerchantDetails().subscribe({
        next: response => {
          expect(response).toEqual({ name: '' });
          expect(spyGetHttp).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

      const req = httpController.expectOne(
        'https://pos.aplazo.net/api/merchant_configs/merchant-details'
      );

      expect(req.request.method).toBe('GET');

      req.flush('Deliberate error', {
        status: 500,
        statusText: 'Internal Server Error',
      });
    });
  });

  describe('updateMerchantBranch', () => {
    let httpController: HttpTestingController;
    let service: MerchantsService;
    let spyPutHttp: jasmine.Spy;

    beforeEach(() => {
      const { httpTestingController, merchantService, spyhttpPut } = setup();

      httpController = httpTestingController;
      service = merchantService;
      spyPutHttp = spyhttpPut;
    });

    afterEach(() => {
      httpController.verify();
    });

    it('should be created', () => {
      expect(service).toBeTruthy();
      expect(service).toBeInstanceOf(MerchantsService);
    });

    it('should update merchantBranch successfully', () => {
      const expectedUpdateResponse = {
        content: {
          id: 124,
          logoUrl: 'https://logo.com',
          merchantId: 2239,
          branches: [],
          DeletedAt: null,
          createdAt: '2024-03-01T14:00:00.000Z',
          updatedAt: '2024-05-28T10:00:00.000Z',
          isBranchCreationEnable: true,
          minimumOrderAmount: 100,
        },
        error: null,
        code: 200,
      };

      service
        .updateMerchantBranch({
          LogoUrl: 'https://logo.com',
          branches: [],
        })
        .subscribe({
          next: response => {
            expect(response).toEqual(expectedUpdateResponse.content);
            expect(spyPutHttp).toHaveBeenCalledTimes(1);
          },
          error: fail,
        });

      const req = httpController.expectOne(
        'https://pos.aplazo.net/api/merchant_configs'
      );

      expect(req.request.method).toBe('PUT');

      req.flush(expectedUpdateResponse);
    });

    it('should throw error when server throws an error', () => {
      service
        .updateMerchantBranch({
          LogoUrl: 'https://logo.com',
          branches: [],
        })
        .subscribe({
          next: fail,
          error: error => {
            expect(error).toBeInstanceOf(HttpErrorResponse);
            expect(error.status).toBe(500);
            expect(spyPutHttp).toHaveBeenCalledTimes(1);
          },
        });

      const req = httpController.expectOne(
        'https://pos.aplazo.net/api/merchant_configs'
      );

      expect(req.request.method).toBe('PUT');

      req.flush('Deliberate error', {
        status: 500,
        statusText: 'Internal Server Error',
      });
    });
  });
});
