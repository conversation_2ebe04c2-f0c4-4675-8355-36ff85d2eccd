import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AplazoPopupComponent } from 'src/app/modules/shared/popup/infra/components/aplazo-popup/aplazo-popup.component';
import { DialogRef } from '@ngneat/dialog';

describe('AplazoPopupComponent', () => {
  let component: AplazoPopupComponent;
  let fixture: ComponentFixture<AplazoPopupComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AplazoPopupComponent],
      providers: [
        {
          provide: DialogRef,
          useValue: {
            close: jasmine.createSpy('close'),
            data: {
              htmlContent: '<h2>Test Popup</h2><p>Contenido dinámico</p>',
            },
          },
        },
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(AplazoPopupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render provided HTML content', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.innerHTML).toContain('<h2>Test Popup</h2>');
    expect(compiled.innerHTML).toContain('<p>Contenido dinámico</p>');
    // Verifica que el DOM incluye el HTML dinámico, no el texto estático anterior
  });

  it('should render fallback if no htmlContent provided', () => {
    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [AplazoPopupComponent],
      providers: [
        {
          provide: DialogRef,
          useValue: {
            close: jasmine.createSpy('close'),
            data: {},
          },
        },
      ],
    }).compileComponents();
    const testFixture = TestBed.createComponent(AplazoPopupComponent);
    testFixture.detectChanges();
    const compiled = testFixture.nativeElement as HTMLElement;
    expect(compiled.innerHTML).toContain('<p>No content</p>');
  });
});
