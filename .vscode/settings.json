{
  "eslint.options": {
    "extensions": [".ts", ".html"]
  },
  "eslint.validate": ["javascript", "typescript", "html"],
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },
  "[html][typescript]": {
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },
  // AI and Code Suggestions
  "editor.inlineSuggest.enabled": true,
  "editor.suggestSelection": "first",
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.acceptSuggestionOnEnter": "on",
  "editor.quickSuggestions": {
    "other": true,
    "comments": false,
    "strings": true
  },
  "editor.suggest.showKeywords": true,
  "editor.suggest.showSnippets": true,
  "editor.suggest.showClasses": true,
  "editor.suggest.showFunctions": true,
  "editor.suggest.showVariables": true,
  "editor.suggest.showConstants": true,
  "editor.suggest.showEnums": true,
  "editor.suggest.showEnumMembers": true,
  "editor.suggest.showWords": true,
  "editor.suggest.showFiles": true,
  "editor.suggest.showReferences": true,
  "editor.suggest.showCustomcolors": true,
  "editor.suggest.showUsers": true,
  "editor.suggest.showIssues": true,
  "editor.suggest.showColors": true,
  "editor.suggest.showFolders": true,
  "editor.suggest.showTypeParameters": true,
  "editor.suggest.showUnits": true,
  "editor.suggest.showValues": true,
  // Angular specific
  "typescript.preferences.includePackageJsonAutoImports": "on",
  "typescript.suggest.autoImports": true,
  "typescript.suggest.includeCompletionsForModuleExports": true,
  "typescript.suggest.includeCompletionsWithSnippetText": true,
  "typescript.suggest.includeCompletionsWithClassMemberSnippets": true,
  "typescript.suggest.includeCompletionsWithInsertText": true,
  "typescript.suggest.includeCompletionsWithReplaceText": true,
  // Performance
  "files.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.angular": true,
    "**/coverage": true
  },
  "search.exclude": {
    "**/node_modules": true,
    "**/dist": true,
    "**/.angular": true,
    "**/coverage": true
  },
  // Git
  "git.enableSmartCommit": true,
  "git.confirmSync": false,
  "git.autofetch": true,
  // Terminal
  "terminal.integrated.defaultProfile.osx": "zsh",
  "terminal.integrated.fontSize": 14,
  // File associations
  "files.associations": {
    "*.component.ts": "typescript",
    "*.service.ts": "typescript",
    "*.module.ts": "typescript",
    "*.guard.ts": "typescript",
    "*.interceptor.ts": "typescript"
  },
  // Augmentcode Configuration
  "augmentcode.enabled": true,
  "augmentcode.autoComplete": true,
  "augmentcode.suggestions": true,
  "augmentcode.contextWindow": 128000,
  "augmentcode.model": "claude-3.5-sonnet",
  "augmentcode.temperature": 0.1,
  "augmentcode.maxTokens": 4000,
  "augmentcode.includeComments": true,
  "augmentcode.includeImports": true,
  "augmentcode.includeTests": false,
  "augmentcode.language": "typescript",
  "augmentcode.framework": "angular"
}
