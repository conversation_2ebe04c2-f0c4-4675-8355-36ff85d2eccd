import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { map, take } from 'rxjs';
import {
  POS_APP_ROUTES,
  RouteConfig,
} from '../../core/domain/config/app-routes.core';
import { StoreService } from '../application/services/store.service';
import { isValidByIntegrationType } from '../domain/integration-type';

export const allowByIntegrationGuard = (): CanActivateFn => {
  return () => {
    const storeService = inject(StoreService);
    const router = inject(Router);
    const appRoutes: RouteConfig = inject(POS_APP_ROUTES);

    return storeService.integrationType$.pipe(
      take(1),
      map(integrationType => {
        if (!isValidByIntegrationType(integrationType)) {
          return router.parseUrl(`/${appRoutes.wrongIntegrationView}`);
        }

        return true;
      })
    );
  };
};
