import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  NgS<PERSON>,
  NgS<PERSON>C<PERSON>,
  NgSwitchDefault,
} from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  inject,
  Input,
  Output,
  ViewEncapsulation,
} from '@angular/core';
import {
  AplazoDynamicPipe,
  AplazoMatchMediaService,
  ElementColor,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoDropdownComponents } from '@aplazo/shared-ui/dropdown';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoLoanStatusLabelColorDirective,
  AplazoLoanStatusMapperPipe,
} from '@aplazo/shared-ui/merchant';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import {
  iconArrowPath,
  iconChevronDown,
  iconInfo,
  iconShare,
} from '@aplazo/ui-icons';
import {
  BehaviorSubject,
  combineLatest,
  combineLatestWith,
  lastValueFrom,
  map,
  Observable,
  take,
  tap,
  timer,
} from 'rxjs';
import { StoreService } from '../../../../core/application/services/store.service';
import { Order } from '../../../../core/domain/order.interface';

export interface IOrdersTableInjectedTextUI {
  orderId: string;
  date: string;
  sellAgentId: string;
  status: string;
  price: {
    label: string;
    tooltip?: string;
  };
  actions: string;
}

export interface IOrderTableEvent {
  type: 'cancel' | 'share' | 'refund';
  order: Order;
}

@Component({
  standalone: true,
  selector: 'aplazo-orders-table',
  imports: [
    AplazoSimpleTableComponents,
    AplazoButtonComponent,
    AplazoIconComponent,
    AplazoCardComponent,
    AplazoDropdownComponents,
    AplazoLoanStatusLabelColorDirective,
    AplazoLoanStatusMapperPipe,
    AplazoDynamicPipe,
    NgFor,
    NgIf,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    AsyncPipe,
    NgClass,
  ],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './orders-table.component.html',
})
export class OrdersTableComponent {
  readonly #storeService = inject(StoreService);
  readonly #matchMedia = inject(AplazoMatchMediaService);
  readonly #iconRegistryService = inject(AplazoIconRegistryService);

  @Output()
  cancelOrder = new EventEmitter<Order>();

  @Output()
  shareOrder = new EventEmitter<Order>();

  @Output()
  showRefundInfo = new EventEmitter<Order>();

  @Output()
  checkOrderStatus = new EventEmitter<Order>();

  readonly #ordersDisabled$ = new BehaviorSubject<Record<number, boolean>>({});
  readonly #orders$ = new BehaviorSubject<Order[]>([]);
  @Input()
  set orders(value: Order[] | null) {
    if (value == null || !Array.isArray(value)) {
      this.#orders$.next([]);
      return;
    }
    this.#orders$.next(value);
  }
  readonly orders$ = this.#orders$.pipe(
    combineLatestWith(this.#ordersDisabled$),

    map(([orders, disabledOrders]) => {
      return orders.map(order => ({
        ...order,
        isDisabled: disabledOrders[order.loanId] ?? false,
      }));
    })
  );

  readonly #text$ = new BehaviorSubject<IOrdersTableInjectedTextUI | null>(
    null
  );
  @Input()
  set i18text(value: IOrdersTableInjectedTextUI | null) {
    if (value == null) {
      console.warn('orders table text is null');
    }
    this.#text$.next(value ?? null);
  }

  readonly text$ = this.#text$.asObservable();

  isShareLinkEnabled$: Observable<boolean> =
    this.#storeService.hasBranchShareLinkEnabled$.pipe(take(1));

  isLargeScreen$ = this.#matchMedia.matchLgScreen$ as Observable<{
    matches: boolean;
  }>;

  readonly vm$ = combineLatest({
    text: this.text$,
    orders: this.orders$,
    isLargeScreen: this.#matchMedia.matchLgScreen$,
  });

  loanStatusColorDefinition: Record<string, ElementColor> = {
    activo: 'success',
    outstanding: 'success',
    created: 'warning',
    refunded: 'info',
    onrefund: 'info',
    error: 'disabled',
    cancelled: 'danger',
  };

  loanStatusTextDefinition: Record<string, string> = {
    activo: 'APROBADO',
    outstanding: 'APROBADO',
    created: 'PENDIENTE',
    refunded: 'DEVUELTO',
    onrefund: 'EN DEVOLUCIÓN',
    error: 'ERROR',
    cancelled: 'CANCELADO',
  };

  constructor() {
    this.#iconRegistryService.registerIcons([
      iconShare,
      iconInfo,
      iconChevronDown,
      iconArrowPath,
    ]);
  }

  cancel(order: Order): void {
    this.cancelOrder.emit(order);
  }

  share(order: Order): void {
    this.shareOrder.emit(order);
  }

  showWMrefundInfo(order: Order): void {
    if (order.isWalmartOrder) {
      this.showRefundInfo.emit(order);
    }
  }

  async checkStatus(order: Order): Promise<void> {
    this.checkOrderStatus.emit(order);

    this.#ordersDisabled$.next({
      ...this.#ordersDisabled$.value,
      [order.loanId]: true,
    });

    const securedDelayToEnableOrder = 3000;

    await lastValueFrom(
      timer(securedDelayToEnableOrder).pipe(
        take(1),
        tap(() => {
          this.#ordersDisabled$.next({
            ...this.#ordersDisabled$.value,
            [order.loanId]: false,
          });
        })
      )
    );
  }
}
