import { TestBed } from '@angular/core/testing';
import { UrlTree } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';
import { StoreService } from 'src/app/core/application/services/store.service';
import { hasNotSelectedBranchGuard } from 'src/app/core/guards/has-empty-branch-selected.guard';
import {
  POS_APP_ROUTES,
  ROUTE_CONFIG,
} from '../../../app/core/domain/config/app-routes.core';

const setup = (mockBranch?: unknown) => {
  TestBed.configureTestingModule({
    imports: [RouterTestingModule],
    providers: [
      {
        provide: StoreService,
        useValue: {
          selectedBranch$: of(mockBranch ?? null),
        },
      },
      {
        provide: POS_APP_ROUTES,
        useValue: ROUTE_CONFIG,
      },
    ],
  });
};

describe('HasEmptyBranchSelectedGuard', () => {
  it('should return true if branch is not selected', async () => {
    setup();

    const result = await TestBed.runInInjectionContext(
      hasNotSelectedBranchGuard as () => Promise<boolean | UrlTree>
    );

    expect(result).toBeTrue();
  });

  it('should return true if branch is not an object', async () => {
    setup('test');

    const result = await TestBed.runInInjectionContext(
      hasNotSelectedBranchGuard as () => Promise<boolean | UrlTree>
    );

    expect(result).toBeTrue();
  });

  it('should return UrlTree if branch is selected', async () => {
    setup({ id: 1 });

    const result = await TestBed.runInInjectionContext(
      hasNotSelectedBranchGuard as () => Promise<boolean | UrlTree>
    );

    expect(result).toBeInstanceOf(UrlTree);
    expect(result.toString()).toBe(`/${ROUTE_CONFIG.aplazoRoot}`);
  });

  it('should return UrlTree if branch has merchantConfigId', async () => {
    setup({ merchantConfigId: 1 });

    const result = await TestBed.runInInjectionContext(
      hasNotSelectedBranchGuard as () => Promise<boolean | UrlTree>
    );

    expect(result).toBeInstanceOf(UrlTree);
    expect(result.toString()).toBe(`/${ROUTE_CONFIG.aplazoRoot}`);
  });

  it('should return UrlTree if branch has name', async () => {
    setup({ name: 'test' });

    const result = await TestBed.runInInjectionContext(
      hasNotSelectedBranchGuard as () => Promise<boolean | UrlTree>
    );

    expect(result).toBeInstanceOf(UrlTree);
    expect(result.toString()).toBe(`/${ROUTE_CONFIG.aplazoRoot}`);
  });
});
