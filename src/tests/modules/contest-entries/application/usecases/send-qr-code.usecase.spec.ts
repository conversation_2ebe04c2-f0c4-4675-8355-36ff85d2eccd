import { TestBed, fakeAsync, tick, flush } from '@angular/core/testing';
import { BehaviorSubject, EMPTY, of, throwError } from 'rxjs';
import {
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { SendQRCodeUseCase } from '../../../../../app/modules/contest-entries/application/usecases/send-qr-code.usecase';
import {
  SendQRResponse,
  ContestRanking,
} from '../../../../../app/modules/contest-entries/domain/contest';
import { ContestRepository } from '../../../../../app/modules/contest-entries/domain/repositories/contest.repository';
import { ContestStoreService } from '../../../../../app/modules/contest-entries/infra/services/contest-store.service';

const mockResponse: SendQRResponse = {
  success: true,
  message: '',
};

const setup = () => {
  const mockRanking: ContestRanking = {
    participantId: 'test-id',
    data: [],
    hasDetails: false,
    isFutureDate: false,
    campaignFinishDate: '',
    qrUrl: '',
    phoneNumber: '',
  };

  const contestRanking$ = new BehaviorSubject<ContestRanking | null>(
    mockRanking
  );
  const repositorySpy = jasmine.createSpyObj<ContestRepository>(
    'ContestRepository',
    ['resendParticipantQR']
  );

  TestBed.configureTestingModule({
    providers: [
      provideLoaderTesting(),
      provideNotifierTesting(),
      provideUseCaseErrorHandlerTesting(),
      SendQRCodeUseCase,
      {
        provide: ContestStoreService,
        useValue: {
          contestRanking$: contestRanking$.asObservable(),
        },
      },
      {
        provide: ContestRepository,
        useValue: repositorySpy,
      },
    ],
  });

  const usecase = TestBed.inject(SendQRCodeUseCase);
  const repository = TestBed.inject(
    ContestRepository
  ) as jasmine.SpyObj<ContestRepository>;
  const store = TestBed.inject(ContestStoreService);
  const loader = TestBed.inject(LoaderService);
  const notifier = TestBed.inject(NotifierService);
  const errorHandler = TestBed.inject(UseCaseErrorHandler);

  const showLoaderSpy = spyOn(loader, 'show').and.returnValue('test-loader-id');
  const hideLoaderSpy = spyOn(loader, 'hide').and.callThrough();
  const successNotifierSpy = spyOn(notifier, 'success').and.callThrough();
  const errorNotifierSpy = spyOn(notifier, 'error').and.callThrough();
  const spyErrorHandler = spyOn(errorHandler, 'handle').and.returnValue(EMPTY);

  return {
    usecase,
    repository,
    store,
    mockRanking,
    contestRanking$,
    showLoaderSpy,
    hideLoaderSpy,
    successNotifierSpy,
    errorNotifierSpy,
    spyErrorHandler,
  };
};

describe('SendQrCodeUseCase', () => {
  it('should be created', () => {
    const { usecase } = setup();
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(SendQRCodeUseCase);
  });

  it('should manage loader lifecycle', fakeAsync(() => {
    const { usecase, repository, showLoaderSpy, hideLoaderSpy } = setup();
    repository.resendParticipantQR.and.returnValue(of(mockResponse));

    usecase.execute().subscribe();
    tick();
    flush();

    expect(showLoaderSpy).toHaveBeenCalled();
    expect(hideLoaderSpy).toHaveBeenCalledWith('test-loader-id');
  }));

  it('should handle missing participant', fakeAsync(() => {
    const { usecase, spyErrorHandler, contestRanking$ } = setup();
    contestRanking$.next(null);

    usecase.execute().subscribe();
    tick();

    expect(spyErrorHandler).toHaveBeenCalledWith(
      new RuntimeMerchantError(
        'Información del participante no disponible',
        'SendQRCode::NoParticipant',
        'send-qr-code-usecase-error'
      ),
      EMPTY
    );
  }));

  it('should handle repository error', fakeAsync(() => {
    const { usecase, repository, errorNotifierSpy } = setup();
    repository.resendParticipantQR.and.returnValue(
      throwError(() => new Error())
    );

    usecase.execute().subscribe();
    tick();
    flush();

    expect(errorNotifierSpy).toHaveBeenCalledWith({
      title: 'Error al enviar QR',
      message: 'No se pudo enviar el QR. Vuelve a intentarlo.',
    });
  }));

  it('should handle successful QR send', fakeAsync(() => {
    const { usecase, repository, mockRanking, successNotifierSpy } = setup();
    repository.resendParticipantQR.and.returnValue(of(mockResponse));

    usecase.execute().subscribe();
    tick();
    flush();
    expect(repository.resendParticipantQR).toHaveBeenCalledWith(
      mockRanking.participantId as string
    );
    expect(successNotifierSpy).toHaveBeenCalledWith({
      title: 'El QR ha sido enviado a tu número registrado',
    });
  }));
});
