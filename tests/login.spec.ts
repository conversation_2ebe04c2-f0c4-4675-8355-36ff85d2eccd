import { expect, Page, test } from '@playwright/test';

const uaApiSuccess = async (page: Page) => {
  await page.route('https://pos.aplazo.**/api/user_agent/', async route => {
    await route.fulfill({
      status: 200,
      body: JSON.stringify({
        allowedUA: true,
      }),
      contentType: 'application/json',
    });
  });
};

test('login reder normally', async ({ page }) => {
  await uaApiSuccess(page);

  await page.goto('/');

  await expect(page).toHaveTitle(/Aplazo | Offline PoS/);
  await expect(page.getByRole('heading', { level: 1 })).toBeVisible();
});

test('login render after statsig failed', async ({ page }) => {
  const warnLogs: string[] = [];

  test.setTimeout(300000);

  await uaApiSuccess(page);

  page.on('console', msg => {
    if (msg.type() === 'warning') {
      warnLogs.push(msg.text());
    }
  });

  await page.route('https://featureassets.org/**', async route => {
    await route.fulfill({
      status: 500,
    });
  });
  await page.goto('/');

  await expect(page).toHaveTitle(/Aplazo | Offline PoS/);

  await expect(page.getByRole('heading', { level: 1 })).toBeVisible({
    timeout: 30000,
  });

  expect(warnLogs).toContain('Statsig::Initialization::Failed');
});
