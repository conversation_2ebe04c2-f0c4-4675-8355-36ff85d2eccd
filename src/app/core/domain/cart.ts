import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';
import { ProductRequest } from './product-request';

export interface ICharge {
  title: string;
  price: number;
}

export interface ILoanRequestUIDto {
  cartId: string;
  products: ProductRequest[];
  totalPrice: string;
  hostname: string;
  branchId: string | number;
  sellsAgentId?: number;
}

export interface ILoanRequestProps {
  cartId: string;
  products: ProductRequest[];
  discount: ICharge;
  taxes: ICharge;
  shipping: ICharge;
  totalPrice: number;
  successUrl: string;
  errorUrl: string;
  webHookUrl: string;
  sellsAgentId: number;
}

export const invalidLoanPriceError =
  'Error al crear prestamo: ingrese solo números para el precio de los productos';

export const invalidSellAgentIdError =
  'Error al crear prestamo: el id del vendedor no es un número válido';

export class LoanRequest {
  public readonly cartId: string;
  public readonly products: ProductRequest[];
  public readonly discount: ICharge;
  public readonly taxes: ICharge;
  public readonly shipping: ICharge;
  public readonly totalPrice: number;
  public readonly successUrl: string;
  public readonly errorUrl: string;
  public readonly webHookUrl: string;
  public readonly sellsAgentId: number;

  private constructor(loan: ILoanRequestProps) {
    this.cartId = loan.cartId;
    this.products = loan.products;
    this.discount = loan.discount;
    this.taxes = loan.taxes;
    this.shipping = loan.shipping;
    this.totalPrice = loan.totalPrice;
    this.successUrl = loan.successUrl;
    this.errorUrl = loan.errorUrl;
    this.webHookUrl = loan.webHookUrl;
    this.sellsAgentId = loan.sellsAgentId;
  }

  public static create(loanUI: ILoanRequestUIDto): LoanRequest {
    Guard.againstInvalidNumbers(
      +loanUI.totalPrice,
      'CreateProduct::InvalidPrice::NaN',
      invalidLoanPriceError
    );

    if (+loanUI.totalPrice < 0) {
      throw new RuntimeMerchantError(
        invalidLoanPriceError,
        'CreateProduct::InvalidPrice::negative',
        'create-product-error'
      );
    }

    const sellsAgentId = loanUI.sellsAgentId;

    Guard.againstInvalidNumbers(
      sellsAgentId,
      'CreateProduct::InvalidSellsAgentId::NaN',
      invalidSellAgentIdError
    );

    if (Number(sellsAgentId) < 0) {
      throw new RuntimeMerchantError(
        invalidSellAgentIdError,
        'CreateProduct::InvalidSellsAgentId',
        'create-product-error'
      );
    }

    const currentIVA = 0.16;

    const taxes = +loanUI.totalPrice * currentIVA;

    return new LoanRequest({
      cartId: loanUI.cartId,
      products: loanUI.products,
      discount: {
        title: 'sin descuento',
        price: 0,
      },
      taxes: {
        price: taxes,
        title: 'IVA',
      },
      shipping: {
        title: 'Recoger en tienda',
        price: 0,
      },

      totalPrice: parseFloat(loanUI.totalPrice),
      successUrl: `${loanUI.hostname}/success?branchId=${loanUI.branchId}`, //page
      errorUrl: `${loanUI.hostname}/secured/error-page?branchId=${loanUI.branchId}`, //pageError
      webHookUrl: 'THIS WILL BE OVVERRIDED BY BACKEND', //Falta ver que url http
      sellsAgentId: sellsAgentId as number,
    });
  }
}
