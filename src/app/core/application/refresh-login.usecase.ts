import { Injectable } from '@angular/core';
import { BaseUsecase } from '@aplazo/merchant/shared';
import {
  catchError,
  defer,
  map,
  Observable,
  of,
  SchedulerLike,
  switchMap,
  take,
  timeout,
  TimeoutError,
} from 'rxjs';
import { StoreService } from './services/store.service';

@Injectable({ providedIn: 'root' })
export class RefreshLoginUsecase
  implements
    BaseUsecase<
      SchedulerLike | undefined,
      Observable<{ merchantId: number; isLoggedIn: boolean }>
    >
{
  readonly #timeout = 2000;
  constructor(private store: StoreService) {}

  execute(
    // to use VirtualTimeScheduler in tests
    // and to handle timeout
    // https://medium.com/angular-in-depth/so-what-is-rxjs-virtualtimescheduler-796e92ed722f
    scheduler?: SchedulerLike
  ): Observable<{ merchantId: number; isLoggedIn: boolean }> {
    return defer(() => this.store.setStoreFromSessionStorage()).pipe(
      timeout(this.#timeout, scheduler),
      switchMap(() => this.store.decodedToken$),
      map(user => ({
        merchantId: user?.merchantId ?? 0,
        isLoggedIn:
          !!user &&
          Object.prototype.hasOwnProperty.call(user, 'merchantId') &&
          user.merchantId > 0,
      })),
      catchError(err => {
        if (err instanceof TimeoutError) {
          console.warn('RefreshInitializer::Timeout');
        }

        return of({ merchantId: 0, isLoggedIn: false });
      }),
      take(1)
    );
  }
}
