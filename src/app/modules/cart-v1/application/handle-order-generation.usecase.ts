import { Injectable, inject } from '@angular/core';
import { I18NService } from '@aplazo/i18n';
import {
  RedirectionService,
  RuntimeMerchantError,
} from '@aplazo/merchant/shared';
import { DialogService } from '@ngneat/dialog';
import {
  EMPTY,
  Observable,
  OperatorFunction,
  catchError,
  map,
  pipe,
  switchMap,
  take,
  zip,
} from 'rxjs';
import { StoreService } from '../../../core/application/services/store.service';
import { POS_APP_ROUTES } from '../../../core/domain/config/app-routes.core';
import { Order } from '../../../core/domain/order.interface';
import { ExpressFormComponent } from '../express-form/express-form.component';
import { FinishOrderUseCase } from './finish-order.usecase';

@Injectable({
  providedIn: 'root',
})
export class HandleOrderGenerationUseCase {
  readonly #store = inject(StoreService);
  readonly #redirecter = inject(RedirectionService);
  readonly #dialog = inject(DialogService);
  readonly #appRoutes = inject(POS_APP_ROUTES);
  readonly #finishUsecase = inject(FinishOrderUseCase);
  readonly #i18n = inject(I18NService);

  merchantId$ = this.#store.getMerchantId$();

  branchId$: Observable<number | null> = this.#store.selectedBranch$.pipe(
    map(branch => branch?.id || null)
  );

  execute(): Observable<Order> {
    /**
     * zip always wait for all observables to emit at least once
     * and then it emits the values as an array
     */
    return zip([
      this.#i18n.getTranslateObjectByKey<Record<string, number[]>>({
        key: 'whiteList',
        scope: 'order-generation',
      }),
      this.merchantId$,
      this.branchId$,
    ]).pipe(
      take(1),

      this.#executeDialogOrRedirectByFlag(),

      this.#finishOrderOrThrows(),

      take(1),

      catchError(error => {
        if (error instanceof RuntimeMerchantError) {
          console.warn(error.code);
        } else {
          console.error(error);
        }

        return EMPTY;
      })
    );
  }

  #executeDialogOrRedirectByFlag(): OperatorFunction<
    [Record<string, number[]> | null, number | null, number | null],
    | {
        agentId: number;
        price: number;
      }
    | undefined
  > {
    return pipe(
      switchMap(([storeFrontsConfig, merchantId, branchId]) => {
        const configByMerchantId =
          storeFrontsConfig && merchantId
            ? storeFrontsConfig[merchantId]
            : null;

        const isConfigAList = Array.isArray(configByMerchantId);
        const isBranchValid =
          configByMerchantId &&
          branchId &&
          (configByMerchantId.length === 0 ||
            !!configByMerchantId.includes(branchId));

        const hasBranchFlagEnabled =
          configByMerchantId && isConfigAList && isBranchValid;

        if (!hasBranchFlagEnabled) {
          console.warn('Order::newOrder::express::disabled');
          this.#redirecter.internalNavigation([
            this.#appRoutes.aplazoRoot,
            this.#appRoutes.aplazoLayout,
            this.#appRoutes.securedCart,
          ]);

          return EMPTY;
        }

        return this.#dialog.open(ExpressFormComponent, {
          enableClose: false,
        }).afterClosed$;
      })
    );
  }

  #finishOrderOrThrows(): OperatorFunction<
    | {
        agentId: number;
        price: number;
      }
    | undefined,
    Order
  > {
    return pipe(
      switchMap(dialogResponse => {
        if (!dialogResponse) {
          throw new RuntimeMerchantError(
            'creación de orden cancelada',
            'Order::newOrder::express::cancelled',
            'new-order-express-dialog-response'
          );
        }

        if (!dialogResponse.price || dialogResponse.price < 0) {
          throw new RuntimeMerchantError(
            'Detectamos un error al procesar la orden. Estamos trabajando en una solución.',
            'Order::newOrder::express::invalid-price',
            'new-order-express-dialog-response'
          );
        }

        if (dialogResponse.agentId == null || dialogResponse.agentId < 0) {
          throw new RuntimeMerchantError(
            'Detectamos un error al procesar la orden. Estamos trabajando en una solución.',
            'Order::newOrder::express::invalid-agent-id',
            'new-order-express-dialog-response'
          );
        }

        return this.#finishUsecase.execute({
          products: [
            {
              name: 'Express pilot purchase',
              price: String(dialogResponse.price),
              quantity: 1,
              sku: 'AplazoID',
            },
          ],
          sellsAgentId: String(dialogResponse.agentId),
          totalPrice: String(dialogResponse.price),
        });
      })
    );
  }
}
