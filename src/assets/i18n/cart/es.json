{"table": {"product": "Producto", "sku": "S<PERSON>", "price": "Precio", "quantity": "Cantidad", "actions": {"label": "Acción", "editButton": "<PERSON><PERSON>", "deleteButton": "Bo<PERSON>r"}}, "totalPrice": {"label": "Total a pagar"}, "agentId": {"inputLabel": "<PERSON><PERSON><PERSON>", "inputPlaceholder": "ID del Asesor de Venta APLAZO", "checkboxLabel": "Esta venta no tiene un Asesor de Venta", "errorInvalidId": "El ID del Asesor debe ser mayor a 0 y menor a 10,000,000", "errorRequired": "Este campo es requerido"}, "form": {"title": "<PERSON><PERSON><PERSON>", "sku": {"label": "ID Producto", "placeholder": "SKU", "errorRequired": "Éste campo es necesario"}, "product": {"label": "Producto", "placeholder": "Nombre del producto", "errorRequired": "Éste campo es necesario", "errorMaxLength": "El nombre del producto no puede exceder los 255 caracteres"}, "price": {"label": "Precio", "placeholder": "$0.00", "errorRequired": "Éste campo es necesario", "errorMin": "El precio debe ser mayor a 0"}, "quantity": {"label": "Cantidad", "placeholder": "0", "errorRequired": "Éste campo es necesario", "errorMin": "La cantidad debe ser mayor a 0"}, "actions": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Guardar"}}, "actions": {"addProduct": {"label": "<PERSON><PERSON><PERSON>"}, "finishOrder": {"label": "Proces<PERSON>"}}, "preloan": {"label": "Ingresa el código de prepago", "errors": {"minlengthError": "El código debe tener al menos {{ minLength }} caracteres"}}}