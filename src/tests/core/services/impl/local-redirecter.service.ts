import { RedirectionService } from '@aplazo/merchant/shared';

export class LocalRedirecter implements RedirectionService {
  async internalNavigation(url: string | string[]): Promise<boolean> {
    console.log('internal navigation :: >> ', url);

    return true;
  }
  async externalNavigation(
    url: string,
    target?: string | undefined
  ): Promise<boolean> {
    console.log('external navigation :: >> ', url, 'target :: >> ', target);
    return true;
  }
  async openPdf(url: string): Promise<boolean> {
    console.log('open pdf :: >> ', url);
    return true;
  }
}
