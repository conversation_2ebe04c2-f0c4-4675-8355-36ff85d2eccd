import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  provideTemporal,
} from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of } from 'rxjs';
import { PosOfflineRepository } from '../../../app/core/domain/repositories/pos-offline.repository';
import { GetHistoricalOrdersUseCase } from '../../../app/modules/historical-v1/application/get-historical-orders.usecase';
import { DateMapper } from '../../../app/modules/historical-v1/date.mapper';

describe('GetHistoricalOrdersUseCase', () => {
  const dateRangeTest = {
    startDate: new Date('2022-10-28'),
    endDate: new Date('2022-11-03'),
  };

  let useCase: GetHistoricalOrdersUseCase;
  let repository: jasmine.SpyObj<PosOfflineRepository>;
  let loader: LoaderService;
  let notifier: NotifierService;
  let dateMapper: DateMapper;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        provideUseCaseErrorHandlerTesting(),
        provideLoaderTesting(),
        provideNotifierTesting(),
        provideTemporal(),
        DateMapper,
        GetHistoricalOrdersUseCase,
        {
          provide: PosOfflineRepository,
          useValue: jasmine.createSpyObj('PosOfflineRepository', [
            'getHistoricalWeekOrders',
          ]),
        },
      ],
    });

    useCase = TestBed.inject(GetHistoricalOrdersUseCase);
    repository = TestBed.inject(
      PosOfflineRepository
    ) as jasmine.SpyObj<PosOfflineRepository>;
    loader = TestBed.inject(LoaderService);
    notifier = TestBed.inject(NotifierService);
    dateMapper = TestBed.inject(DateMapper);
  });

  it('should be created', () => {
    expect(useCase).toBeTruthy();
  });

  it('should call date mapper, getHistoricalWeekOrders, loader methods', fakeAsync(() => {
    const dateMapperSpy = spyOn(
      dateMapper,
      'fromDateRangeToRawDateYearFistWithHyphen'
    ).and.callThrough();
    const loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    const loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    repository.getHistoricalWeekOrders.and.returnValue(
      of([
        {
          id: 648884,
          createdAt: '2022-10-28T21:04:30.394Z',
          updatedAt: '2022-10-28T21:04:30.394Z',
          DeletedAt: null,
          date: '2022-10-28 21:04:30.394Z',
          status: 'created',
          price: 259,
          url: 'https://checkout-offline.aplazo.dev/main/2abcb24f-63e6-46fb-bb67-249e5b7db3b1',
          loanId: 54761,
          merchantId: 12,
          products: [],
          branchId: 175,
          sellsAgentId: 0,
        },

        {
          id: 663336,
          createdAt: '2022-10-30T04:47:23.275Z',
          updatedAt: '2022-10-30T04:47:23.275Z',
          DeletedAt: null,
          date: '2022-10-30 04:47:23.275Z',
          status: 'created',
          price: 223,
          url: 'https://checkout-offline.aplazo.dev/main/9370be10-84be-4d30-9963-83e28e12097a',
          loanId: 54699,
          merchantId: 12,
          products: [],
          branchId: 176,
          sellsAgentId: 0,
        },

        {
          id: 677788,
          createdAt: '2022-10-31T12:30:16.176Z',
          updatedAt: '2022-10-31T12:30:16.176Z',
          DeletedAt: null,
          date: '2022-10-31 12:30:16.176Z',
          status: 'created',
          price: 298,
          url: 'https://checkout-offline.aplazo.dev/main/8b71973a-3ef9-495f-9ab5-9c9df55d165d',
          loanId: 54637,
          merchantId: 12,
          products: [],
          branchId: 177,
          sellsAgentId: 0,
        },

        {
          id: 692240,
          createdAt: '2022-11-01T20:13:09.057Z',
          updatedAt: '2022-11-01T20:13:09.057Z',
          DeletedAt: null,
          date: '2022-11-01 20:13:09.057Z',
          status: 'created',
          price: 244,
          url: 'https://checkout-offline.aplazo.dev/main/df1fcb0e-6045-4b34-8be2-849d92a66fa5',
          loanId: 54575,
          merchantId: 12,
          products: [],
          branchId: 178,
          sellsAgentId: 0,
        },

        {
          id: 706692,
          createdAt: '2022-11-03T03:56:01.758Z',
          updatedAt: '2022-11-03T03:56:01.758Z',
          DeletedAt: null,
          date: '2022-11-03 03:56:01.758Z',
          status: 'created',
          price: 271,
          url: 'https://checkout-offline.aplazo.dev/main/7c051742-5165-4dcf-86f0-23c03f0fb7c7',
          loanId: 54513,
          merchantId: 12,
          products: [],
          branchId: 175,
          sellsAgentId: 0,
        },
      ])
    );

    let result: any;

    useCase.execute(dateRangeTest).subscribe(resp => {
      result = resp;
    });

    tick();

    expect(dateMapperSpy).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);

    expect(result.length).toBe(5);
  }));
});
