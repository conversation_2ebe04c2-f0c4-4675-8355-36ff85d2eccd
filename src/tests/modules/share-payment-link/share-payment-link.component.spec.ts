import { Async<PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { TransactionBehaviorService } from '@aplazo/front-analytics/kount';
import { provideNotifierTesting } from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { AplazoIconComponent } from '@aplazo/shared-ui/icon';
import { AplazoTabsComponents } from '@aplazo/shared-ui/tabs';
import { DialogRef } from '@ngneat/dialog';
import { StatsigService } from '@statsig/angular-bindings';
import { provideEnvironmentNgxMask } from 'ngx-mask';
import { RequestOTPUseCase } from 'src/app/modules/share-payment-link/no-internet-purchase/application/request-otp.usecase';
import { provideI18NTesting } from 'src/tests/i18n.local';
import { AnalyticsService } from '../../../app/core/application/services/analytics.service';
import { EventService } from '../../../app/core/domain/repositories/events-service';
import { ConfirmFirstInstallmentUsecase } from '../../../app/modules/share-payment-link/application/confirm-first-installment.usecase';
import { RequestAuthorizationCodeUsecase } from '../../../app/modules/share-payment-link/application/request-authorization-code.usecase';
import { SendLinkUsecase } from '../../../app/modules/share-payment-link/application/send-link.usecase';
import { GetQrImageService } from '../../../app/modules/share-payment-link/application/services/qr-image.service';
import { FinishWithBarcodeUsecase } from '../../../app/modules/share-payment-link/barcode/application/finish-with-barcode.usecase';
import { DynamicTokenService } from '../../../app/modules/share-payment-link/dynamic-token/dynamic-token.service';
import { LocalPaymentLinkComponent } from '../../../app/modules/share-payment-link/local-payment-link/local-payment-link.component';
import { ValidateOTPUseCase } from '../../../app/modules/share-payment-link/no-internet-purchase/application/validate-otp.usecase';
import { SendAuthCodeFormComponent } from '../../../app/modules/share-payment-link/send-auth-code-form/send-auth-code-form.component';
import { SentWhatsappLinkFormComponent } from '../../../app/modules/share-payment-link/sent-whatsapp-link-form/sent-whatsapp-link-form.component';
import { SharePaymentLinkComponent } from '../../../app/modules/share-payment-link/share-payment-link.component';
import { TogglePaymentLinkDialogService } from '../../../app/modules/share-payment-link/toggle-payment-link-dialog.service';
import { LocalAnalyticsService } from '../../core/services/impl/local-analytics.service';
import { LocalEventsService } from '../../core/services/impl/local-event.service';
import { LocalGetQrImageService } from './infra/services/local-get-qr-image.service';

const setup = (
  args: {
    flags?: Partial<{
      b2b_front_posui_barcode_enabled: boolean;
      b2b_front_posui_auth_code: boolean;
      b2b_front_posui_dynamic_tokens: boolean;
    }>;
  } = {}
) => {
  const defaultConfig = {
    b2b_front_posui_barcode_enabled: true,
    b2b_front_posui_auth_code: true,
    b2b_front_posui_dynamic_tokens: true,
  };

  const config = {
    flags: {
      b2b_front_posui_barcode_enabled:
        args?.flags?.b2b_front_posui_barcode_enabled != null
          ? args?.flags?.b2b_front_posui_barcode_enabled
          : defaultConfig.b2b_front_posui_barcode_enabled,
      b2b_front_posui_auth_code:
        args?.flags?.b2b_front_posui_auth_code != null
          ? args?.flags?.b2b_front_posui_auth_code
          : defaultConfig.b2b_front_posui_auth_code,
      b2b_front_posui_dynamic_tokens:
        args?.flags?.b2b_front_posui_dynamic_tokens != null
          ? args?.flags?.b2b_front_posui_dynamic_tokens
          : defaultConfig.b2b_front_posui_dynamic_tokens,
    },
  };

  TestBed.configureTestingModule({
    imports: [
      AplazoTabsComponents,
      AplazoCardComponent,
      AplazoIconComponent,
      AplazoButtonComponent,
      AplazoFormFieldDirectives,
      ReactiveFormsModule,
      SentWhatsappLinkFormComponent,
      LocalPaymentLinkComponent,
      NgIf,
      AsyncPipe,
      SendAuthCodeFormComponent,
    ],
    providers: [
      TogglePaymentLinkDialogService,
      provideEnvironmentNgxMask(),
      provideNotifierTesting(),
      {
        provide: DialogRef,
        useValue: {
          close: () => void 0,
          data: {
            loan: {
              loanId: 123,
            },
          },
        },
      },
      {
        provide: StatsigService,
        useValue: {
          checkGate: (name: keyof typeof config.flags) => {
            const flags = config.flags;
            const flag = flags[name];

            return flag;
          },
        },
      },
      {
        provide: SendLinkUsecase,
        useValue: jasmine.createSpyObj('SendLinkUsecase', ['execute']),
      },
      {
        provide: GetQrImageService,
        useClass: LocalGetQrImageService,
      },
      {
        provide: AnalyticsService,
        useClass: LocalAnalyticsService,
      },
      {
        provide: EventService,
        useClass: LocalEventsService,
      },
      {
        provide: RequestAuthorizationCodeUsecase,
        useValue: jasmine.createSpyObj('RequestAuthorizationCodeUsecase', [
          'execute',
        ]),
      },
      {
        provide: ConfirmFirstInstallmentUsecase,
        useValue: jasmine.createSpyObj('ConfirmFirstInstallmentUsecase', [
          'execute',
        ]),
      },
      {
        provide: FinishWithBarcodeUsecase,
        useValue: jasmine.createSpyObj('FinishWithBarcodeUsecase', ['execute']),
      },
      {
        provide: RequestOTPUseCase,
        useValue: jasmine.createSpyObj('RequestOTPUseCase', ['execute']),
      },
      {
        provide: ValidateOTPUseCase,
        useValue: jasmine.createSpyObj('ValidateOTPUseCase', ['execute']),
      },
      {
        provide: DynamicTokenService,
        useValue: {} as any,
      },
      {
        provide: TransactionBehaviorService,
        useValue: {
          seesionId: '123',
        },
      },
      provideHttpClientTesting(),
      provideI18NTesting('cart'),
    ],
  });

  const fixture = TestBed.createComponent(SharePaymentLinkComponent);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  const dialogRef = TestBed.inject(DialogRef);

  return { fixture, component, dialogRef };
};

describe('SharePaymentLinkComponent', () => {
  const aplazoTabQuery = By.css('li.aplazo-tab__item > button.aplazo-tab');

  it('should create', () => {
    const { component } = setup();

    expect(component).toBeTruthy();
  });

  it("should call dialogRef's close method", () => {
    const { component, dialogRef } = setup();

    const spy = spyOn(dialogRef, 'close');

    component.close();

    expect(spy).toHaveBeenCalledTimes(1);
  });

  it('should show barcode tab if barcodeEnabled flag is true', () => {
    const { fixture } = setup({
      flags: {
        b2b_front_posui_barcode_enabled: true,
        b2b_front_posui_auth_code: false,
        b2b_front_posui_dynamic_tokens: false,
      },
    });

    const tabs = fixture.debugElement.queryAll(aplazoTabQuery);

    const tab = tabs.filter(
      t => t.nativeElement.textContent.trim() === 'Código de Barras'
    );

    expect(tab.length).toBe(1);
  });

  it('should not show barcode tab if barcodeEnabled flag is false', fakeAsync(() => {
    const { fixture } = setup({
      flags: {
        b2b_front_posui_barcode_enabled: false,
        b2b_front_posui_auth_code: false,
        b2b_front_posui_dynamic_tokens: false,
      },
    });

    tick();
    fixture.detectChanges();

    const tabs = fixture.debugElement.queryAll(aplazoTabQuery);

    const [tab] = tabs.filter(
      t => t.nativeElement.textContent.trim() === 'Código de Barras'
    );

    const tabParent = tab.parent;

    expect(tab).toBeDefined();
    expect(
      tabParent?.nativeElement.classList.contains('aplazo-tab__item--hidden')
    ).toBeTruthy();
  }));

  it('should show auth code tab if authCodeEnabled flag is true', () => {
    const { fixture } = setup({
      flags: {
        b2b_front_posui_barcode_enabled: false,
        b2b_front_posui_auth_code: true,
        b2b_front_posui_dynamic_tokens: false,
      },
    });

    const tabs = fixture.debugElement.queryAll(aplazoTabQuery);

    const authCodeTab = tabs.filter(
      t => t.nativeElement.textContent.trim() === 'Código de autorización'
    );

    expect(authCodeTab.length).toBe(1);
  });

  it('should not show auth code tab if authCodeEnabled flag is false', fakeAsync(() => {
    const { fixture } = setup({
      flags: {
        b2b_front_posui_barcode_enabled: false,
        b2b_front_posui_auth_code: false,
        b2b_front_posui_dynamic_tokens: false,
      },
    });

    tick();
    fixture.detectChanges();

    const tabs = fixture.debugElement.queryAll(aplazoTabQuery);

    const [tab] = tabs.filter(
      t => t.nativeElement.textContent.trim() === 'Código de autorización'
    );

    const tabParent = tab.parent;

    expect(tab).toBeDefined();
    expect(
      tabParent?.nativeElement.classList.contains('aplazo-tab__item--hidden')
    ).toBeTruthy();
  }));

  it('should show dynamic token tab if dynamicTokenEnabled flag is true', () => {
    const { fixture } = setup({
      flags: {
        b2b_front_posui_barcode_enabled: false,
        b2b_front_posui_auth_code: false,
        b2b_front_posui_dynamic_tokens: true,
      },
    });

    const tabs = fixture.debugElement.queryAll(aplazoTabQuery);

    const dynamicTokenTab = tabs.filter(
      t => t.nativeElement.textContent.trim() === 'Compra Sin Internet'
    );

    expect(dynamicTokenTab.length).toBe(1);
  });

  it('should not show dynamic token tab if dynamicTokenEnabled flag is false', fakeAsync(() => {
    const { fixture } = setup({
      flags: {
        b2b_front_posui_barcode_enabled: false,
        b2b_front_posui_auth_code: false,
        b2b_front_posui_dynamic_tokens: false,
      },
    });

    tick();
    fixture.detectChanges();

    const tabs = fixture.debugElement.queryAll(aplazoTabQuery);

    const [tab] = tabs.filter(
      t => t.nativeElement.textContent.trim() === 'Compra Sin Internet'
    );

    const tabParent = tab.parent;

    expect(tab).toBeDefined();
    expect(
      tabParent?.nativeElement.classList.contains('aplazo-tab__item--hidden')
    ).toBeTruthy();
  }));
});
