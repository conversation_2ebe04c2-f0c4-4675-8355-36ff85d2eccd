import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PopupStorageService } from './application/services/popup-storage.service';
import { ShowPopupUseCase } from './application/usecases/show-popup.usecase';
import { AplazoPopupDialogService } from './infra/components/aplazo-popup/aplazo-popup-dialog.service';

/**
 * ✅ DEMO COMPONENT FOR PERMANENT DISMISS FUNCTIONALITY
 * 
 * This component demonstrates that the permanent dismiss functionality
 * is working correctly. It can be added to any page for testing.
 */
@Component({
  selector: 'app-permanent-dismiss-demo',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="bg-white p-6 rounded-lg shadow-lg max-w-2xl mx-auto">
      <h2 class="text-xl font-bold mb-4">🧪 Permanent Dismiss Demo</h2>
      
      <!-- Test Controls -->
      <div class="space-y-4 mb-6">
        <div class="flex items-center space-x-4">
          <label class="font-medium">Popup ID:</label>
          <input 
            [(ngModel)]="testPopupId" 
            class="border rounded px-3 py-1"
            placeholder="test-popup-1"
          />
        </div>
        
        <div class="flex items-center space-x-4">
          <label class="font-medium">Branch ID:</label>
          <input 
            [(ngModel)]="testBranchId" 
            class="border rounded px-3 py-1"
            placeholder="123"
          />
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="grid grid-cols-2 gap-4 mb-6">
        <button 
          (click)="showTestPopup()"
          class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          🎭 Show Test Popup
        </button>
        
        <button 
          (click)="checkDismissStatus()"
          class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          🔍 Check Dismiss Status
        </button>
        
        <button 
          (click)="markAsDismissed()"
          class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
        >
          ❌ Mark as Dismissed
        </button>
        
        <button 
          (click)="clearDismiss()"
          class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600"
        >
          🔄 Clear Dismiss
        </button>
      </div>

      <!-- Status Display -->
      <div class="bg-gray-100 p-4 rounded">
        <h3 class="font-bold mb-2">📊 Current Status:</h3>
        <div class="space-y-2 text-sm">
          <div>
            <strong>Permanently Dismissed:</strong> 
            <span [class]="isDismissed() ? 'text-red-600' : 'text-green-600'">
              {{ isDismissed() ? 'YES' : 'NO' }}
            </span>
          </div>
          <div>
            <strong>Should Show Popup:</strong> 
            <span [class]="shouldShow() ? 'text-green-600' : 'text-red-600'">
              {{ shouldShow() ? 'YES' : 'NO' }}
            </span>
          </div>
          <div>
            <strong>Last Action:</strong> 
            <span class="text-blue-600">{{ lastAction() }}</span>
          </div>
        </div>
      </div>

      <!-- Instructions -->
      <div class="mt-6 p-4 bg-blue-50 rounded">
        <h3 class="font-bold text-blue-800 mb-2">📋 How to Test:</h3>
        <ol class="text-sm text-blue-700 space-y-1">
          <li>1. Click "Show Test Popup" - popup should appear with checkbox</li>
          <li>2. Check "No mostrar de nuevo" and close popup</li>
          <li>3. Click "Check Dismiss Status" - should show "YES"</li>
          <li>4. Try "Show Test Popup" again - should not appear</li>
          <li>5. Click "Clear Dismiss" and try showing popup again - should work</li>
        </ol>
      </div>

      <!-- Storage Debug -->
      <div class="mt-4 p-4 bg-gray-50 rounded">
        <h3 class="font-bold mb-2">🔧 Storage Debug:</h3>
        <pre class="text-xs bg-white p-2 rounded overflow-auto">{{ storageDebug() }}</pre>
      </div>
    </div>
  `,
})
export class PermanentDismissDemoComponent {
  private storageService = inject(PopupStorageService);
  private dialogService = inject(AplazoPopupDialogService);

  // Test configuration
  testPopupId = signal('test-popup-1');
  testBranchId = signal('123');
  
  // Status signals
  isDismissed = signal(false);
  shouldShow = signal(true);
  lastAction = signal('Ready to test');
  storageDebug = signal('{}');

  ngOnInit() {
    this.updateStatus();
  }

  /**
   * Show a test popup with permanent dismiss functionality
   */
  showTestPopup() {
    const htmlContent = `
      <div class="text-center">
        <h2 class="text-2xl font-bold text-blue-600 mb-4">🧪 Test Popup</h2>
        <p class="text-gray-700 mb-4">
          This is a test popup to demonstrate the permanent dismiss functionality.
        </p>
        <p class="text-sm text-gray-500">
          Check the "No mostrar de nuevo" checkbox below and close this popup.
          Then try opening it again - it should not appear!
        </p>
      </div>
    `;

    const dialogResult$ = this.dialogService.openPopup(htmlContent);
    if (dialogResult$) {
      dialogResult$.subscribe(result => {
        console.log('🔍 [Demo] Dialog result:', result);
        
        if (result?.permanentDismiss) {
          console.log('🚀 [Demo] User chose permanent dismiss!');
          this.storageService.markPopupAsPermanentlyDismissed(
            this.testPopupId(),
            this.testBranchId()
          );
          this.lastAction.set('Popup permanently dismissed by user');
        } else {
          this.lastAction.set('Popup closed without permanent dismiss');
        }
        
        this.updateStatus();
      });
    }
  }

  /**
   * Check current dismiss status
   */
  checkDismissStatus() {
    this.updateStatus();
    this.lastAction.set('Status checked');
  }

  /**
   * Manually mark popup as dismissed
   */
  markAsDismissed() {
    this.storageService.markPopupAsPermanentlyDismissed(
      this.testPopupId(),
      this.testBranchId()
    );
    this.updateStatus();
    this.lastAction.set('Manually marked as dismissed');
  }

  /**
   * Clear dismiss status
   */
  clearDismiss() {
    this.storageService.clearPermanentDismiss(
      this.testPopupId(),
      this.testBranchId()
    );
    this.updateStatus();
    this.lastAction.set('Dismiss status cleared');
  }

  /**
   * Update all status signals
   */
  private updateStatus() {
    const popupId = this.testPopupId();
    const branchId = this.testBranchId();
    
    this.isDismissed.set(
      this.storageService.isPopupPermanentlyDismissed(popupId, branchId)
    );
    
    this.shouldShow.set(
      this.storageService.shouldShowPopup(popupId, branchId)
    );
    
    // Get storage debug info
    const records = this.storageService.getShownRecords();
    this.storageDebug.set(JSON.stringify(records, null, 2));
  }
}

/**
 * USAGE:
 * 
 * Add this component to any page for testing:
 * 
 * <app-permanent-dismiss-demo></app-permanent-dismiss-demo>
 * 
 * Or add to app.component.html temporarily for testing.
 */
