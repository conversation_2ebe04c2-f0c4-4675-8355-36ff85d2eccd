import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { PopupDemoComponent } from '../../../../../app/modules/shared/popup/components/popup-demo.component';
import { PopupTriggerService } from '../../../../../app/modules/shared/popup/application/services/popup-trigger.service';
import { PopupStorageService } from '../../../../../app/modules/shared/popup/application/services/popup-storage.service';
import { PopupRepository } from '../../../../../app/modules/shared/popup/infra/repositories/popup.repository';
import { StoreService } from '../../../../../app/core/application/services/store.service';
import { of } from 'rxjs';

describe('PopupDemoComponent', () => {
  let fixture: ComponentFixture<PopupDemoComponent>;
  let component: PopupDemoComponent;
  let popupTriggerService: jasmine.SpyObj<PopupTriggerService>;
  let popupStorageService: jasmine.SpyObj<PopupStorageService>;
  let popupRepository: jasmine.SpyObj<PopupRepository>;
  let storeService: jasmine.SpyObj<StoreService>;

  beforeEach(async () => {
    const triggerSpy = jasmine.createSpyObj('PopupTriggerService', [
      'triggerPopupIfNeeded',
      'triggerAfterLogin',
      'triggerAfterPaymentSuccess',
      'shouldShowPopup',
    ]);
    const storageSpy = jasmine.createSpyObj('PopupStorageService', [
      'wasPopupShownToday',
      'clearRecordsForDate',
      'clearAllRecords',
      'getShownRecords',
    ]);
    const repositorySpy = jasmine.createSpyObj('PopupRepository', [
      'getAvailablePopups',
    ]);
    const storeSpy = jasmine.createSpyObj('StoreService', [], {
      selectedBranch$: of({ id: 36, name: 'Test Branch' }),
    });

    await TestBed.configureTestingModule({
      imports: [PopupDemoComponent],
      providers: [
        { provide: PopupTriggerService, useValue: triggerSpy },
        { provide: PopupStorageService, useValue: storageSpy },
        { provide: PopupRepository, useValue: repositorySpy },
        { provide: StoreService, useValue: storeSpy },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(PopupDemoComponent);
    component = fixture.componentInstance;
    popupTriggerService = TestBed.inject(
      PopupTriggerService
    ) as jasmine.SpyObj<PopupTriggerService>;
    popupStorageService = TestBed.inject(
      PopupStorageService
    ) as jasmine.SpyObj<PopupStorageService>;
    popupRepository = TestBed.inject(
      PopupRepository
    ) as jasmine.SpyObj<PopupRepository>;
    storeService = TestBed.inject(StoreService) as jasmine.SpyObj<StoreService>;

    // Setup default mocks with proper return values
    popupRepository.getAvailablePopups.and.returnValue(of([]));
    popupStorageService.getShownRecords.and.returnValue([]);
    popupStorageService.wasPopupShownToday.and.returnValue(false);
    popupTriggerService.triggerPopupIfNeeded.and.returnValue(of(true));
    popupTriggerService.triggerAfterLogin.and.returnValue(of(true));
    popupTriggerService.triggerAfterPaymentSuccess.and.returnValue(of(true));
    popupTriggerService.shouldShowPopup.and.returnValue(of(true));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with current branch ID', fakeAsync(() => {
    fixture.detectChanges();
    tick();

    expect(component.currentBranchId()).toBe('36');
    expect(component.config.branchId).toBe('36');
  }));

  it('should load available popups on initialization', fakeAsync(() => {
    const mockPopups = [
      {
        popupId: 'POPUP-001',
        priority: 1,
        startAt: '2024-01-01',
        endAt: '2024-12-31',
      },
    ];
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    fixture = TestBed.createComponent(PopupDemoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    tick();
    expect(component.availablePopups()).toEqual(mockPopups);
  }));

  it('should trigger popup manually', fakeAsync(() => {
    popupTriggerService.triggerPopupIfNeeded.and.returnValue(of(true));
    fixture = TestBed.createComponent(PopupDemoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    tick();
    component.config.branchId = '36';
    component.config.customHtml = '';
    component.triggerPopup();
    tick();
    expect(popupTriggerService.triggerPopupIfNeeded).toHaveBeenCalledWith({
      branchId: '36',
    });
    expect(component.lastResult()).toBeTruthy();
    expect(component.lastResult().success).toBe(true);
    expect(component.lastResult().triggerType).toBe('manual');
  }));

  it('should trigger popup after login', fakeAsync(() => {
    component.config.triggerType = 'login';
    fixture.detectChanges();
    tick();

    component.triggerPopup();
    tick();

    expect(popupTriggerService.triggerAfterLogin).toHaveBeenCalledWith('36');
    expect(component.lastResult().triggerType).toBe('login');
  }));

  it('should trigger popup after payment', fakeAsync(() => {
    component.config.triggerType = 'payment';
    fixture.detectChanges();
    tick();

    component.triggerPopup();
    tick();

    expect(popupTriggerService.triggerAfterPaymentSuccess).toHaveBeenCalledWith(
      '36'
    );
    expect(component.lastResult().triggerType).toBe('payment');
  }));

  it('should check popup status', fakeAsync(() => {
    fixture.detectChanges();
    tick();

    component.checkPopupStatus();
    tick();

    expect(popupTriggerService.shouldShowPopup).toHaveBeenCalledWith({
      branchId: '36',
    });
    expect(component.lastResult().action).toBe('status_check');
    expect(component.lastResult().shouldShow).toBe(true);
  }));

  it('should clear today records', fakeAsync(() => {
    fixture.detectChanges();
    tick();

    component.clearTodayRecords();
    tick();

    expect(popupStorageService.clearRecordsForDate).toHaveBeenCalledWith(
      component.today()
    );
    expect(component.lastResult().action).toBe('clear_today');
  }));

  it('should clear all records', fakeAsync(() => {
    fixture.detectChanges();
    tick();

    component.clearAllRecords();
    tick();

    expect(popupStorageService.clearAllRecords).toHaveBeenCalled();
    expect(component.lastResult().action).toBe('clear_all');
  }));

  it('should handle errors during popup trigger', fakeAsync(() => {
    popupTriggerService.triggerPopupIfNeeded.and.returnValue(of(false));
    fixture = TestBed.createComponent(PopupDemoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    tick();
    component.config.branchId = '36';
    component.config.customHtml = '';
    component.triggerPopup();
    tick();
    expect(component.lastResult().success).toBe(false);
  }));

  it('should update shown today count', fakeAsync(() => {
    const mockPopups = [
      {
        popupId: 'POPUP-001',
        priority: 1,
        startAt: '2024-01-01',
        endAt: '2024-12-31',
      },
      {
        popupId: 'POPUP-002',
        priority: 2,
        startAt: '2024-01-01',
        endAt: '2024-12-31',
      },
    ];
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorageService.wasPopupShownToday.and.callFake(
      (popupId: string) => popupId === 'POPUP-001'
    );
    fixture = TestBed.createComponent(PopupDemoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    tick();
    component.config.branchId = '36';
    (component as any).updateShownToday();
    expect(component.shownToday()).toBe(1);
  }));
});
