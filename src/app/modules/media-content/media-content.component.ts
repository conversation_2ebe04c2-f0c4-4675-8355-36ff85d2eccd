import { AsyncPipe } from '@angular/common';
import { Component, inject } from '@angular/core';
import { NotifierService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { AplazoCommonMessageComponents } from '@aplazo/shared-ui/merchant';
import { AplazoTabsComponents } from '@aplazo/shared-ui/tabs';
import { AplazoYoutubeDialogComponent } from '@aplazo/shared-ui/youtube';
import { iconPlayCircle } from '@aplazo/ui-icons';
import { DialogService } from '@ngneat/dialog';
import { StatsigService } from '@statsig/angular-bindings';
import {
  BehaviorSubject,
  Observable,
  forkJoin,
  map,
  of,
  switchMap,
  take,
  tap,
  withLatestFrom,
} from 'rxjs';
import { AnalyticsService } from '../../core/application/services/analytics.service';
import { StoreService } from '../../core/application/services/store.service';
import {
  IMediaContentUIDto,
  ITrainingMediaContentUIDto,
} from '../../core/domain/notification';
import { NotificationsHandlerService } from '../../core/services/notifications-handler.service';
import { AplazoMediaCardThumbnailComponent } from './media-content-card.component';

@Component({
  standalone: true,
  selector: 'aplazo-media-content',
  imports: [
    AplazoCardComponent,
    AplazoIconComponent,
    AplazoButtonComponent,
    AplazoCommonMessageComponents,
    AplazoTabsComponents,
    AplazoMediaCardThumbnailComponent,
    AsyncPipe,
  ],
  template: `
    <div class="px-8 pt-6 pb-[30vh]">
      <aplz-ui-card appearance="flat">
        @if ((hasContent$ | async) === true) {
          <aplz-ui-tab-group (tabSelectionChange)="changeTab($event)">
            @for (tab of tabOptions$ | async; track tab) {
              <aplz-ui-tab [label]="tab.label"> </aplz-ui-tab>
            }
          </aplz-ui-tab-group>

          <h2 class="text-xl font-medium mt-4">Contenido</h2>

          <article class="grid grid-cols-auto-fill-xs-1 gap-4">
            @if (items$ | async; as items) {
              @for (item of items; track item) {
                <aplazo-media-card-content-thumbnail [media]="item">
                  @if (item.isValidVideoUrl) {
                    <button
                      aplzButton
                      aplzAppearance="basic"
                      size="sm"
                      [rounded]="true"
                      (click)="openVideo(item)">
                      <aplz-ui-icon name="play-circle" size="md"></aplz-ui-icon>
                    </button>
                  }
                </aplazo-media-card-content-thumbnail>
              }
            }
          </article>
        } @else {
          <div class="mt-6 pt-8">
            @if (emptyNotificationsInjectedText$ | async; as msgText) {
              <aplz-ui-common-message
                [i18Text]="{
                  title: msgText.title,
                  description: msgText.description
                }">
              </aplz-ui-common-message>
            }
          </div>
        }
      </aplz-ui-card>
    </div>
  `,
})
export class MediaContentComponent {
  readonly #notificationsHandler = inject(NotificationsHandlerService);
  readonly #notifier = inject(NotifierService);
  readonly #dialogService = inject(DialogService);
  readonly #iconRegistry = inject(AplazoIconRegistryService);
  readonly #analytics = inject(AnalyticsService);
  readonly #statsig = inject(StatsigService);
  readonly #storeService = inject(StoreService);

  readonly #trainingContent$: Observable<ITrainingMediaContentUIDto> =
    this.#notificationsHandler.getTrainingContent$();

  readonly tabOptions$ = this.#trainingContent$.pipe(
    map(content => {
      if (!content) {
        return [];
      }

      return content.tags.map(item => ({
        id: item,
        label: item,
        value: item,
      }));
    })
  );

  readonly #tabSelectionChange$ = new BehaviorSubject<number>(0);

  items$: Observable<IMediaContentUIDto[]> = this.#tabSelectionChange$.pipe(
    withLatestFrom(this.tabOptions$),
    switchMap(([index, tabOptions]) =>
      this.#trainingContent$.pipe(
        map(content => {
          if (!content?.content || index < 0 || index >= tabOptions.length) {
            return [];
          }

          return content.content.filter(item =>
            item.tags.includes(tabOptions[index].label)
          );
        })
      )
    )
  );

  hasContent$: Observable<boolean> = this.#trainingContent$.pipe(
    map(data => data?.tags?.length > 0 && data?.content?.length > 0)
  );

  emptyNotificationsInjectedText$: Observable<{
    title: string;
    description: string;
  }> = of({
    title: 'Ups!',
    description: 'Aún no tenemos contenido multimedia, espéralo muy pronto',
  });

  constructor() {
    this.#iconRegistry.registerIcons([iconPlayCircle]);
  }

  changeTab(event: { index: number }): void {
    this.#tabSelectionChange$.next(event.index);
  }

  openVideo(data: IMediaContentUIDto): void {
    if (!data || !data.isValidVideoUrl) {
      throw new Error('There is no enough data to open the video');
    }

    const url = new URL(data.urlVideo);

    const videoIdFromQuery = url.searchParams.get('v');
    const videoIdFromPathname = url.pathname.replace('/', '');
    const isYoutubeOrigin = [
      'https://www.youtube.com',
      'https://youtube.com',
      'https://youtu.be',
    ].some(origin => url.origin === origin);

    if (!isYoutubeOrigin) {
      throw new Error('There is no valid youtube video url');
    }

    let videoId = videoIdFromQuery;

    if (!url.searchParams.has('v') && !videoIdFromQuery) {
      videoId = videoIdFromPathname;
    }

    if (!videoId || videoId.trim() === '') {
      this.#notifier.warning({
        title: 'Video no encontrado',
      });
      return;
    }

    this.#dialogService.open(AplazoYoutubeDialogComponent, {
      data: {
        videoId,
        title: data.title,
        description: data.description,
      },
      minWidth: '250px',
      maxHeight: '90vh',
      minHeight: '450px',
    });

    this.#analytics.track('buttonClick', {
      buttonName: 'aplazoVersityOpenVideo',
      timestamp: new Date().getTime(),
      title: data.title,
    });

    forkJoin({
      merchantId: this.#storeService.getMerchantId$().pipe(take(1)),
      branchId: this.#storeService.selectedBranch$.pipe(take(1)),
    })
      .pipe(
        map(({ merchantId, branchId }) => ({
          merchantId: String(merchantId),
          branchId: String(branchId),
        })),
        tap(({ merchantId, branchId }) => {
          this.#statsig.logEvent('posui_aplazoversity_open_video', data.title, {
            merchantId,
            branchId,
          });
        }),
        take(1)
      )
      .subscribe();
  }
}
