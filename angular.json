{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"], "cache": {"enabled": false}}, "version": 1, "newProjectRoot": "projects", "projects": {"merchant-offline-pos-ui": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "aplazo", "i18n": {"sourceLocale": "es-MX"}, "architect": {"build": {"builder": "@ngx-env/builder:application", "options": {"outputPath": "dist/merchant-offline-pos-ui", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["src/styles.scss", "node_modules/ngx-toastr/toastr.css", "node_modules/@aplazo/shared-ui/config/air-datepicker.css", "node_modules/@aplazo/shared-ui/themes/aplazo-light.css"], "allowedCommonJsDependencies": ["*"], "preserveSymlinks": true}, "configurations": {"production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "serviceWorker": "ngsw-config.json"}, "stage": {"optimization": true, "outputHashing": "all", "sourceMap": true, "namedChunks": false, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "15mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "serviceWorker": "ngsw-config.json"}, "develop": {"preserveSymlinks": true, "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "10mb", "maximumError": "15mb"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@ngx-env/builder:dev-server", "options": {"open": false, "host": "0.0.0.0"}, "configurations": {"production": {"buildTarget": "merchant-offline-pos-ui:build:production"}, "stage": {"buildTarget": "merchant-offline-pos-ui:build:stage"}, "dev": {"buildTarget": "merchant-offline-pos-ui:build:develop"}}, "defaultConfiguration": "develop"}, "extract-i18n": {"builder": "@ngx-env/builder:extract-i18n", "options": {"buildTarget": "merchant-offline-pos-ui:build"}}, "test": {"builder": "@ngx-env/builder:karma", "options": {"preserveSymlinks": true, "main": "src/test.ts", "polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets", "src/manifest.webmanifest"], "styles": ["node_modules/@aplazo/shared-ui/config/air-datepicker.css", "node_modules/@aplazo/shared-ui/themes/aplazo-light.css", "node_modules/ngx-toastr/toastr.css", "src/styles.scss"], "scripts": [], "browsers": "Chrome"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}}