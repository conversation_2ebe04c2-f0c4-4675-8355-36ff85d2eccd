import { HttpErrorResponse } from '@angular/common/http';
import { SecurityContext } from '@angular/core';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { DomSanitizer } from '@angular/platform-browser';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { GetTermsConditionsLinkUsecase } from '../../../../../app/modules/contest-entries/application/usecases/get-terms-conditions-link.usecase';
import { ContestRepository } from '../../../../../app/modules/contest-entries/domain/repositories/contest.repository';

describe('GetTermsConditionsLinkUsecase', () => {
  let usecase: GetTermsConditionsLinkUsecase;
  let repository: jasmine.SpyObj<ContestRepository>;
  let showLoader: jasmine.Spy;
  let hideLoader: jasmine.Spy;
  let errorHandler: jasmine.Spy;
  let sanitizer: jasmine.SpyObj<DomSanitizer>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        GetTermsConditionsLinkUsecase,
        {
          provide: ContestRepository,
          useValue: jasmine.createSpyObj('ContestRepository', [
            'retriveLastTyCLink',
          ]),
        },
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: DomSanitizer,
          useValue: jasmine.createSpyObj('DomSanitizer', ['sanitize']),
        },
      ],
    });

    usecase = TestBed.inject(GetTermsConditionsLinkUsecase);
    repository = TestBed.inject(
      ContestRepository
    ) as jasmine.SpyObj<ContestRepository>;
    const loader = TestBed.inject(LoaderService);
    showLoader = spyOn(loader, 'show').and.callThrough();
    hideLoader = spyOn(loader, 'hide').and.callThrough();

    errorHandler = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
    sanitizer = TestBed.inject(DomSanitizer) as jasmine.SpyObj<DomSanitizer>;
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(GetTermsConditionsLinkUsecase);
  });

  it('should return the terms conditions link', fakeAsync(() => {
    const link = {
      termsConditions: 'https://cdn.aplazo.com/terms-conditions.pdf',
    };
    sanitizer.sanitize.and.returnValue(link.termsConditions);

    repository.retriveLastTyCLink.and.returnValue(of(link));

    let result: any;

    usecase.execute().subscribe({
      next: r => {
        result = r;
      },
      error: fail,
    });

    tick();

    expect(result).toBe(link.termsConditions);
    expect(sanitizer.sanitize).toHaveBeenCalledWith(
      SecurityContext.URL,
      link.termsConditions
    );
    expect(showLoader).toHaveBeenCalledTimes(1);
    expect(hideLoader).toHaveBeenCalledTimes(1);
    expect(errorHandler).not.toHaveBeenCalled();
  }));

  it('should propagate the error', fakeAsync(() => {
    const err = new HttpErrorResponse({
      status: 500,
      statusText: 'Internal Server Error',
    });

    repository.retriveLastTyCLink.and.returnValue(throwError(() => err));

    let result: any;

    usecase.execute().subscribe({
      next: fail,
      error: r => {
        result = r;
      },
    });

    tick();

    expect(result).toEqual(err);
    expect(showLoader).toHaveBeenCalledTimes(1);
    expect(hideLoader).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledWith(err);
  }));

  it('should sanitize the link', fakeAsync(() => {
    const link = {
      termsConditions: 'javascript:void(0)',
    };

    sanitizer.sanitize.and.returnValue(null);

    repository.retriveLastTyCLink.and.returnValue(of(link));

    let result: any;

    usecase.execute().subscribe({
      next: r => {
        result = r;
      },
      error: fail,
    });

    tick();

    expect(result).toBe('');
    expect(sanitizer.sanitize).toHaveBeenCalledWith(
      SecurityContext.URL,
      link.termsConditions
    );
    expect(showLoader).toHaveBeenCalledTimes(1);
    expect(hideLoader).toHaveBeenCalledTimes(1);
    expect(errorHandler).not.toHaveBeenCalled();
  }));
});
