import { Observable, of } from 'rxjs';
import {
  AddProductEventDataFoundation,
  CancelOrderEventDataFoundation,
  LoginEventDataFoundation,
  NewOrderEventDataFoundation,
  StoreFrontEventDataFoundation,
} from '../../../../app/core/domain/events.data-foundation';
import { EventService } from '../../../../app/core/domain/repositories/events-service';

export class LocalEventsService extends EventService {
  login(data: LoginEventDataFoundation): Observable<void> {
    console.log('EventService::login::>>', data);
    return of(void 0);
  }
  storeFront(data: StoreFrontEventDataFoundation): Observable<void> {
    console.log('EventService::storeFront::>>', data);
    return of(void 0);
  }
  newOrder(data: NewOrderEventDataFoundation): Observable<void> {
    console.log('EventService::newOrder::>>', data);
    return of(void 0);
  }
  addProduct(data: AddProductEventDataFoundation): Observable<void> {
    console.log('EventService::addProduct::>>', data);
    return of(void 0);
  }
  shareByQrCode(data: CancelOrderEventDataFoundation): Observable<void> {
    console.log('EventService::shareByQrCode::>>', data);
    return of(void 0);
  }
  shareOrder(data: CancelOrderEventDataFoundation): Observable<void> {
    console.log('EventService::shareOrder::>>', data);
    return of(void 0);
  }
  cancelOrder(data: CancelOrderEventDataFoundation): Observable<void> {
    console.log('EventService::cancelOrder::>>', data);
    return of(void 0);
  }
}
