import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, NgSwitchDefault } from '@angular/common';
import { Component, inject } from '@angular/core';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconInfo } from '@aplazo/ui-icons';
import { DialogRef } from '@ngneat/dialog';

@Component({
  standalone: true,
  selector: 'aplazo-dialog-response',
  template: `
    <aplz-ui-card size="full">
      <div class="grid gap-12 justify-items-center my-10">
        <ng-container [ngSwitch]="data?.messageType">
          <svg
            *ngSwitchCase="'error'"
            width="88"
            height="89"
            viewBox="0 0 88 89"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <circle
              cx="44.0159"
              cy="44.3519"
              r="43.207"
              fill="#FE9797"
              stroke="black"
              stroke-width="1.28976" />
            <path
              d="M31.1191 44.3516H58.2041"
              stroke="black"
              stroke-width="2.57952"
              stroke-linecap="round" />
          </svg>
          <svg
            *ngSwitchCase="'success'"
            width="103"
            height="103"
            viewBox="0 0 103 103"
            fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <circle cx="51.8166" cy="51.2463" r="34.7864" fill="#74C9A2" />
            <circle
              opacity="0.1"
              cx="51.8147"
              cy="51.2463"
              r="47.594"
              stroke="#74C9A2"
              stroke-width="7.0625" />
            <path
              d="M62.7997 42.0244L47.2622 57.5619L40.1997 50.4994"
              stroke="#060606"
              stroke-width="2.825"
              stroke-linecap="round"
              stroke-linejoin="round" />
          </svg>

          <aplz-ui-icon name="info" size="xl" *ngSwitchDefault> </aplz-ui-icon>
        </ng-container>

        <h1 *ngIf="data?.title" class="text-2xl font-bold">
          {{ data?.title }}
        </h1>

        <p
          *ngIf="data?.content"
          class="text-center max-w-xs text-dark-secondary">
          {{ data?.content }}
        </p>

        <div class="" *ngIf="data?.action">
          <button
            aplzButton
            aplzAppearance="solid"
            aplzColor="dark"
            size="md"
            [rounded]="true"
            (click)="applyAction()">
            {{ data?.action.label }}
          </button>
        </div>
      </div>
    </aplz-ui-card>
  `,
  imports: [
    NgIf,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    AplazoCardComponent,
    AplazoButtonComponent,
    AplazoIconComponent,
  ],
})
export class DialogResponseComponent {
  readonly #dialogRef = inject(DialogRef);
  readonly #iconRegistry = inject(AplazoIconRegistryService);

  readonly data = this.#dialogRef.data;

  constructor() {
    this.#iconRegistry.registerIcons([iconInfo]);
  }

  applyAction(): void {
    this.#dialogRef.close({
      action: 'close',
    });
  }
}
