import {
  DateMapper,
  notDateRangeProvided,
} from '../../../app/modules/historical-v1/date.mapper';

describe('DateMapper', () => {
  it('should throw error when no argument is provided', () => {
    // @ts-expect-error: Testing error case
    expect(() => DateMapper.fromDateRangeToRequest()).toThrowError(
      notDateRangeProvided
    );
  });

  it('should map from dd/MM/yyyy to yyyy-MM-dd', () => {
    const date = { startDate: '01/01/2020', endDate: '01/01/2020' };
    const result = DateMapper.fromDateRangeToRequest(date);

    expect(result.startDate).toBe('2020-01-01');
    expect(result.endDate).toBe('2020-01-01');
  });
});
