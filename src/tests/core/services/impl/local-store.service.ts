import { BehaviorSubject, Observable, map, shareReplay } from 'rxjs';
import {
  IBranchFeatureFlags,
  IShareLinkFlags,
  StoreService,
} from '../../../../app/core/application/services/store.service';
import { IUserJwt } from '../../../../app/core/domain/access-token';
import { Branch } from '../../../../app/core/domain/entities';
import { MerchantConfig } from '../../../../app/core/domain/merchant-config';

export class LocalStore implements StoreService {
  hasBranchShareLinkEnabled$: Observable<boolean>;
  private _token$ = new BehaviorSubject<string | null>(null);
  private _decodedToken$ = new BehaviorSubject<IUserJwt | null>(null);
  private _merchantConfig$ = new BehaviorSubject<MerchantConfig | null>(null);
  private _selectedBranch$ = new BehaviorSubject<Branch | null>(null);
  private _merchantName$ = new BehaviorSubject<string>('');

  public getToken(): string | null {
    return this._token$.value;
  }

  merchantName$ = this._merchantName$.asObservable();

  public get token$(): Observable<string | null> {
    return this._token$.pipe(shareReplay(1));
  }

  public get merchantConfig$(): Observable<MerchantConfig | null> {
    return this._merchantConfig$.pipe(shareReplay(1));
  }

  public get decodedToken$(): Observable<IUserJwt | null> {
    return this._decodedToken$.pipe(shareReplay(1));
  }

  public getMerchantId$(): Observable<number | null> {
    return this.merchantConfig$.pipe(
      map(config => (config ? config.merchantId : null))
    );
  }

  public get selectedBranch$(): Observable<Branch | null> {
    return this._selectedBranch$.pipe(shareReplay(1));
  }

  public setToken(token: string | null) {
    this._token$.next(token);
  }

  public setMerchantConfig(config: MerchantConfig | null) {
    this._merchantConfig$.next(config);
  }

  public setDecodedToken(user: IUserJwt | null) {
    this._decodedToken$.next(user);
  }

  public setSelectedBranch(branch: Branch | null) {
    this._selectedBranch$.next(branch);
  }

  async setMerchantName(merchantName: string | null): Promise<void> {
    if (merchantName == null) {
      this._merchantName$.next('');
      return;
    }

    if (typeof merchantName === 'string' && merchantName.length > 0) {
      this._merchantName$.next(merchantName);
      return;
    }

    this._merchantName$.next('');
  }

  public get isLoggedIn$(): Observable<boolean> {
    return this.token$.pipe(map(token => !!token));
  }

  public get userRole$(): Observable<string> {
    return this.decodedToken$.pipe(
      map(user => {
        if (!user) {
          return '';
        }

        return user.role;
      })
    );
  }

  public get integrationType$(): Observable<string> {
    return this.decodedToken$.pipe(
      map(user => {
        if (!user) {
          return '';
        }

        return user.integrationType;
      })
    );
  }

  public get selectedBranchName$(): Observable<string | null> {
    return this.selectedBranch$.pipe(
      map(branch => {
        if (!branch) {
          return null;
        }

        return branch.name;
      })
    );
  }

  public get selectedBranchShareLinkFlags$(): Observable<IShareLinkFlags | null> {
    return this.selectedBranch$.pipe(
      map(branch => {
        if (!branch || !branch.shareLinkFlags) {
          return null;
        }

        return {
          isWhatsappAllowed: branch.shareLinkFlags.isWhatsappAllowed,
          isQrAllowed: branch.shareLinkFlags.isQrAllowed,
          isSMSAllowed: branch.shareLinkFlags.isSMSAllowed,
        };
      })
    );
  }

  public get selectedBranchFeatureFlags$(): Observable<IBranchFeatureFlags | null> {
    return this.selectedBranch$.pipe(
      map(branch => {
        if (!branch || !branch.branchFeatureFlags) {
          return null;
        }

        return {
          isSellAgentTrackEnable:
            branch.branchFeatureFlags.isSellAgentTrackEnable,
          isSupportChatEnable: branch.branchFeatureFlags.isSupportChatEnable,
          isOrderRefundEnable: branch.branchFeatureFlags.isOrderRefundEnable,
          isTodayReportEnable: branch.branchFeatureFlags.isTodayReportEnable,
        };
      })
    );
  }

  public get hasBranchCreationEnabled$(): Observable<boolean> {
    return this.merchantConfig$.pipe(
      map(config => config?.isBranchCreationEnable || false)
    );
  }

  public get branches$(): Observable<Branch[]> {
    return this.merchantConfig$.pipe(
      map(config => {
        if (!config) {
          return [];
        }

        return config.branches;
      })
    );
  }

  public get hasBranches$(): Observable<boolean> {
    return this.merchantConfig$.pipe(
      map(config => {
        if (!config) {
          return false;
        }

        return config.branches.length > 0;
      })
    );
  }

  public get minimumOrderAmount$(): Observable<number | null> {
    return this.merchantConfig$.pipe(
      map(config => {
        if (!config || !config.minimumOrderAmount) {
          return null;
        }

        return config.minimumOrderAmount;
      })
    );
  }

  public get merchantLogoUrl$(): Observable<string | null> {
    return this.merchantConfig$.pipe(
      map(config => {
        if (!config) {
          return null;
        }

        return this.isValidImagePath(config.logoUrl) ? config.logoUrl : null;
      })
    );
  }

  public clearStore(): void {
    this.setToken(null);
    this.setMerchantConfig(null);
    this.setSelectedBranch(null);
  }

  public async setStoreFromSessionStorage(): Promise<void> {
    return Promise.resolve();
  }

  private isValidImagePath(path: string): boolean {
    const hasImageExtension =
      path.match(/\.(jpg|jpeg|png|webp|avif|gif|svg)$/) != null;
    const hasValidProtocol = path.match(/^(http|https):\/\//) != null;

    return hasImageExtension && hasValidProtocol;
  }
}
