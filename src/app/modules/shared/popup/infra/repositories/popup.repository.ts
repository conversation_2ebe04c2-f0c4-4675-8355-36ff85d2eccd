import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of, catchError } from 'rxjs';
import {
  POS_ENVIRONMENT_CORE,
  PosEnvironmentCoreType,
} from '../../../../../../app-core/domain/environments';

export interface PopupMeta {
  popupId: string;
  startAt: string;
  endAt: string;
  priority: number;
}

@Injectable({ providedIn: 'root' })
export class PopupRepository {
  private http = inject(HttpClient);
  private environment = inject<PosEnvironmentCoreType>(POS_ENVIRONMENT_CORE);
  private apiUrl = this.environment.promoApiUrl;

  /**
   * GET /api/v1/popups?branchId={branchId}
   * Obtiene los popups disponibles para una branch específica
   */
  getAvailablePopups(branchId: string): Observable<PopupMeta[]> {
    // Por ahora, usar mocks por defecto para evitar errores 401
    // TODO: Implementar verificación de autenticación cuando el backend esté listo
    console.log(
      '🔍 [PopupRepository] Getting available popups for branch:',
      branchId
    );
    const mockPopups = this.getMockPopups();
    console.log('🔍 [PopupRepository] Returning mock popups:', mockPopups);
    return of(mockPopups);

    // Código original comentado para cuando el backend esté listo:
    /*
    return this.http
      .get<PopupMeta[]>(`${this.apiUrl}/api/v1/popups?branchId=${branchId}`)
      .pipe(
        catchError((error: any) => {
          console.warn(
            'Error fetching popups from API, using mock data:',
            error
          );
          return this.getMockPopups();
        })
      );
    */
  }

  /**
   * GET /api/v1/popups/{popupId}
   * Obtiene el HTML de un popup específico
   */
  getPopupHtml(popupId: string): Observable<string> {
    // Por ahora, usar mocks por defecto para evitar errores 401
    // TODO: Implementar verificación de autenticación cuando el backend esté listo
    console.log('🔍 [PopupRepository] Getting HTML for popup:', popupId);
    const mockHtml = this.getMockPopupHtml(popupId);
    console.log('🔍 [PopupRepository] Returning mock HTML for popup:', popupId);
    return mockHtml;

    // Código original comentado para cuando el backend esté listo:
    /*
    return this.http
      .get<string>(`${this.apiUrl}/api/v1/popups/${popupId}/html`)
      .pipe(
        catchError((error: any) => {
          console.warn(
            'Error fetching popup HTML from API, using mock data:',
            error
          );
          return this.getMockPopupHtml(popupId);
        })
      );
    */
  }

  /**
   * Mock data como fallback cuando el API no está disponible
   */
  private getMockPopups(): PopupMeta[] {
    return [
      {
        popupId: 'POPUP-001',
        startAt: '2025-06-23:00:00:00UTC',
        endAt: '2025-07-23:00:00:00UTC',
        priority: 1,
      },
      {
        popupId: 'POPUP-002',
        startAt: '2025-06-23:00:00:00UTC',
        endAt: '2025-07-23:00:00:00UTC',
        priority: 2,
      },
    ];
  }

  /**
   * Mock HTML como fallback cuando el API no está disponible
   */
  private getMockPopupHtml(popupId: string): Observable<string> {
    if (popupId === 'POPUP-001') {
      return of('<h2>¿Tienes problemas para generar el ticket?</h2>');
    }
    if (popupId === 'POPUP-002') {
      return of('<h2>¿Sin señal?</h2>');
    }
    return of('<h2>Popup no encontrado</h2>');
  }
}
