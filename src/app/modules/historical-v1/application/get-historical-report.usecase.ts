import { Injectable } from '@angular/core';
import {
  BrowserDownloaderService,
  LoaderService,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  EMPTY,
  Observable,
  catchError,
  defer,
  finalize,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { PosOfflineRepository } from '../../../core/domain/repositories/pos-offline.repository';
import { DateMapper } from '../date.mapper';

@Injectable()
export class GetHistoricalReportUseCase {
  constructor(
    private offlineService: PosOfflineRepository,
    private loader: LoaderService,
    private notifier: NotifierService,
    private downloader: BrowserDownloaderService,
    private dateMapper: DateMapper,
    private errorHandler: UseCaseErrorHandler
  ) {}

  execute(dateRange: { startDate: Date; endDate: Date }): Observable<void> {
    const idLoader = this.loader.show();

    try {
      if (!dateRange.startDate || !dateRange.endDate) {
        throw new RuntimeMerchantError(
          'Un rango de fechas es requerido para generar el reporte',
          'GetHistoricalReportUseCase::execute::invalid-date-range'
        );
      }

      const { startDate, endDate } =
        this.dateMapper.fromDateRangeToRawDateYearFistWithHyphen({
          startDate: dateRange.startDate,
          endDate: dateRange.endDate,
        });

      return this.offlineService
        .getHistoricalReportByDate(startDate, endDate)
        .pipe(
          take(1),

          switchMap(response =>
            defer(() =>
              this.downloader.executeByBlob(
                response,
                'reporte-historico',
                'xlsx'
              )
            )
          ),

          tap(() => {
            this.notifier.success({
              title: 'Reporte generado',
              message:
                'El reporte se ha generado correctamente y la descarga comenzara pronto.',
            });
          }),

          finalize(() => {
            this.loader.hide(idLoader);
          }),

          catchError(error => this.errorHandler.handle<never>(error))
        );
    } catch (error) {
      this.loader.hide(idLoader);

      return this.errorHandler.handle(error, EMPTY);
    }
  }
}
