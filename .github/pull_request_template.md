## Pull Request Template

### Description

<!-- Provide a clear and concise description of your changes -->

[Briefly explain the purpose and context of the PR]

### Change Type

- [ ] Feature
- [ ] Bug Fix
- [ ] Refactor
- [ ] Documentation
- [ ] Other: **\_**

### Implementation Details

<!-- Explain the key technical changes and why you chose this approach -->

- Specific technical modifications
- Rationale behind the approach
- Key design decisions

### Impact

<!-- List the key areas affected by your changes -->

- [ ] User Interface
- [ ] Data Flow/State
- [ ] Performance
- [ ] Dependencies
- [ ] Documentation
- Detailed explanation of how these areas are impacted

### Testing Evidence

<!-- How have you verified these changes work? -->

- [ ] Unit/Integration Tests Added
- [ ] Manual Testing Completed
- [ ] Edge Cases Considered
- [ ] Cross-browser Tested (if UI changes)
- Specific test scenarios and results

### Screenshots/Videos

<!-- For UI changes, include before/after screenshots or videos -->

[Attach relevant visual evidence]

### Checklist

<!-- Ensure your PR meets these requirements -->

- [ ] Code follows project style guidelines
- [ ] Documentation is updated
- [ ] Self-review completed
- [ ] No console errors/warnings
- [ ] Changes are responsive (if UI)
- [ ] Existing tests pass

### Additional Notes

<!-- Add any other context about the changes here -->

[Optional additional context or explanations]

### Breaking Changes

<!-- List any breaking changes and migration steps if applicable -->

- Specific breaking changes
- Step-by-step migration guide
