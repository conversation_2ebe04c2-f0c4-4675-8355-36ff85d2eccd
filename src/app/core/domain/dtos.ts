import { Branch } from './entities';

export interface NotificationTarget {
  phoneNumber?: string;
  emailAddress?: string;
}

export const NOTIFICATION_CHANNELS = {
  WHATSAPP: 'WHATSAPP',
  EMAIL: 'EMAIL',
  SMS: 'SMS',
} as const;

export type NotificationChannel =
  (typeof NOTIFICATION_CHANNELS)[keyof typeof NOTIFICATION_CHANNELS];

export interface UpdateMerchantConfigResponse {
  id: number;
  logoUrl: string;
  merchantId: number;
  branches: Branch[];
  DeletedAt: string | null;
  createdAt: string;
  updatedAt: string;
  isBranchCreationEnable: boolean;
  minimumOrderAmount?: number;
}

export interface UpdateMerchantConfigDTO {
  LogoUrl: string;
  branches: string[];
}
