import { Observable } from 'rxjs';
import {
  ContestRegion,
  ContestRegistrationRequest,
  ParticipantDetailResponse,
  SendQRResponse,
} from '../contest';

export abstract class ContestRepository {
  abstract retriveLastTyCLink(): Observable<{ termsConditions: string }>;
  abstract retrieveRegions(): Observable<ContestRegion[]>;
  abstract createOne(request: ContestRegistrationRequest): Observable<void>;
  abstract getOneWithDetails(
    participantCode: string
  ): Observable<ParticipantDetailResponse>;
  abstract resendParticipantQR(
    participantId: string
  ): Observable<SendQRResponse>;
}
