import {
  HTTP_INTERCEPTORS,
  HttpClient,
  HttpErrorResponse,
} from '@angular/common/http';
import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { ObservabilityService } from '@aplazo/front-observability';
import { RedirectionService } from '@aplazo/merchant/shared';
import { LocalRedirecter } from '@aplazo/merchant/shared-testing';
import { StoreService } from 'src/app/core/application/services/store.service';
import { GlobalErrorHandlerInterceptor } from '../../../app/core/interceptors/global-error-handler.interceptor';

describe('GlobalErrorHandlerInterceptor', () => {
  let httpController: HttpTestingController;
  let interceptor: GlobalErrorHandlerInterceptor;
  let client: HttpClient;

  let observabilitySpy: jasmine.SpyObj<ObservabilityService>;
  let clearStoreSpy: jasmine.Spy;
  let internalNavigationSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        {
          provide: StoreService,
          useValue: {
            clearStore: () => {
              void 0;
            },
          },
        },
        {
          provide: RedirectionService,
          useClass: LocalRedirecter,
        },
        {
          provide: HTTP_INTERCEPTORS,
          useClass: GlobalErrorHandlerInterceptor,
          multi: true,
        },
        {
          provide: ObservabilityService,
          useValue: jasmine.createSpyObj('ObservabilityService', [
            'sendCustomLog',
          ]),
        },
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    interceptor = TestBed.inject(GlobalErrorHandlerInterceptor);
    observabilitySpy = TestBed.inject(
      ObservabilityService
    ) as jasmine.SpyObj<ObservabilityService>;
    client = TestBed.inject(HttpClient);
    clearStoreSpy = spyOn(
      TestBed.inject(StoreService),
      'clearStore'
    ).and.callThrough();
    observabilitySpy.sendCustomLog.and.callThrough();
    internalNavigationSpy = spyOn(
      TestBed.inject(RedirectionService),
      'internalNavigation'
    ).and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(interceptor).toBeTruthy();
  });

  it('should not handle errors when request is for a whitelisted resource [svg]', () => {
    client.get('http://localhost:4200/resource.svg').subscribe({
      next: resp => {
        expect(resp).toBeTruthy();
        expect(observabilitySpy.sendCustomLog)
          .withContext(
            'Expected not to send custom log for whitelisted resource [svg]'
          )
          .toHaveBeenCalledTimes(0);
      },
      error: fail,
    });

    const req = httpController.expectOne('http://localhost:4200/resource.svg');

    expect(req.request.method).toBe('GET');

    req.flush('test');
  });

  it('should not handle errors when request is for a whitelisted resource [json]', () => {
    client.get('http://localhost:4200/resource.json').subscribe({
      next: resp => {
        expect(resp).toBeTruthy();
        expect(observabilitySpy.sendCustomLog)
          .withContext(
            'Expected not to send custom log for whitelisted resource [json]'
          )
          .toHaveBeenCalledTimes(0);
      },
      error: fail,
    });

    const req = httpController.expectOne('http://localhost:4200/resource.json');

    expect(req.request.method).toBe('GET');

    req.flush('test');
  });

  it('should not handle error when request is successful', () => {
    client.get('http://localhost:4200/resource').subscribe({
      next: resp => {
        expect(resp).toBeTruthy();
        expect(observabilitySpy.sendCustomLog)
          .withContext('Expected not to send custom log for successful request')
          .toHaveBeenCalledTimes(0);
      },
      error: fail,
    });

    const req = httpController.expectOne('http://localhost:4200/resource');

    expect(req.request.method).toBe('GET');

    req.flush('test');
  });

  it('should handle error and build custom log for client side error', () => {
    const errTest = new ErrorEvent('load');

    client.get('http://localhost:4200/resource').subscribe({
      next: fail,
      error: err => {
        expect(err)
          .withContext('Error should be an instance of HttpErrorResponse')
          .toBeInstanceOf(HttpErrorResponse);

        expect(observabilitySpy.sendCustomLog)
          .withContext('Expected to send custom log for client side error')
          .toHaveBeenCalledTimes(1);

        expect(clearStoreSpy)
          .withContext('Expected not clear store for client side error')
          .toHaveBeenCalledTimes(0);
      },
    });

    const req = httpController.expectOne('http://localhost:4200/resource');

    expect(req.request.method).toBe('GET');

    req.flush(errTest, { status: 400, statusText: 'Bad Request' });
  });

  it('should handle error and build custom log for server side error', () => {
    const errTest = { code: 'OR-001' };

    client.get('http://localhost:4200/resource').subscribe({
      next: fail,
      error: err => {
        expect(err)
          .withContext('Error should be an instance of HttpErrorResponse')
          .toBeInstanceOf(HttpErrorResponse);

        expect(Object.prototype.hasOwnProperty.call(err?.error, 'code'))
          .withContext(
            'Mapped error should be an object and have a code property'
          )
          .toBeTrue();

        expect(observabilitySpy.sendCustomLog)
          .withContext('Expected to send custom log for server side error')
          .toHaveBeenCalledTimes(1);

        expect(clearStoreSpy)
          .withContext(
            'Expected not clear store for server side error when code is not AU-001'
          )
          .toHaveBeenCalledTimes(0);
      },
    });

    const req = httpController.expectOne('http://localhost:4200/resource');

    expect(req.request.method).toBe('GET');

    req.flush(errTest, { status: 400, statusText: 'Bad request' });
  });

  it('should handle error and build custom log for server side error [AU-001]', () => {
    const errTest = { code: 'AU-001' };

    client.get('http://localhost:4200/resource').subscribe({
      next: fail,
      error: err => {
        expect(err)
          .withContext('Error should be an instance of HttpErrorResponse')
          .toBeInstanceOf(HttpErrorResponse);

        expect(Object.prototype.hasOwnProperty.call(err?.error, 'code'))
          .withContext(
            'Mapped error should be an object and have a code property'
          )
          .toBeTrue();

        expect(observabilitySpy.sendCustomLog)
          .withContext('Expected to send custom log for server side error')
          .toHaveBeenCalledTimes(1);

        expect(clearStoreSpy)
          .withContext('Expected to clear store for server side error')
          .toHaveBeenCalledTimes(1);

        expect(internalNavigationSpy)
          .withContext(
            'Expected to navigate to authentication route for server side error'
          )
          .toHaveBeenCalledTimes(1);
      },
    });

    const req = httpController.expectOne('http://localhost:4200/resource');

    expect(req.request.method).toBe('GET');

    req.flush(errTest, { status: 400, statusText: 'Bad Request' });
  });
});
