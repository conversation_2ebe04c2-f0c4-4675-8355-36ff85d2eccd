sonar.projectKey=aplazo_angular.merchant-offline-pos-ui
sonar.projectName=POS UI - Merchant Offline
sonar.sourceEncoding=UTF-8
sonar.sources=src/
sonar.exclusions=**/node_modules/**,**/*.config.js,**/*.config.ts,**/*.spec.ts,**/*.mjs,src/tests/**/*,src/mocks/**/*,src/environments/**/*,src/main.ts,src/**/*.routes.ts,src/**/routes.ts,src/test.ts,src/zone-flags.ts
sonar.tests=src/tests/
sonar.test.inclusions=src/tests/**/*
sonar.javascript.lcov.reportPaths=coverage/merchant-offline-pos-ui/lcov.info

# Ignore specific rules
sonar.issue.ignore.multicriteria=1
sonar.issue.ignore.multicriteria.1.ruleKey=javascript:S6653
sonar.issue.ignore.multicriteria.1.resourceKey=**/*
