import { NgIf } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { SendAuthCodeFormComponent } from '../../../app/modules/share-payment-link/send-auth-code-form/send-auth-code-form.component';

describe('SendAuthCodeFormComponent', () => {
  let component: SendAuthCodeFormComponent;
  let fixture: ComponentFixture<SendAuthCodeFormComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        AplazoFormFieldDirectives,
        ReactiveFormsModule,
        AplazoButtonComponent,
        NgxMaskDirective,
        NgIf,
      ],
      providers: [provideNgxMask()],
    });

    fixture = TestBed.createComponent(SendAuthCodeFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
