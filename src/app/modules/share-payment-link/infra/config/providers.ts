import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { GetQrImageService } from '../../application/services/qr-image.service';
import { GetQrImageWithHttpService } from '../../get-qr-image.service';

export function provideQrImageService(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: GetQrImageService,
      useClass: GetQrImageWithHttpService,
    },
  ]);
}
