import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { POS_ENVIRONMENT_CORE } from '../../../../../app-core/domain/environments';
import {
  ContestRegion,
  ContestRegistrationRequest,
  ParticipantDetailResponse,
  SendQRResponse,
} from '../../domain/contest';
import { ContestRepository } from '../../domain/repositories/contest.repository';

@Injectable({ providedIn: 'root' })
export class ContestWithHttpRepository implements ContestRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(POS_ENVIRONMENT_CORE);

  retriveLastTyCLink(): Observable<{ termsConditions: string }> {
    return this.#http.get<{ termsConditions: string }>(
      `${this.#environment.exposedPublicApiUrl}/api/v1/merchant-rewards/campaign/latest-terms-and-conditions`
    );
  }

  retrieveRegions(): Observable<ContestRegion[]> {
    return this.#http.get<ContestRegion[]>(
      `${this.#environment.customerRegistrationMicro}/catalog/states`
    );
  }

  createOne(request: ContestRegistrationRequest): Observable<void> {
    return this.#http.post<void>(
      `${this.#environment.exposedPublicApiUrl}/api/v1/merchant-rewards/sign-up`,
      request
    );
  }

  getOneWithDetails(
    participantCode: string
  ): Observable<ParticipantDetailResponse> {
    return this.#http.get<ParticipantDetailResponse>(
      `${this.#environment.exposedPublicApiUrl}/api/v1/merchant-rewards/participant-rank/${participantCode}`
    );
  }

  resendParticipantQR(participantId: string): Observable<SendQRResponse> {
    return this.#http.post<SendQRResponse>(
      `${this.#environment.exposedPublicApiUrl}/api/v1/merchant-rewards/resend-participants-qr`,
      { participantId }
    );
  }
}
