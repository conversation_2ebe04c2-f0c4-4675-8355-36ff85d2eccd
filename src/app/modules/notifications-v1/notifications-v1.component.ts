import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>I<PERSON> } from '@angular/common';
import { Component, OnDestroy, inject } from '@angular/core';
import { I18NService } from '@aplazo/i18n';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoCommonMessageComponents } from '@aplazo/shared-ui/merchant';
import {
  AplazoTabComponent,
  AplazoTabsComponents,
} from '@aplazo/shared-ui/tabs';
import {
  BehaviorSubject,
  Observable,
  Subject,
  combineLatest,
  map,
  switchMap,
  take,
  takeUntil,
  tap,
} from 'rxjs';
import { AnalyticsService } from '../../core/application/services/analytics.service';
import {
  NotificationType,
  NotificationUI,
} from '../../core/domain/notification';
import { NotificationsHandlerService } from '../../core/services/notifications-handler.service';
import { NotificationsStore } from '../../core/services/notifications-store.service';
import { AplazoNotificationCardComponent } from './notification-card.component';
const PROMO_TYPES = {
  Promoción: 'PROMO',
  Funcionalidad: 'FEATURE',
  Información: 'INFO',
} as const;

type PromoTypeLabel = keyof typeof PROMO_TYPES;

@Component({
  standalone: true,
  selector: 'aplazo-notifications-v1',
  imports: [
    AplazoCardComponent,
    AplazoTabsComponents,
    AplazoCommonMessageComponents,
    AplazoNotificationCardComponent,
    NgIf,
    NgFor,
    AsyncPipe,
  ],
  templateUrl: './notifications-v1.component.html',
})
export class NotificationsV1Component implements OnDestroy {
  readonly #notificationHandler = inject(NotificationsHandlerService);
  readonly #notificationStore = inject(NotificationsStore);
  readonly #i18n = inject(I18NService);
  readonly #analyticsService = inject(AnalyticsService);
  readonly #scope = 'promos';

  readonly #destroy$ = new Subject<void>();
  readonly #selectedItemIndex$ = new BehaviorSubject<number>(0);
  readonly selectedIndex$ = this.#selectedItemIndex$.asObservable();

  readonly notificationLabels = Object.entries(PROMO_TYPES).map(
    ([key, value]) => ({ label: key, value })
  ) as { label: PromoTypeLabel; value: NotificationType }[];

  newItemsCounter$: Observable<
    Array<{ label: string; newItemsCounter: number; newIds: number[] }>
  > = this.#notificationStore.newNotificationsByTypeCounter$().pipe(
    map(counters =>
      this.notificationLabels.map(({ label }) => ({
        label,
        newItemsCounter:
          counters.find(i => i?.type === PROMO_TYPES[label])?.counter ?? 0,
        newIds: counters.find(i => i?.type === PROMO_TYPES[label])?.ids ?? [],
      }))
    ),
    takeUntil(this.#destroy$)
  );

  notifications$: Observable<{
    hasContent: boolean;
    data: { label: PromoTypeLabel; content: NotificationUI[] }[];
  }> = this.#selectedItemIndex$.pipe(
    switchMap(selectedIndex =>
      this.#notificationStore.getNotificationsByType$(
        PROMO_TYPES[this.notificationLabels[selectedIndex].label]
      )
    ),

    map(promos => {
      const emptyContent = [] as NotificationUI[];

      if (!promos || promos.size === 0) {
        return {
          hasContent: false,
          data: [
            {
              label: 'Promoción' as PromoTypeLabel,
              content: emptyContent,
            },
          ],
        };
      }

      const mappedPromos = Array.from(promos.entries())
        .map(([label, content]) => ({
          label: label,
          content: [...content],
          hasContent: content.length > 0,
        }))
        .filter(not => not.hasContent)
        .slice();

      if (mappedPromos.length === 0) {
        return {
          hasContent: false,
          data: [
            {
              label: 'Promoción' as PromoTypeLabel,
              content: emptyContent,
            },
          ],
        };
      }

      return {
        hasContent: true,
        data: mappedPromos.map(promos => {
          return {
            label: promos.label as PromoTypeLabel,
            content: promos.content,
          };
        }),
      };
    }),
    takeUntil(this.#destroy$)
  );

  emptyNotificationsInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
  }>({
    key: 'emptyList',
    scope: this.#scope,
  });
  selectInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    placeholder: string;
    tooltip?: string;
  }>({
    key: 'selection',
    scope: this.#scope,
  });
  selectLabelInjectedText$ = this.#i18n.getTranslateObjectByKey<{
    label: string;
  }>({
    key: 'selectLabel',
    scope: this.#scope,
  });

  readonly vm$ = combineLatest({
    newItemsCounter: this.newItemsCounter$,
    notifications: this.notifications$,
    textUI: this.emptyNotificationsInjectedText$,
    selectedIndex: this.selectedIndex$,
  }).pipe(takeUntil(this.#destroy$));

  constructor() {
    this.#notificationHandler
      .getNotifications$()
      .pipe(
        tap(notis => {
          this.#notificationStore.setNotifications(notis);
        }),
        take(1)
      )
      .subscribe();
  }

  changeTab(event: { index: number; tab: AplazoTabComponent | null }): void {
    this.#notificationStore.markAllAsRead(
      PROMO_TYPES[this.notificationLabels[event.index].label]
    );
    this.#selectedItemIndex$.next(event.index);

    this.#analyticsService.track('buttonClick', {
      buttonName:
        'notifications' +
        PROMO_TYPES[this.notificationLabels[event.index].label],
      timestamp: Date.now() as any,
    });
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
