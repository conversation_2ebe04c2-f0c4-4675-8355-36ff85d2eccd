{"content": {"id": 289, "createdAt": "2023-12-20T18:15:08.188179Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "merchantId": 2239, "logoUrl": "https://res.cloudinary.com/dnzxq7dgk/image/upload/f_auto,q_auto/sad_cactus_jzzu1s", "branches": [{"id": 145, "createdAt": "2024-01-12T22:34:46.52316Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "name": "Store QA_wolf No 1", "banned": false, "merchantConfigId": 289, "shareLinkFlags": {"id": 142, "createdAt": "2024-01-12T22:34:46.528663Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "isQrAllowed": true, "isWhatsappAllowed": true, "isSMSAllowed": true, "branchId": 145}, "branchFeatureFlags": {"id": 84, "createdAt": "2024-01-12T22:34:46.533087Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "branchId": 145, "isSellAgentTrackEnable": false, "isSupportChatEnable": false, "isOrderRefundEnable": false, "isTodayReportEnable": false}}, {"id": 241, "createdAt": "2024-05-03T19:46:05.143139Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "name": "Store QA_wolf No 2", "banned": false, "merchantConfigId": 289, "shareLinkFlags": {"id": 238, "createdAt": "2024-05-03T19:46:05.167899Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "isQrAllowed": true, "isWhatsappAllowed": true, "isSMSAllowed": true, "branchId": 241}, "branchFeatureFlags": {"id": 180, "createdAt": "2024-05-03T19:46:05.174481Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "branchId": 241, "isSellAgentTrackEnable": false, "isSupportChatEnable": false, "isOrderRefundEnable": false, "isTodayReportEnable": false}}, {"id": 242, "createdAt": "2024-05-03T19:46:05.143139Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "name": "Store QA_wolf No 3", "banned": false, "merchantConfigId": 289, "shareLinkFlags": {"id": 239, "createdAt": "2024-05-03T19:46:05.167899Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "isQrAllowed": true, "isWhatsappAllowed": true, "isSMSAllowed": true, "branchId": 242}, "branchFeatureFlags": {"id": 181, "createdAt": "2024-05-03T19:46:05.174481Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "branchId": 242, "isSellAgentTrackEnable": false, "isSupportChatEnable": false, "isOrderRefundEnable": false, "isTodayReportEnable": false}}, {"id": 243, "createdAt": "2024-05-03T19:55:53.681881Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "name": "Store QA_wolf No 4", "banned": false, "merchantConfigId": 289, "shareLinkFlags": {"id": 240, "createdAt": "2024-05-03T19:55:53.688129Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "isQrAllowed": true, "isWhatsappAllowed": true, "isSMSAllowed": true, "branchId": 243}, "branchFeatureFlags": {"id": 182, "createdAt": "2024-05-03T19:55:53.694263Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "branchId": 243, "isSellAgentTrackEnable": false, "isSupportChatEnable": false, "isOrderRefundEnable": false, "isTodayReportEnable": false}}, {"id": 244, "createdAt": "2024-05-03T19:55:53.681881Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "name": "Store QA_wolf No 5", "banned": false, "merchantConfigId": 289, "shareLinkFlags": {"id": 241, "createdAt": "2024-05-03T19:55:53.688129Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "isQrAllowed": true, "isWhatsappAllowed": true, "isSMSAllowed": true, "branchId": 244}, "branchFeatureFlags": {"id": 183, "createdAt": "2024-05-03T19:55:53.694263Z", "updatedAt": "2024-05-27T17:19:50.777765Z", "DeletedAt": null, "branchId": 244, "isSellAgentTrackEnable": false, "isSupportChatEnable": false, "isOrderRefundEnable": false, "isTodayReportEnable": false}}], "minimumOrderAmount": 100, "isBranchCreationEnable": false}, "error": null, "code": 200}