import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { AnalyticsService } from '../application/services/analytics.service';
import { AnalyticsWithGtmService } from '../services/analytics-with-gtm.service';

export function provideAnalyticsService(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: AnalyticsService,
      useClass: AnalyticsWithGtmService,
    },
  ]);
}
