import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/common';
import { Component } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { I18NService } from '@aplazo/i18n';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoCommonMessageComponents } from '@aplazo/shared-ui/merchant';
import { AplazoTabsComponents } from '@aplazo/shared-ui/tabs';
import { of } from 'rxjs';
import { AnalyticsService } from '../../../app/core/application/services/analytics.service';
import { NotificationsStore } from '../../../app/core/services/notifications-store.service';
import { AplazoNotificationCardComponent } from '../../../app/modules/notifications-v1/notification-card.component';
import { NotificationsV1Component } from '../../../app/modules/notifications-v1/notifications-v1.component';
import { LocalAnalyticsService } from '../../core/services/impl/local-analytics.service';

@Component({
  standalone: true,
  selector: 'aplazo-badge',
  template: `<span>test</span>`,
})
class NotificationBadgeComponent {
  count = 0;
  notificationTypes = ['PROMO'];
}

xdescribe('NotificationsV1Component', () => {
  let component: NotificationsV1Component;
  let fixture: ComponentFixture<NotificationsV1Component>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        AplazoButtonComponent,
        AplazoCardComponent,
        AplazoTabsComponents,
        AplazoCommonMessageComponents,
        AplazoNotificationCardComponent,
        NotificationBadgeComponent,
        NgIf,
        NgFor,
        AsyncPipe,
      ],
      providers: [
        {
          provide: I18NService,
          useValue: jasmine.createSpyObj('I18NService', [
            'getTranslateObjectByKey',
          ]),
        },
        {
          provide: AnalyticsService,
          useClass: LocalAnalyticsService,
        },
        {
          provide: NotificationsStore,
          useValue: {
            newNotificationsCounter$: () =>
              of([
                {
                  type: 'PROMO',
                  counter: 2,
                },
              ]),
            getNotificationsByType$: () => of([]),
            updateNotifications: () => void 0,
          },
        },
      ],
    });
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(NotificationsV1Component);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
