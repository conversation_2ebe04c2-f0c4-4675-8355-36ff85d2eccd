@if (vm$ | async; as context) {
  @if (context.hasDetails === false) {
    <article
      class="py-8 px-6 grid justify-center w-full font-light text-dark-secondary text-lg">
      <div class="max-w-lg">
        <h2 class="text-center text-dark-secondary text-2xl font-medium">
          ¡Consulta tu progreso!
        </h2>
        <p
          class="mt-6 leading-8 tracking-normal font-light text-dark-secondary text-pretty">
          Mantente al tanto de tu progreso. Ingresa tu y accede a tu posición y
          herramientas exclusivas.
        </p>

        <p
          class="mt-6 leading-8 tracking-normal font-light text-dark-secondary text-pretty">
          Ingresa tu
          <span class="mx-1 font-medium"> código de participante </span>
          para saber tu posición en la tabla de posiciones.
        </p>

        <p
          class="mt-6 leading-8 tracking-normal font-medium text-dark-secondary text-pretty">
          ¿Cómo funciona?
        </p>

        <ol
          class="leading-8 tracking-normal font-light text-dark-secondary text-pretty list-decimal list-inside">
          <li>Encuentra tu código único (Ej. AFIK1234).</li>
          <li>Escríbelo en el campo de búsqueda.</li>
          <li>
            Haz clic en consultar mi posición para ver tu posición actual.
          </li>
        </ol>

        <p
          class="mt-6 leading-8 tracking-normal font-light text-dark-secondary text-pretty">
          <span class="font-medium">¡Mantente en el top de la tabla</span>
          y consigue increíbles premios!
        </p>
      </div>

      <form
        [formGroup]="form"
        class="p-8 border-aplazo-aplazo rounded-lg"
        (ngSubmit)="retrieveParticipantInfo()">
        <div class="mx-auto max-w-full md:max-w-[280px] grid">
          <div
            class="relative w-full mb-8 flex border-2 rounded-lg"
            [class.border-special-danger]="search.touched && search.errors">
            <button
              aplzButton
              size="sm"
              type="button"
              class="flex-shrink-0 flex-grow-0 bg-light-primary border-e border-e-dark-tertiary pointer-events-none">
              <aplz-ui-icon name="search" size="sm"></aplz-ui-icon>
              <span class="aplazo-component--sr-only">Search</span>
            </button>

            <input
              type="text"
              class="block py-2.5 mx-1 w-full text-base text-dark-secondary ring-0 outline-none border-none rounded-lg"
              inputmode="search"
              placeholder="abcd0000"
              formControlName="search"
              aplazoLettersNumbers
              aplazoTrimSpaces />

            @if (search.value) {
              <button
                aplzButton
                type="button"
                size="sm"
                class="bg-light-primary border-s border-s-dark-tertiary"
                (click)="clear()">
                <aplz-ui-icon name="x-mark" size="sm"></aplz-ui-icon>
                <span class="aplazo-component--sr-only">Reset</span>
              </button>
            }

            <div class="absolute top-full text-sm w-full">
              @if (search.touched && search.hasError('required')) {
                <p class="whitespace-normal text-special-danger">
                  Ingrese un código de participante
                </p>
              } @else if (search.touched && search.hasError('minlength')) {
                <p class="whitespace-normal text-special-danger">
                  Ingrese un código de más de 2 caracteres
                </p>
              } @else if (
                search.touched && search.hasError(participantErrorKey)
              ) {
                <p class="whitespace-normal text-special-danger">
                  {{ search.getError(participantErrorKey) }}
                </p>
              }

              @if (!search.touched || !search.errors) {
                <p class="whitespace-normal">
                  Código de participante. Ej. AFIK1234
                </p>
              }
            </div>
          </div>
        </div>

        <button
          aplzButton
          type="submit"
          aplzAppearance="solid"
          aplzColor="dark"
          size="md"
          class="mx-auto"
          [disabled]="context.isLoading"
          [rounded]="true">
          Consultar mi posición
        </button>
      </form>
    </article>
  } @else {
    <div
      class="px-8 mt-8 flex justify-center md:justify-between items-center flex-wrap gap-4">
      <button
        aplzButton
        (click)="clear()"
        aplzAppearance="stroked"
        aplzColor="aplazo"
        size="md"
        class="flex-grow-0 flex-shrink-0">
        <aplz-ui-icon name="arrow-left" size="md"></aplz-ui-icon>
        <span class="ml-2">Cambiar participante</span>
      </button>

      <p
        class="text-xl text-dark-secondary leading-8 tracking-normal font-light text-right">
        Código Participante:
        <span class="ml-1 font-medium text-lg">
          {{ search.value }}
        </span>
      </p>
    </div>
  }
}
