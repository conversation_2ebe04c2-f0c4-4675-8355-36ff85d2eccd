import { TestBed } from '@angular/core/testing';
import { UrlTree } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { NotifierService } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom } from 'rxjs';
import { StoreService } from '../../../app/core/application/services/store.service';
import { MerchantConfig } from '../../../app/core/domain/merchant-config';
import { HasBranchCreationEnabledGuard } from '../../../app/core/guards/has-branch-creation-enabled.guard';
import {
  configWithBranchesAndCreationFlagDisabled,
  configWithoutBranchesAndCreationFlagDisabled,
  merchantConfigDummy,
} from '../../../mocks/merchant-config.dummy';
import { LocalStore } from '../services/impl/local-store.service';

describe('HasBranchCreationEnabledGuard', () => {
  let guard: HasBranchCreationEnabledGuard;
  let store: StoreService;
  let notifier: NotifierService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      providers: [
        {
          provide: StoreService,
          useClass: LocalStore,
        },
        provideLoaderTesting(),
        provideNotifierTesting(),
      ],
    });

    guard = TestBed.inject(HasBranchCreationEnabledGuard);
    store = TestBed.inject(StoreService);
    notifier = TestBed.inject(NotifierService);
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should return UrlTree to root when store-createBranches is disabled', async () => {
    const notifierSpy = spyOn(notifier, 'warning');
    store.setMerchantConfig(
      configWithBranchesAndCreationFlagDisabled as unknown as MerchantConfig
    );

    const result = await lastValueFrom(guard.canActivate());

    expect(result).toBeInstanceOf(UrlTree);
    expect(result.toString()).toBe('/');
    expect(notifierSpy).toHaveBeenCalledTimes(1);
  });

  it('should return UrlTree to root when has no branches', async () => {
    const notifierSpy = spyOn(notifier, 'warning');
    store.setMerchantConfig(
      configWithoutBranchesAndCreationFlagDisabled as unknown as MerchantConfig
    );

    const result = await lastValueFrom(guard.canActivate());

    expect(result).toBeInstanceOf(UrlTree);
    expect(result.toString()).toBe('/');
    expect(notifierSpy).toHaveBeenCalledTimes(1);
  });

  it('should allow navigation when isBranchCreationEnable', async () => {
    const notifierSpy = spyOn(notifier, 'warning');
    store.setMerchantConfig(merchantConfigDummy as unknown as MerchantConfig);

    const result = await lastValueFrom(guard.canActivate());

    expect(result).toBeTrue();
    expect(notifierSpy).toHaveBeenCalledTimes(0);
  });
});
