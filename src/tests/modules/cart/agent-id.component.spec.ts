import { Component, DebugElement, ViewChild } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { provideNgxMask } from 'ngx-mask';
import { AnalyticsService } from '../../../app/core/application/services/analytics.service';
import { AgentIdComponent } from '../../../app/modules/cart-v1/agent-id.component';

@Component({
  standalone: true,
  selector: 'app-test',
  template: `
    <aplazo-agent-id
      [formControl]="control"
      [injectedText]="templateUI"></aplazo-agent-id>
  `,
  imports: [ReactiveFormsModule, AgentIdComponent],
})
class TestComponent {
  control = new FormControl('');
  templateUI = {
    inputLabel: 'inputLabel',
    inputPlaceholder: 'inputPlaceholder',
    checkboxLabel: 'checkboxLabel',
    errorRequired: 'errorRequired',
    errorInvalidId: 'errorInvalidId',
  };

  @ViewChild(AgentIdComponent)
  agentIdComponent!: AgentIdComponent;
}

describe('AgentIdComponent', () => {
  let fixtureContainer: ComponentFixture<TestComponent>;
  let fixtureAgentId: DebugElement;
  let testComponent: TestComponent;
  let agentId: AgentIdComponent;
  let analyticsSpy: jasmine.SpyObj<AnalyticsService>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [ReactiveFormsModule],
      providers: [
        provideNgxMask(),
        {
          provide: AnalyticsService,
          useValue: jasmine.createSpyObj('AnalyticsService', ['track']),
        },
      ],
    });

    fixtureContainer = TestBed.createComponent(TestComponent);
    testComponent = fixtureContainer.componentInstance;
    fixtureAgentId = fixtureContainer.debugElement.query(
      By.directive(AgentIdComponent)
    );
    agentId = testComponent.agentIdComponent;

    fixtureContainer.detectChanges();

    analyticsSpy = TestBed.inject(
      AnalyticsService
    ) as jasmine.SpyObj<AnalyticsService>;
  });

  it('should be created', () => {
    expect(fixtureContainer).toBeTruthy();
  });

  it('should disable the input when the control is disabled', () => {
    const value = () => testComponent.control.value;
    analyticsSpy.track.and.returnValue(void 0);

    expect(value()).toBe('');

    const checkbox = fixtureAgentId.query(By.css('input[type="checkbox"]'));
    checkbox.triggerEventHandler('change', { target: { checked: true } });
    fixtureContainer.detectChanges();

    expect(value()).toBeNull();
    expect(analyticsSpy.track).toHaveBeenCalledTimes(1);
    expect(analyticsSpy.track).toHaveBeenCalledWith('buttonClick', {
      buttonName: 'cartDisableSellAgent',
    });
  });

  it('should reflect the input value in the control', () => {
    const value = () => testComponent.control.value;
    analyticsSpy.track.and.returnValue(void 0);

    expect(value()).toBe('');

    const input = fixtureAgentId.query(By.css('input[aplzforminput]'));
    input.nativeElement.value = 123456;
    input.triggerEventHandler('input', { target: input.nativeElement });
    fixtureContainer.detectChanges();

    expect(value()?.toString()).toEqual('123456');
  });
});
