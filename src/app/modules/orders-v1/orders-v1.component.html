@if (vm$ | async; as ctx) {
  <article class="px-4 lg:px-10 pt-5 pb-10 bg-dark-background">
    <h2 class="text-xl font-medium mb-3 uppercase">Resumen de hoy</h2>

    @if (ctx.ordersStatsContent; as stats) {
      <aplz-ui-card appearance="flat" [dark]="true">
        <div class="grid gap-2 grid-cols-1 lg:grid-cols-3 md:grid-cols-2">
          @for (stat of stats; track stat; let even = $even; let last = $last) {
            <aplz-ui-stat-card
              [I18nText]="{ statCardTitle: stat.statCardTitle }"
              [pipeName]="stat.pipeName ?? null"
              [principalAmount]="stat.value"
              [hasBorder]="
                (!last &&
                  ['lg', 'xl', '2xl', '3xl', '4xl', '5xl'].includes(
                    ctx.screenSize
                  )) ||
                (even && ['md'].includes(ctx.screenSize))
              ">
            </aplz-ui-stat-card>
          }
        </div>
      </aplz-ui-card>
    }
  </article>

  @if (ctx.bannerContentUI) {
    <section class="px-4 lg:px-10 pb-10 bg-dark-background">
      <aplazo-dynamic-banner
        [content]="ctx.bannerContentUI"></aplazo-dynamic-banner>
    </section>
  }

  <section class="px-4 lg:px-10 pb-[30vh]">
    @if (ctx.orders; as orders) {
      @if (
        orders.length === 0 &&
          !ctx.searchValue &&
          ctx.orderTypeControl === 'Todos';
        as ordersList
      ) {
        <aplz-ui-card class="mt-6" appearance="flat">
          <aplz-ui-common-message
            [i18Text]="{
              title: ctx.emptyMessage.title,
              description: ctx.emptyMessage.description
            }"
            imgName="emptyLoans">
            <aplz-ui-message-actions>
              <div class="grid gap-4">
                <button
                  aplzButton
                  aplzAppearance="solid"
                  aplzColor="dark"
                  size="md"
                  class="mx-auto"
                  (click)="newOrder()">
                  {{ ctx.emptyMessage.button.label }}
                </button>

                <aplazo-preloan-cart-button
                  class="mx-auto"
                  *stgCheckGate="
                    'b2b_front_posui_preloan_enabled'
                  "></aplazo-preloan-cart-button>
              </div>
            </aplz-ui-message-actions>
          </aplz-ui-common-message>
        </aplz-ui-card>
      } @else {
        <div
          class="grid justify-center gap-y-4 w-full py-6 lg:py-0 lg:flex lg:items-center lg:gap-x-4 lg:flex-wrap">
          <div
            class="mx-auto lg:mx-0 lg:text-left w-[125px] flex-shrink-0 flex-grow-0 leading-[0rem]">
            <span class="inline-block font-medium text-lg truncate w-full">
              {{ ordersCount | i18nPlural: ctx.counterLabelTextTemplate }}
            </span>
          </div>

          <div class="mx-auto">
            <aplz-ui-select [formControl]="orderTypeControl">
              @for (opt of orderTypes; track opt) {
                <aplz-ui-option [ngValue]="opt" [label]="opt"> </aplz-ui-option>
              }
            </aplz-ui-select>
          </div>

          <div class="lg:mt-8 max-w-full md:max-w-[260px]">
            <aplz-ui-search
              [textUI]="ctx.searchbarInjectedText"
              [formControl]="searchControl"
              [minLength]="minLength">
            </aplz-ui-search>
          </div>

          <div
            class="flex items-end justify-center lg:justify-end gap-x-4 flex-grow flex-shrink-0">
            @if ((refresh$ | async) !== undefined) {
              <button
                aplzButton
                size="sm"
                aplzAppearance="stroked"
                [disabled]="ctx.isLoadingReport"
                (click)="refreshOrdersList()">
                <aplz-ui-icon name="arrow-path" size="md"></aplz-ui-icon>
              </button>
            }

            <button
              aplzButton
              aplzAppearance="solid"
              aplzColor="dark"
              size="md"
              (click)="newOrder()">
              {{ (newOrderLabelText$ | async)?.buttonLabel || '' }}
            </button>
            <aplazo-preloan-cart-button
              *stgCheckGate="
                'b2b_front_posui_preloan_enabled'
              "></aplazo-preloan-cart-button>
            <button
              aplzButton
              aplzAppearance="solid"
              aplzColor="dark"
              size="md"
              *aplazoPosUiBranchFf="'isTodayReportEnable'"
              [disabled]="ctx.isLoadingReport"
              (click)="generateNewReport()">
              Reporte Del Día
            </button>
          </div>
        </div>

        @if (
          orders.length === 0 &&
            (ctx.searchValue || ctx.orderTypeControl !== 'Todos');
          as emptySearch
        ) {
          <aplz-ui-card
            class="mt-6"
            [appearance]="
              ['xs', 'sm', 'md'].includes(ctx.screenSize)
                ? 'borderless'
                : 'flat'
            ">
            <div class="pt-14">
              <aplz-ui-common-message [i18Text]="ctx.emptySearchInjectedText">
              </aplz-ui-common-message>
            </div>
          </aplz-ui-card>
        } @else {
          <aplazo-orders-table
            [orders]="orders"
            [i18text]="ctx.ordersTableInjectedText"
            (cancelOrder)="cancelOrder($event)"
            (shareOrder)="sharePaymentLink($event)"
            (checkOrderStatus)="checkOrderStatus($event)"
            (showRefundInfo)="showRefundInfo($event)"></aplazo-orders-table>
        }
      }
    }
  </section>
}
