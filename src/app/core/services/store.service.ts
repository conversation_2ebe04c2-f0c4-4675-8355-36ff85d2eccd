import { inject, Injectable } from '@angular/core';
import {
  BrowserSessionStorageService,
  JwtDecoderService,
} from '@aplazo/merchant/shared';
import { BehaviorSubject, map, Observable, of, switchMap } from 'rxjs';
import { POS_ENVIRONMENT_CORE } from '../../../app-core/domain/environments';
import {
  IBranchFeatureFlags,
  IShareLinkFlags,
  StoreService,
} from '../application/services/store.service';
import { IUserJwt } from '../domain/access-token';
import { Branch } from '../domain/entities';
import { MerchantConfig } from '../domain/merchant-config';

@Injectable({ providedIn: 'root' })
export class GeneralStoreService implements StoreService {
  private decoder: JwtDecoderService<IUserJwt> = inject(JwtDecoderService);
  private sessionStorageService = inject(BrowserSessionStorageService);
  private environment = inject(POS_ENVIRONMENT_CORE);

  private readonly tokenStorageKey = `${this.environment.appName}_token`;
  private readonly merchantConfigStorageKey = `${this.environment.appName}_config`;
  private readonly selectedBranchStorageKey = `${this.environment.appName}_currentBranch`;
  private readonly merchantNameStorageKey = `${this.environment.appName}_merchantName`;

  readonly #token$: BehaviorSubject<string | null> = new BehaviorSubject<
    string | null
  >(null);
  readonly #decodedToken$: BehaviorSubject<IUserJwt | null> =
    new BehaviorSubject<IUserJwt | null>(null);
  readonly #merchantConfig$: BehaviorSubject<MerchantConfig | null> =
    new BehaviorSubject<MerchantConfig | null>(null);
  readonly #selectedBranch$: BehaviorSubject<Branch | null> =
    new BehaviorSubject<Branch | null>(null);
  readonly #merchantName$ = new BehaviorSubject<string>('');

  readonly token$ = this.#token$.asObservable();
  readonly decodedToken$ = this.#decodedToken$.asObservable();
  readonly merchantConfig$ = this.#merchantConfig$.asObservable();
  readonly selectedBranch$ = this.#selectedBranch$.asObservable();
  readonly merchantName$ = this.#merchantName$.asObservable();

  readonly userRole$ = this.decodedToken$.pipe(
    map(user => (!user ? '' : user.role))
  );

  getToken(): string | null {
    return this.#token$.value;
  }

  getMerchantId$(): Observable<number | null> {
    return this.merchantConfig$.pipe(
      map(config => (config ? config.merchantId : null))
    );
  }

  setToken(token: string | null): void {
    this.#token$.next(token);

    if (!token) {
      this.sessionStorageService.removeItem(this.tokenStorageKey);
    } else {
      this.sessionStorageService.setItem(this.tokenStorageKey, token);
    }
  }

  setMerchantConfig(config: MerchantConfig | null) {
    this.#merchantConfig$.next(config);

    if (!config) {
      this.sessionStorageService.removeItem(this.merchantConfigStorageKey);
    } else {
      this.sessionStorageService.setItem(
        this.merchantConfigStorageKey,
        JSON.stringify(config)
      );
    }
  }

  setDecodedToken(user: IUserJwt | null) {
    this.#decodedToken$.next(user);
  }

  setSelectedBranch(branch: Branch | null) {
    this.#selectedBranch$.next(branch);

    if (!branch) {
      this.sessionStorageService.removeItem(this.selectedBranchStorageKey);
    } else {
      this.sessionStorageService.setItem(
        this.selectedBranchStorageKey,
        JSON.stringify(branch)
      );
    }
  }

  get isLoggedIn$(): Observable<boolean> {
    return this.token$.pipe(
      switchMap(token => {
        if (!token) {
          return of(false);
        }

        return of(true);
      })
    );
  }

  get integrationType$(): Observable<string> {
    return this.decodedToken$.pipe(
      map(user => {
        if (!user) {
          return '';
        }

        return user.integrationType;
      })
    );
  }

  get selectedBranchName$(): Observable<string | null> {
    return this.selectedBranch$.pipe(
      map(branch => {
        if (!branch) {
          return null;
        }

        return branch.name;
      })
    );
  }

  get selectedBranchShareLinkFlags$(): Observable<IShareLinkFlags | null> {
    return this.selectedBranch$.pipe(
      map(branch => {
        if (!branch || !branch.shareLinkFlags) {
          return null;
        }

        return {
          isWhatsappAllowed: branch.shareLinkFlags.isWhatsappAllowed,
          isQrAllowed: branch.shareLinkFlags.isQrAllowed,
          isSMSAllowed: branch.shareLinkFlags.isSMSAllowed,
        };
      })
    );
  }

  get hasBranchShareLinkEnabled$(): Observable<boolean> {
    return this.selectedBranch$.pipe(
      map(branch => {
        if (!branch || !branch.shareLinkFlags) {
          return false;
        }

        return (
          branch.shareLinkFlags.isQrAllowed ||
          branch.shareLinkFlags.isSMSAllowed ||
          branch.shareLinkFlags.isWhatsappAllowed
        );
      })
    );
  }

  get selectedBranchFeatureFlags$(): Observable<IBranchFeatureFlags | null> {
    return this.selectedBranch$.pipe(
      map(branch => {
        if (!branch || !branch.branchFeatureFlags) {
          return null;
        }

        return {
          isSellAgentTrackEnable:
            branch.branchFeatureFlags.isSellAgentTrackEnable,
          isSupportChatEnable: branch.branchFeatureFlags.isSupportChatEnable,
          isOrderRefundEnable: branch.branchFeatureFlags.isOrderRefundEnable,
          isTodayReportEnable: branch.branchFeatureFlags.isTodayReportEnable,
        };
      })
    );
  }

  get hasBranchCreationEnabled$(): Observable<boolean> {
    return this.merchantConfig$.pipe(
      map(config => config?.isBranchCreationEnable || false)
    );
  }

  get branches$(): Observable<Branch[]> {
    return this.merchantConfig$.pipe(
      map(config => {
        if (!config) {
          return [];
        }

        return config.branches;
      })
    );
  }

  get hasBranches$(): Observable<boolean> {
    return this.merchantConfig$.pipe(
      map(config => {
        if (
          !config ||
          !config.branches ||
          !Array.isArray(config.branches) ||
          config.branches.length === 0
        ) {
          return false;
        }

        return config.branches.length > 0;
      })
    );
  }

  get minimumOrderAmount$(): Observable<number | null> {
    return this.merchantConfig$.pipe(
      map(config => {
        if (!config || !config.minimumOrderAmount) {
          return null;
        }

        return config.minimumOrderAmount;
      })
    );
  }

  get merchantLogoUrl$(): Observable<string | null> {
    return this.merchantConfig$.pipe(
      map(config => {
        if (!config) {
          return null;
        }

        return this.isValidImagePath(config.logoUrl) ? config.logoUrl : null;
      })
    );
  }

  clearStore(): void {
    this.setToken(null);
    this.setMerchantConfig(null);
    this.setSelectedBranch(null);
    this.setMerchantName(null);

    this.sessionStorageService.clearStorage();
  }

  async setMerchantName(merchantName: string | null): Promise<void> {
    if (merchantName == null) {
      this.#merchantName$.next('');
      this.sessionStorageService.removeItem(this.merchantNameStorageKey);
      return;
    }

    if (typeof merchantName === 'string' && merchantName.length > 0) {
      this.#merchantName$.next(merchantName);
      this.sessionStorageService.setItem(
        this.merchantNameStorageKey,
        merchantName
      );
      return;
    }

    const token = this.#decodedToken$.getValue();

    if (!token) {
      this.#merchantName$.next('');
      this.sessionStorageService.removeItem(this.merchantNameStorageKey);
      return;
    }

    this.#merchantName$.next(token.companyName || token.name);
    this.sessionStorageService.setItem(
      this.merchantNameStorageKey,
      token.companyName || token.name
    );
  }

  async setStoreFromSessionStorage(): Promise<void> {
    const token = this.sessionStorageService.getItem(this.tokenStorageKey);
    if (token) {
      this.setToken(token);
      const user = await (this.decoder.decodeToken(token) as Promise<IUserJwt>);
      this.#decodedToken$.next(user);
    }

    const merchantConfig = this.sessionStorageService.getItem(
      this.merchantConfigStorageKey
    );
    if (merchantConfig) {
      this.setMerchantConfig(JSON.parse(merchantConfig));
    }

    const selectedBranch = this.sessionStorageService.getItem(
      this.selectedBranchStorageKey
    );
    if (selectedBranch) {
      this.setSelectedBranch(JSON.parse(selectedBranch));
    }

    const merchantName = this.sessionStorageService.getItem(
      this.merchantNameStorageKey
    );

    if (merchantName) {
      this.setMerchantName(merchantName);
    }
  }

  private isValidImagePath(path: string): boolean {
    const hasImageExtension =
      path.match(/\.(jpg|jpeg|png|webp|avif|gif|svg)$/) != null;
    const hasValidProtocol = path.match(/^(http|https):\/\//) != null;

    return hasImageExtension && hasValidProtocol;
  }
}
