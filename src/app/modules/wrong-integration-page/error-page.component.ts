import { Component, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { StoreService } from '../../core/application/services/store.service';

@Component({
  standalone: true,
  selector: 'app-error-page',
  template: `
    <main
      class="h-full min-h-[600px] lg:h-screen flex flex-col items-center justify-center">
      <div class="w-full max-w-lg text-center">
        <picture class="block w-full h-24">
          <img
            class="w-full h-full max-w-full max-h-full object-contain"
            src="https://cdn.aplazo.mx/merchant-dash-assets/Decline%402x.png"
            alt=" error image icon" />
        </picture>
        <h1 class="text-lg lg:text-xl text-dark-secondary mt-14 py-4">
          Lo sentimos, pero no tiene los requisitos necesarios para utilizar
          esta herramienta.
        </h1>
      </div>
    </main>
  `,
})
export class WrongIntegrationPageComponent implements OnDestroy {
  readonly #store = inject(StoreService);

  ngOnDestroy(): void {
    this.#store.clearStore();
  }
}
