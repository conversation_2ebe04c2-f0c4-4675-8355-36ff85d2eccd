import { NgIf } from '@angular/common';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { DialogRef } from '@ngneat/dialog';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import { ProductFormComponent } from '../../../app/modules/cart-v1/product-form/product-form.component';

describe('ProductFormComponent', () => {
  let component: ProductFormComponent;
  let fixture: ComponentFixture<ProductFormComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        NgIf,
        ReactiveFormsModule,
        NgxMaskDirective,
        AplazoCardComponent,
        AplazoFormFieldDirectives,
        AplazoButtonComponent,
      ],
      providers: [
        provideNgxMask(),
        {
          provide: DialogRef,
          useValue: {
            close: () => void 0,
            data: {
              injectedText: {
                title: 'Product Form',
                productId: 'productId',
                product: {
                  label: 'Product',
                  placeholder: 'Product',
                  errorRequired: 'Product is required',
                },
                sku: {
                  label: 'SKU',
                  placeholder: 'SKU',
                  errorRequired: 'SKU is required',
                },
                price: {
                  label: 'Price',
                  placeholder: 'Price',
                  errorRequired: 'Price is required',
                  errorMin: 'Price must be greater than 0',
                },
                quantity: {
                  label: 'Quantity',
                  placeholder: 'Quantity',
                  errorRequired: 'Quantity is required',
                  errorMin: 'Quantity must be greater than 0',
                },
                actions: {
                  cancel: 'Cancel',
                  confirm: 'Confirm',
                },
              },
            },
          },
        },
      ],
    });
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ProductFormComponent);
    component = fixture.componentInstance;

    // fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // it('should have a form with 4 inputs', async () => {});

  it('should have a formGroup with default values', () => {
    expect(component.formGroup.value).toEqual({
      sku: null,
      name: null,
      price: null,
      quantity: null,
    });
  });

  // it('should have a disabled button by default', () => {
  //   const buttonElement = fixture.debugElement.query(
  //     By.css('button[aplzappearance="solid"]')
  //   );
  //   console.log(buttonElement);
  //   expect(buttonElement.nativeElement.disabled).toBeTrue();
  // });

  // it('should call addPanelClass and updatePosition onInit', () => {});

  // it('should call dialog close method', () => {});

  // it('should not close the dialog and mark as touch all controls within form when form is invalid', () => {});

  // it('should have a valid form value when the input receives correct data', () => {
  //   component.formGroup.setValue({
  //     sku: 'sku',
  //     name: 'name',
  //     price: 1,
  //     quantity: 1,
  //   });

  //   fixture.detectChanges();

  //   expect(component.formGroup.valid).toBeTrue();
  // });

  // it('should close the dialog when form is valid', () => {});
});
