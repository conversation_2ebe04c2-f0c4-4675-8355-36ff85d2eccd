import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  ConnectionStatusService,
  RedirectionService,
} from '@aplazo/merchant/shared';
import { StatsigService } from '@statsig/angular-bindings';
import { take } from 'rxjs';
import { StoreService } from '../../core/application/services/store.service';

@Injectable({ providedIn: 'root' })
export class SecuredV1LogoutUsecase implements BaseUsecase<undefined, void> {
  readonly #redirectionService = inject(RedirectionService);
  readonly #storeService = inject(StoreService);
  readonly #statsigService = inject(StatsigService);
  readonly #connectionService = inject(ConnectionStatusService);

  execute(): void {
    this.#connectionService.status$.pipe(take(1)).subscribe(({ isOnline }) => {
      if (isOnline) {
        this.#statsigService.updateUserAsync({
          userID: undefined,
        });
        this.#storeService.clearStore();
      }
      this.#redirectionService.internalNavigation('/');
    });
  }
}
