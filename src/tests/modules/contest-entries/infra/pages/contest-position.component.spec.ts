import { AsyncPipe } from '@angular/common';
import {
  discardPeriodicTasks,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { LoaderService } from '@aplazo/merchant/shared';
import { provideLoaderTesting } from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { Observable, of } from 'rxjs';
import { GetOneParticipantWithDetailsUseCase } from 'src/app/modules/contest-entries/application/usecases/get-one-with-details.usecase';
import { AnalyticsService } from '../../../../../app/core/application/services/analytics.service';
import { SendQRCodeUseCase } from '../../../../../app/modules/contest-entries/application/usecases/send-qr-code.usecase';
import { ContestRankingUI } from '../../../../../app/modules/contest-entries/domain/contest';
import { AplazoContestPositionWithSearchComponent } from '../../../../../app/modules/contest-entries/infra/components/contest-position-with-search/contest-position-with-search.component';
import { ParticipantRankingComponent } from '../../../../../app/modules/contest-entries/infra/components/participant-ranking/participant-ranking.component';
import { AplazoContestPositionComponent } from '../../../../../app/modules/contest-entries/infra/pages/contest-position/contest-position.component';
import { ContestStoreService } from '../../../../../app/modules/contest-entries/infra/services/contest-store.service';

const mockRankingData: ContestRankingUI = {
  campaignFinishDate: '2023-12-31',
  data: [
    { rank: 1, totalRegistration: 10, matched: true },
    { rank: 2, totalRegistration: 8, matched: false },
  ],
  qrUrl: 'https://test.qr',
  hasDetails: true,
  isFutureDate: true,
  tier: null,
  participantId: 'test-participant-123',
};

const mockRankingNoData: ContestRankingUI = {
  ...mockRankingData,
  data: [],
};

const mockRankingTier4: ContestRankingUI = {
  ...mockRankingData,
  tier: '4',
  data: [{ rank: 1, totalRegistration: 15, matched: true }],
};

const setup = (args?: {
  ranking?: ContestRankingUI;
  senQrResponse?: Observable<void> | never;
}) => {
  const defaultConfig = {
    ranking: mockRankingData,
    senQrResponse: of(void 0),
  };

  const config = {
    ranking: args?.ranking ?? defaultConfig.ranking,
    senQrResponse: args?.senQrResponse ?? defaultConfig.senQrResponse,
  };

  TestBed.configureTestingModule({
    imports: [
      AsyncPipe,
      ParticipantRankingComponent,
      AplazoContestPositionWithSearchComponent,
      AplazoButtonComponent,
      AplazoIconComponent,
    ],
    providers: [
      AplazoIconRegistryService,
      {
        provide: ContestStoreService,
        useValue: {
          contestRanking$: of(config.ranking),
          hasDetails$: of(config.ranking.hasDetails),
          setNewRanking: () => {
            return;
          },
          clearRanking: () => {
            return;
          },
        },
      },
      {
        provide: AnalyticsService,
        useValue: jasmine.createSpyObj('AnalyticsService', ['track']),
      },
      {
        provide: SendQRCodeUseCase,
        useValue: {
          execute: config.senQrResponse,
        },
      },
      {
        provide: GetOneParticipantWithDetailsUseCase,
        useValue: {
          execute: of(config.ranking),
        },
      },
      provideLoaderTesting(),
    ],
  });

  const fixture = TestBed.createComponent(AplazoContestPositionComponent);
  const component = fixture.componentInstance;

  const analyticsSpy = TestBed.inject(
    AnalyticsService
  ) as jasmine.SpyObj<AnalyticsService>;
  const iconRegistry = TestBed.inject(AplazoIconRegistryService);
  const sendQRUseCase = TestBed.inject(SendQRCodeUseCase);
  const loaderService = TestBed.inject(LoaderService);
  const store = TestBed.inject(ContestStoreService);

  const showLoaderSpy = spyOn(loaderService, 'show').and.callThrough();
  const hideLoaderSpy = spyOn(loaderService, 'hide').and.callThrough();

  const sendQrUseCaseSpy = spyOn(sendQRUseCase, 'execute').and.callThrough();

  const registerIconSpy = spyOn(
    iconRegistry,
    'registerIcons'
  ).and.callThrough();

  fixture.detectChanges();

  return {
    fixture,
    component,
    analyticsSpy,
    showLoaderSpy,
    hideLoaderSpy,
    sendQrUseCaseSpy,
    registerIconSpy,
    store,
  };
};

describe('AplazoContestPositionComponent', () => {
  it('should create', () => {
    const { component } = setup();
    expect(component).toBeTruthy();
  });

  it('should register the spin-circle icon on initialization', () => {
    const { registerIconSpy } = setup();
    expect(registerIconSpy).toHaveBeenCalledTimes(1);
  });

  it('should track pageView on initialization', () => {
    const { analyticsSpy } = setup();
    expect(analyticsSpy.track).toHaveBeenCalledWith('pageView', {
      timestamp: jasmine.any(Number),
    });
  });

  describe('sendQRCode', () => {
    it('should call SendQRCodeUseCase when button is not disabled', fakeAsync(() => {
      const { component, sendQrUseCaseSpy, fixture } = setup();

      tick();
      fixture.detectChanges();

      sendQrUseCaseSpy.and.returnValue(of(undefined));

      component.sendQRCode();
      tick();

      expect(sendQrUseCaseSpy).toHaveBeenCalledTimes(1);

      discardPeriodicTasks();
    }));
  });

  describe('template rendering', () => {
    it('should render ParticipantRankingComponent when tier is not 4 and data exists', () => {
      const { fixture } = setup();

      const participantRanking =
        fixture.debugElement.nativeElement.querySelector(
          'app-participant-ranking'
        );

      expect(participantRanking).toBeTruthy();
    });

    it('should render empty state message when data is empty', () => {
      const { fixture } = setup({
        ranking: mockRankingNoData,
      });
      fixture.detectChanges();

      const emptyStateMessage =
        fixture.debugElement.nativeElement.querySelector(
          'h2.text-lg.text-dark-primary.text-center.text-pretty'
        );
      expect(emptyStateMessage).toBeTruthy();
      expect(emptyStateMessage.textContent.trim()).toBe('No tienes registros');

      const participantRanking =
        fixture.debugElement.nativeElement.querySelector(
          'app-participant-ranking'
        );
      expect(participantRanking).toBeFalsy(); // Should not render ranking component

      const totalRegistration =
        fixture.debugElement.nativeElement.querySelector(
          'p.font-semibold.text-dark-primary.mt-2.text-center.text-xl'
        );
      expect(totalRegistration).toBeFalsy(); // Should not show total registration message
    });

    it('should render total registrations when tier is 4', () => {
      const { fixture } = setup({
        ranking: mockRankingTier4,
      });
      fixture.detectChanges();

      const totalRegistration =
        fixture.debugElement.nativeElement.querySelector(
          'p.font-semibold.text-dark-primary.mt-2.text-center.text-xl'
        );
      expect(totalRegistration).toBeTruthy();
      expect(totalRegistration.textContent.trim()).toBe('15');

      const participantRanking =
        fixture.debugElement.nativeElement.querySelector(
          'app-participant-ranking'
        );
      expect(participantRanking).toBeFalsy();

      const emptyStateMessage =
        fixture.debugElement.nativeElement.querySelector(
          'h2.text-lg.text-dark-primary.text-center.text-pretty'
        );
      expect(emptyStateMessage).toBeTruthy();
      expect(emptyStateMessage.textContent.trim()).toBe(
        'Tienes los siguientes registros acumulados:'
      );
    });

    it('should show "termina" text when isFutureDate is true', () => {
      const { fixture } = setup();

      const challengeText = fixture.debugElement.nativeElement.querySelector(
        'h2.text-xl.text-center.my-8.text-pretty'
      );

      expect(challengeText.textContent).toContain('termina');
      expect(challengeText.textContent).not.toContain('terminó');
    });

    it('should show "terminó" text when isFutureDate is false', () => {
      const { fixture } = setup({
        ranking: {
          ...mockRankingData,
          isFutureDate: false,
        },
      });

      fixture.detectChanges();

      const challengeText = fixture.debugElement.nativeElement.querySelector(
        'h2.text-xl.text-center.my-8.text-pretty'
      );

      expect(challengeText.textContent).toContain('terminó');
      expect(challengeText.textContent).not.toContain('termina');
    });

    it('should enable the button when showSpinner is false', fakeAsync(() => {
      const { fixture } = setup({
        ranking: {
          ...mockRankingData,
          isFutureDate: false,
        },
      });
      tick();
      fixture.detectChanges();
      const button =
        fixture.debugElement.nativeElement.querySelector('button[aplzButton]');
      expect(button.disabled).toBeFalse();
    }));

    it('should display the correct QR code URL', () => {
      const { fixture } = setup(); // Uses default mockRankingData
      fixture.detectChanges();

      const imgElement = fixture.debugElement.nativeElement.querySelector(
        'figure img'
      ) as HTMLImageElement;

      expect(imgElement).toBeTruthy();
      expect(
        imgElement.src.trim().startsWith(mockRankingData.qrUrl as string)
      ).toBeTrue();
    });

    it('should show the formatted date in the template', fakeAsync(() => {
      const { fixture, component } = setup();

      // Force component to use the formatted date
      component.ngOnInit();
      fixture.detectChanges();
      tick();

      const dateTextElement = fixture.debugElement.nativeElement.querySelector(
        'h2.text-xl.text-center.my-8.text-pretty > span'
      );
      expect(dateTextElement).toBeTruthy();
      // Use contains as formatting might vary slightly
      expect(dateTextElement.textContent).toContain('2023-12-31');
    }));
  });
});
