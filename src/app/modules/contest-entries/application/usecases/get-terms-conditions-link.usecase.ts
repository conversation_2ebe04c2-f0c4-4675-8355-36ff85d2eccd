import { inject, Injectable, SecurityContext } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import {
  BaseUsecase,
  LoaderService,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, finalize, map, Observable, take } from 'rxjs';
import { ContestRepository } from '../../domain/repositories/contest.repository';

@Injectable()
export class GetTermsConditionsLinkUsecase
  implements BaseUsecase<undefined, Observable<string>>
{
  readonly #repository = inject(ContestRepository);
  readonly #errorHandler = inject(UseCaseErrorHandler);
  readonly #loader = inject(LoaderService);
  readonly #sanitizer = inject(DomSanitizer);

  execute(): Observable<string> {
    const idLoader = this.#loader.show();

    return this.#repository.retriveLastTyCLink().pipe(
      map(link => {
        return (
          this.#sanitizer.sanitize(SecurityContext.URL, link.termsConditions) ??
          ''
        );
      }),

      catchError(e => this.#errorHandler.handle<never>(e)),

      take(1),

      finalize(() => {
        this.#loader.hide(idLoader);
      })
    );
  }
}
