import { Order } from '../app/core/domain/order.interface';

export const ordersDummy: Order[] = [
  {
    id: 21394,
    createdAt: '2022-08-25T00:01:15.62232Z',
    updatedAt: '2022-08-25T00:01:15.62232Z',
    DeletedAt: null,
    date: '2022-08-25 00:01:15.620977892 +0000 UTC m=+862499.633762304',
    status: 'created',
    price: 136,
    url: 'https://checkout-offline.aplazo.dev/main/ba319945-8bac-4146-9b94-c13e017f87ee',
    loanId: 46080,
    merchantId: 12,
    products: [],
    branchId: 173,
    sellsAgentId: 0,
  },
  {
    id: 21387,
    createdAt: '2022-08-24T18:29:55.498656Z',
    updatedAt: '2022-08-24T18:30:46.801251Z',
    DeletedAt: null,
    date: '2022-08-24 18:29:55.497279482 +0000 UTC m=+842619.510063127',
    status: 'Activo',
    price: 250,
    url: 'https://checkout-offline.aplazo.dev/main/8d54a984-15ba-4969-9f08-2b2ed8f00474',
    loanId: 46041,
    merchantId: 12,
    products: [],
    branchId: 175,
    sellsAgentId: 0,
  },
  {
    id: 21384,
    createdAt: '2022-08-24T06:11:07.410095Z',
    updatedAt: '2022-08-24T06:11:07.410095Z',
    DeletedAt: null,
    date: '2022-08-24 06:11:07.408776901 +0000 UTC m=+798291.421560546',
    status: 'created',
    price: 462,
    url: 'https://checkout-offline.aplazo.dev/main/547b7f1c-1367-4a13-b92b-358b970eab80',
    loanId: 46006,
    merchantId: 12,
    products: [],
    branchId: 175,
    sellsAgentId: 0,
  },
  {
    id: 21385,
    createdAt: '2022-08-24T16:05:56.216795Z',
    updatedAt: '2022-08-24T16:26:05.658652Z',
    DeletedAt: null,
    date: '2022-08-24 16:05:56.214933702 +0000 UTC m=+833980.227718314',
    status: 'Activo',
    price: 250,
    url: 'https://checkout-offline.aplazo.dev/main/1cda0c5a-ea91-4a34-a4ba-df4abaec3bde',
    loanId: 46011,
    merchantId: 12,
    products: [],
    branchId: 175,
    sellsAgentId: 0,
  },
  {
    id: 21386,
    createdAt: '2022-08-24T17:38:20.01572Z',
    updatedAt: '2022-08-24T17:38:20.01572Z',
    DeletedAt: null,
    date: '2022-08-24 17:38:20.014399906 +0000 UTC m=+839524.027183551',
    status: 'created',
    price: 200,
    url: 'https://checkout-offline.aplazo.dev/main/ba767fc3-b76b-489d-ae89-8c3d656e192d',
    loanId: 46019,
    merchantId: 12,
    products: [],
    branchId: 174,
    sellsAgentId: 0,
  },
  {
    id: 21391,
    createdAt: '2022-08-24T22:50:47.020853Z',
    updatedAt: '2022-08-24T22:50:47.020853Z',
    DeletedAt: null,
    date: '2022-08-24 22:50:47.019674602 +0000 UTC m=+858271.032459247',
    status: 'created',
    price: 131,
    url: 'https://checkout-offline.aplazo.dev/main/2ff9c667-7be7-4ec0-bc08-828263407f64',
    loanId: 46076,
    merchantId: 12,
    products: [],
    branchId: 175,
    sellsAgentId: 0,
  },
  {
    id: 21392,
    createdAt: '2022-08-24T23:30:17.773498Z',
    updatedAt: '2022-08-24T23:30:17.773498Z',
    DeletedAt: null,
    date: '2022-08-24 23:30:17.772250207 +0000 UTC m=+860641.785052539',
    status: 'created',
    price: 174,
    url: 'https://checkout-offline.aplazo.dev/main/e8ac9e5d-fd79-4778-ab5d-ab3f7708c7d7',
    loanId: 46078,
    merchantId: 12,
    products: [],
    branchId: 173,
    sellsAgentId: 0,
  },
  {
    id: 21393,
    createdAt: '2022-08-24T23:52:01.939634Z',
    updatedAt: '2022-08-24T23:52:01.939634Z',
    DeletedAt: null,
    date: '2022-08-24 23:52:01.936724718 +0000 UTC m=+861945.949508363',
    status: 'created',
    price: 123,
    url: 'https://checkout-offline.aplazo.dev/main/9e835158-eef0-45e4-9c24-dcad658c98ab',
    loanId: 46079,
    merchantId: 12,
    products: [],
    branchId: 173,
    sellsAgentId: 0,
  },
];
