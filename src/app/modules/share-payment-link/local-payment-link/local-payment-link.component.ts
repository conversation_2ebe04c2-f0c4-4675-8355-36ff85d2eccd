import { Component, Input } from '@angular/core';

@Component({
  standalone: true,
  selector: 'aplazo-local-payment-link',
  template: `
    <div>
      <h2 class="text-xl">Link para continuar a la pasarela de pago.</h2>
      <div class="actions">
        <button
          class="aplazo-button aplazo-button--primary"
          (click)="openLink()">
          Abrir link de pago
        </button>
      </div>
    </div>
  `,
  styles: [
    `
      .text-subheading-1 {
        margin: 1rem auto 2rem;
      }
      .actions {
        display: flex;
        justify-content: center;
        width: 100%;
      }

      .aplazo-button {
        width: auto;
        min-width: 100px;
        padding: 0.5rem 1rem;
      }
    `,
  ],
})
export class LocalPaymentLinkComponent {
  @Input() paymentURL = '';

  public openLink() {
    window.open(this.paymentURL);
  }
}
