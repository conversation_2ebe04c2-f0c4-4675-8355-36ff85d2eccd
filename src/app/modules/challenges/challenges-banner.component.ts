import { Component, inject } from '@angular/core';
import { POS_APP_ROUTES } from '../../core/domain/config/app-routes.core';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { RouterModule } from '@angular/router';

@Component({
  standalone: true,
  imports: [AplazoButtonComponent, RouterModule],
  selector: 'aplazo-challenges-banner',
  templateUrl: './challenges-banner.component.html',
})
export class ChallengesBannerComponent {
  readonly appRoutes = inject(POS_APP_ROUTES);
}
