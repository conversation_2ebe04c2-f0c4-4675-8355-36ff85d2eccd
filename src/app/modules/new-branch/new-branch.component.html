@if (injectedText$ | async; as injectedText) {
  <main class="max-w-lg pt-8 mx-auto pb-[30vh]">
    <div class="text-center my-14">
      <aplz-ui-logo></aplz-ui-logo>

      <h5 class="text-xl font-medium my-8">
        {{ injectedText.title }}
      </h5>

      <p class="max-w-sm my-8 mx-auto text-dark-secondary">
        {{ injectedText.legend }}
      </p>
    </div>

    <div class="max-w-sm mx-auto my-10">
      <aplz-ui-form-field>
        <aplz-ui-form-label>
          {{ injectedText.input.label }}
        </aplz-ui-form-label>
        <input
          aplzFormInput
          type="text"
          [placeholder]="injectedText.input.placeholder"
          [formControl]="branchNameControl" />

        <p
          aplzFormError
          *ngIf="
            branchNameControl.touched && branchNameControl.hasError('required')
          ">
          {{ injectedText.input.errorRequired }}
        </p>
      </aplz-ui-form-field>
    </div>

    <div class="max-w-sm mx-auto">
      <button
        aplzButton
        aplzAppearance="solid"
        aplzColor="dark"
        size="md"
        [fullWidth]="true"
        class="my-10"
        data-test="new-branch-button-save-branch"
        [disabled]="branchNameControl.pristine || branchNameControl.invalid"
        (click)="saveBranch()">
        {{ injectedText.actions.save }}
      </button>

      <button
        aplzButton
        aplzAppearance="basic"
        aplzColor="light"
        size="md"
        [fullWidth]="true"
        data-test="new-branch-button-logout"
        (click)="logOut()">
        {{ injectedText.actions.cancel }}
      </button>
    </div>
  </main>
}
