import { Async<PERSON>ipe } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  ComponentFixture,
  discardPeriodicTasks,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { NavigationEnd, Router } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { TagManagerService } from '@aplazo/front-analytics/tag-manager';
import { LoaderService, WebchatService } from '@aplazo/merchant/shared';
import { AplazoPillLoaderComponent } from '@aplazo/shared-ui/loader';
import { BehaviorSubject, of, Subject } from 'rxjs';
import {
  POS_ENVIRONMENT_CORE,
  posEnvironmentCore,
} from '../app-core/domain/environments'; // ✅ ADD IMPORT
import { AppComponent } from '../app/app.component';
import { StoreService } from '../app/core/application/services/store.service';
import { MerchantsService } from '../app/core/services/merchants.service';
import { PopupTriggerService } from '../app/modules/shared/popup/application/services/popup-trigger.service';

describe('AppComponent', () => {
  let fixture: ComponentFixture<AppComponent>;
  let component: AppComponent;
  let router: Router;
  let storeService: jasmine.SpyObj<StoreService>;
  let loaderService: jasmine.SpyObj<LoaderService>;
  let tagManagerService: jasmine.SpyObj<TagManagerService>;
  let webchatService: jasmine.SpyObj<WebchatService<any>>;
  let merchantsService: jasmine.SpyObj<MerchantsService>;

  // Mock subjects for observables
  const isLoadingSubject = new BehaviorSubject<boolean>(false);
  const loaderMessageSubject = new BehaviorSubject<string[]>(['Loading...']);
  const tokenSubject = new BehaviorSubject<string>('test-token');
  const merchantNameSubject = new BehaviorSubject<string>('Test Merchant');
  const merchantConfigSubject = new BehaviorSubject<any>({ merchantId: 123 });
  const selectedBranchSubject = new BehaviorSubject<any>({
    id: 456,
    name: 'Test Branch',
  });
  const chatInitializedSubject = new BehaviorSubject<boolean>(true);
  const integrationTypeSubject = new BehaviorSubject<string>('TEST');
  const getMerchantIdSubject = new BehaviorSubject<number>(123);
  const routerEvents = new Subject<any>();

  beforeEach(() => {
    // Create spies for all required services
    storeService = jasmine.createSpyObj('StoreService', ['getMerchantId$'], {
      token$: tokenSubject,
      merchantName$: merchantNameSubject,
      merchantConfig$: merchantConfigSubject,
      selectedBranch$: selectedBranchSubject,
      integrationType$: integrationTypeSubject,
    });
    storeService.getMerchantId$.and.returnValue(getMerchantIdSubject);

    loaderService = jasmine.createSpyObj('LoaderService', [], {
      isLoading$: isLoadingSubject,
      loaderMessage$: loaderMessageSubject,
    });

    tagManagerService = jasmine.createSpyObj('TagManagerService', [
      'trackEvent',
    ]);

    webchatService = jasmine.createSpyObj(
      'WebchatService',
      ['show', 'hide', 'setCustomerChatAttributes'],
      { chatInitialized$: chatInitializedSubject }
    );

    merchantsService = jasmine.createSpyObj('MerchantsService', [
      'getMerchantDetails',
    ]);
    merchantsService.getMerchantDetails.and.returnValue(
      of({
        email: '<EMAIL>',
        status: 'active',
        channel: 'web',
        segment: 'retail',
        name: 'Test Merchant',
      })
    );

    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule,
        HttpClientTestingModule,
        AsyncPipe,
        AplazoPillLoaderComponent,
      ], // ✅ ADD HttpClientTestingModule
      providers: [
        { provide: StoreService, useValue: storeService },
        { provide: LoaderService, useValue: loaderService },
        { provide: TagManagerService, useValue: tagManagerService },
        { provide: WebchatService, useValue: webchatService },
        { provide: MerchantsService, useValue: merchantsService },
        {
          provide: PopupTriggerService,
          useValue: jasmine.createSpyObj('PopupTriggerService', [
            'cleanupOldRecords',
          ]),
        },
        {
          provide: POS_ENVIRONMENT_CORE, // ✅ ADD ENVIRONMENT PROVIDER
          useValue: posEnvironmentCore,
        },
      ],
    });

    fixture = TestBed.createComponent(AppComponent);
    component = fixture.componentInstance;
    router = TestBed.inject(Router);

    // Mock router events using property definition instead of spyOn
    Object.defineProperty(router, 'events', { value: routerEvents });

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should bind loader state from LoaderService', fakeAsync(() => {
    let isLoading: boolean | undefined;
    let loaderMessage: any;

    component.isLoading$.subscribe(value => (isLoading = value));
    component.loaderMessage$.subscribe(value => (loaderMessage = value));

    tick();

    expect(isLoading).toBeFalse();
    expect(loaderMessage).toEqual(['Loading...']);

    isLoadingSubject.next(true);
    loaderMessageSubject.next(['Processing...']);

    tick();

    expect(isLoading).toBeTrue();
    expect(loaderMessage).toEqual(['Processing...']);
  }));

  it('should track page view on NavigationEnd event', fakeAsync(() => {
    // Simulate navigation end event
    routerEvents.next(new NavigationEnd(1, '/', '/'));
    tick();

    expect(tagManagerService.trackEvent).toHaveBeenCalledWith({
      event: 'pageView',
      merchantId: 123,
      merchantName: 'Test Merchant',
      branchId: 456,
      branchName: 'Test Branch',
    });
  }));

  it('should initialize webchat service during setup', fakeAsync(() => {
    // The initialization happens in ngOnInit, which is already called in beforeEach
    tick();

    // Verify webchat is initialized properly
    expect(webchatService.setCustomerChatAttributes).toHaveBeenCalled();
    discardPeriodicTasks();
  }));

  it('should set customer chat attributes when authenticated', fakeAsync(() => {
    tick();

    expect(webchatService.setCustomerChatAttributes).toHaveBeenCalledWith({
      attributes: {
        sharedEmails: ['<EMAIL>'],
        sharedPhones: [],
      },
      customAttributes: {
        merchantIdNum: 123,
        merchantNameStr: 'Test Merchant',
        merchantintegrationStr: 'TEST',
        storefrontNameStr: 'Test Branch',
        storefrontIdNum: 456,
        correoStr: '<EMAIL>',
        statusStr: 'active',
        merchantchannelStr: 'web',
        merchantsegmentStr: 'retail',
      },
    });

    discardPeriodicTasks();
  }));
});
