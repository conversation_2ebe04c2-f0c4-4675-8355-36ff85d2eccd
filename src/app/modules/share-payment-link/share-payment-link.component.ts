import { NgIf } from '@angular/common';
import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, inject } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { AplazoTabsComponents } from '@aplazo/shared-ui/tabs';
import { DialogRef } from '@ngneat/dialog';
import { StatsigService } from '@statsig/angular-bindings';
import { BehaviorSubject, Observable, Subject, map, takeUntil } from 'rxjs';
import { AnalyticsService } from '../../core/application/services/analytics.service';
import {
  NOTIFICATION_CHANNELS,
  NotificationChannel,
} from '../../core/domain/dtos';
import { Branch } from '../../core/domain/entities';
import { Order } from '../../core/domain/order.interface';
import { EventService } from '../../core/domain/repositories/events-service';
import { ConfirmFirstInstallmentUsecase } from './application/confirm-first-installment.usecase';
import { RequestAuthorizationCodeUsecase } from './application/request-authorization-code.usecase';
import { SendLinkUsecase } from './application/send-link.usecase';
import { BarcodeComponent } from './barcode/infra/components/barcode.component';
import { DynamicTokenComponent } from './dynamic-token/dynamic-token.component';
import { LocalPaymentLinkComponent } from './local-payment-link/local-payment-link.component';
import { NoInternetPurchaseComponent } from './no-internet-purchase/infra/components/no-internet-purchase.component';
import { QrImageComponent } from './qr-image.component';
import { SentWhatsappLinkFormComponent } from './sent-whatsapp-link-form/sent-whatsapp-link-form.component';
import { TogglePaymentLinkDialogService } from './toggle-payment-link-dialog.service';

@Component({
  standalone: true,
  selector: 'aplazo-share-payment-link',
  template: `
    <aplz-ui-card size="md">
      <header class="text-center">
        <h4 class="text-lg">Métodos de pago</h4>
        <p class="text-dark-secondary font-light text-base">
          Orden
          {{ data.loan?.loanId }}
        </p>
      </header>

      <aplz-ui-tab-group (tabSelectionChange)="changeTab($event)">
        <aplz-ui-tab label="Teléfono">
          <aplz-ui-tab-body>
            <aplazo-sent-whatsapp-link-form
              [branch]="data.branch"
              (sendLinkEvent)="sendLink($event)"
              (cancelClick)="close()"></aplazo-sent-whatsapp-link-form>
          </aplz-ui-tab-body>
        </aplz-ui-tab>

        <aplz-ui-tab label="Link de pago" *ngIf="data.showPaymentLink === true">
          <aplz-ui-tab-body>
            <aplazo-local-payment-link
              [paymentURL]="data.loan?.url || ''"></aplazo-local-payment-link>
          </aplz-ui-tab-body>
        </aplz-ui-tab>

        <aplz-ui-tab label="Qr">
          <aplz-ui-tab-body>
            <aplazo-qr-image
              [loanId]="data.loan?.loanId ?? null"></aplazo-qr-image>
          </aplz-ui-tab-body>
        </aplz-ui-tab>

        <aplz-ui-tab
          label="Código de Barras"
          [hidden]="barcodeEnabled === false">
          <aplz-ui-tab-body>
            <app-barcode
              [data]="{
                order: data.loan,
                branch: data.branch
              }"
              (close)="close()"></app-barcode>
          </aplz-ui-tab-body>
        </aplz-ui-tab>

        <aplz-ui-tab
          label="Código de autorización"
          [hidden]="authCodeEnabled === false">
          <aplz-ui-tab-body>
            <aplazo-no-internet-purchase
              [loanId]="data.loan?.loanId"
              (cancel)="close()"></aplazo-no-internet-purchase>
          </aplz-ui-tab-body>
        </aplz-ui-tab>

        <aplz-ui-tab
          label="Compra Sin Internet"
          [hidden]="dynamicTokenEnabled === false">
          <aplz-ui-tab-body>
            <aplazo-dynamic-token-container
              [loanId]="data.loan?.loanId"></aplazo-dynamic-token-container>
          </aplz-ui-tab-body>
        </aplz-ui-tab>
      </aplz-ui-tab-group>
    </aplz-ui-card>
  `,
  imports: [
    AplazoTabsComponents,
    AplazoCardComponent,
    AplazoFormFieldDirectives,
    ReactiveFormsModule,
    SentWhatsappLinkFormComponent,
    LocalPaymentLinkComponent,
    DynamicTokenComponent,
    NgIf,
    NoInternetPurchaseComponent,
    QrImageComponent,
    BarcodeComponent,
  ],
})
export class SharePaymentLinkComponent implements OnInit, OnDestroy {
  readonly #sendLinkUsecase: SendLinkUsecase = inject(SendLinkUsecase);
  readonly #sharePaymentLinkService: TogglePaymentLinkDialogService = inject(
    TogglePaymentLinkDialogService
  );
  readonly #dialogRef: DialogRef<
    {
      branch: Branch;
      loan: Order | null;
      showPaymentLink: boolean;
    },
    void
  > = inject(DialogRef);
  readonly #analyticsService = inject(AnalyticsService);
  readonly #eventService = inject(EventService);
  readonly #requestAuthCode = inject(RequestAuthorizationCodeUsecase);
  readonly #confirmFirstInstallment: ConfirmFirstInstallmentUsecase = inject(
    ConfirmFirstInstallmentUsecase
  );
  readonly #flagsService = inject(StatsigService);

  readonly #destroy$ = new Subject<void>();

  readonly authCodeEnabled = this.#flagsService.checkGate(
    'b2b_front_posui_auth_code'
  );

  readonly dynamicTokenEnabled = this.#flagsService.checkGate(
    'b2b_front_posui_dynamic_tokens'
  );

  readonly barcodeEnabled = this.#flagsService.checkGate(
    'b2b_front_posui_barcode_enabled'
  );

  get data() {
    return this.#dialogRef.data;
  }

  sendLink(event: { phone: number; target: string }): void {
    if (this.#dialogRef.data.loan) {
      this.#sendLinkUsecase.execute({
        loanId: this.#dialogRef.data.loan.loanId,
        phone: event.phone,
        target: event.target as NotificationChannel,
        dialogRef: this.#dialogRef,
      });

      if (event.target === NOTIFICATION_CHANNELS.WHATSAPP) {
        this.#analyticsService.track('buttonClick', {
          buttonName: 'shareViaWhatsapp',
          genericInfo: String(event.phone),
        });
      }
      if (event.target === NOTIFICATION_CHANNELS.SMS) {
        this.#analyticsService.track('buttonClick', {
          buttonName: 'shareViaSms',
          genericInfo: String(event.phone),
        });
      }
    }
  }

  close(): void {
    this.#dialogRef.close();
  }

  changeTab(event: { index: number }) {
    if (event.index === 0) {
      this.#analyticsService.track('buttonClick', {
        buttonName: 'toShareViaPhone',
        loanId: this.data.loan?.loanId,
      });
    }
    if (event.index === 1) {
      this.#analyticsService.track('buttonClick', {
        buttonName: 'toShareViaQr',
        loanId: this.data.loan?.loanId,
      });
      this.#eventService
        .shareByQrCode({
          order_id: this.data.loan?.loanId || 0,
        })
        .subscribe();
    }
  }

  ngOnInit(): void {
    this.#eventService
      .shareOrder({
        order_id: this.data.loan?.loanId || 0,
      })
      .subscribe();
    this.#sharePaymentLinkService.notifyDialogOpen();
    this.#sharePaymentLinkService.isOpenDialog$
      .pipe(
        takeUntil(this.#destroy$),
        map(isOpenDialog => !isOpenDialog)
      )
      .subscribe(hasCloseRequest => {
        if (hasCloseRequest) {
          this.close();
        }
      });
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  #authCodeSent = new BehaviorSubject<boolean>(false);
  #defaultSchemeId = -1;

  requestAuthorizationCode(phone: number): void {
    const loanToken = this.data.loan?.url.split('/').slice(-1)[0] || '';
    this.#requestAuthCode
      .execute(phone, loanToken)
      .pipe(takeUntil(this.#destroy$))
      .subscribe({
        next: (defaultSchemeId: number) => {
          this.#defaultSchemeId = defaultSchemeId;
          this.#authCodeSent.next(true);
        },
      });
  }

  updatePhoneNumber(): void {
    this.#authCodeSent.next(false);
  }

  confirmFirstInstallment(data: { phone: number; authCode: string }): void {
    const { phone, authCode } = data;
    this.#confirmFirstInstallment.execute(
      this.data.loan?.loanId || 0,
      phone,
      authCode,
      this.#defaultSchemeId,
      this.#dialogRef
    );
  }

  get authCodeSent(): Observable<boolean> {
    return this.#authCodeSent.asObservable();
  }
}
