version: '3'
services:
  tagged1:
    container_name: posui_tagged1
    image: ${TAG_1}
    build:
      context: .
      dockerfile: Dockerfile
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
    env_file:
      - .env
    ports:
      - 80:80

  tagged2:
    container_name: posui_tagged2
    image: ${TAG_2}
    build:
      context: .
      dockerfile: Dockerfile
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
    env_file:
      - .env
    ports:
      - 80:80

  posui_local:
    container_name: posuiLocal
    image: posui_local
    build:
      context: .
      dockerfile: Dockerfile
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
    env_file:
      - .env
    ports:
      - 4200:80

  posui_local_dev:
    container_name: posuiLocalDev
    image: posui_local_dev
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
        ENV: ${DEV_ENV}
    env_file:
      - .env
    ports:
      - 4200:4200
    volumes:
      - '/usr/src/app/node_modules'
      - '.:/usr/src/app'

  posui_local_test:
    container_name: posuiLocalTest
    image: posui_local_test
    build:
      context: .
      dockerfile: Dockerfile.test
      args:
        AUTH_TOKEN: ${AUTH_TOKEN}
    env_file:
      - .env
    ports:
      - 4200:4200
    volumes:
      - posui_local_test_coverage_vol:/usr/src/app/coverage

volumes:
  posui_app:
  posui_local_test_coverage_vol:
