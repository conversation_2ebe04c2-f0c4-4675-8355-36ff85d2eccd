import {
  ChangeDetectionStrategy,
  Component,
  computed,
  inject,
  input,
  SecurityContext,
  signal,
  ViewEncapsulation,
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { RedirectionService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AnalyticsService } from '../../../../../app/core/application/services/analytics.service';
import { DynamicBannerContentUI } from '../../domain/entities/banner';

@Component({
  standalone: true,
  selector: 'aplazo-dynamic-banner',
  template: `
    @if (sanitizedContent()) {
      <article
        class="bg-aplazo-aplazo rounded-lg p-4 flex flex-wrap items-center gap-4">
        @if (
          sanitizedContent() &&
          sanitizedContent()!.imageUrl &&
          sanitizedContent()!.imageUrl!.length > 0
        ) {
          <figure class="flex-shrink-0 flex-grow-0 w-28">
            <img
              [src]="sanitizedContent()!.imageUrl"
              alt="banner content relative image"
              class="w-full h-full object-cover"
              (error)="onImageLoadError()" />
          </figure>
        }

        <div class="flex-1 flex-grow flex-shrink">
          <h1 class="text-md font-bold mb-1">
            {{ sanitizedContent()!.title }}
          </h1>

          <p class="text-sm">
            {{ sanitizedContent()!.description }}
          </p>
        </div>

        <button
          class="flex-shrink-0 flex-grow-0"
          aplzButton
          aplzAppearance="solid"
          aplzColor="dark"
          size="sm"
          [rounded]="true"
          (click)="goToCta()">
          {{ content()!.ctaText }}
        </button>
      </article>
    }
  `,
  imports: [AplazoButtonComponent],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DynamicBannerComponent {
  readonly #sanitizer = inject(DomSanitizer);
  readonly #redirecter = inject(RedirectionService);
  readonly #analytics = inject(AnalyticsService);

  readonly content = input.required<DynamicBannerContentUI | null>();
  readonly #loadedImage = signal<boolean>(true);

  readonly sanitizedContent = computed(() => {
    const imageUrl =
      this.#loadedImage() && this.content()?.imageUrl
        ? this.content()?.imageUrl
        : '';

    if (!this.content()?.isInternalRedirect) {
      const ctaUrl = this.#sanitizer.sanitize(
        SecurityContext.URL,
        this.content()?.ctaUrl ?? ''
      );

      return {
        ...this.content(),
        ctaUrl,
        imageUrl,
      };
    }

    return {
      ...this.content(),
      imageUrl,
    };
  });

  async goToCta(): Promise<void> {
    this.#analytics.track('buttonClick', {
      buttonName: 'posui_buttonbanner_click',
      genericInfo: this.sanitizedContent().ctaUrl ?? '',
      label: this.sanitizedContent().ctaText ?? '',
      loanId: this.sanitizedContent()?.id ?? 0,
      timestamp: new Date().getTime(),
    });

    if (this.sanitizedContent()?.isInternalRedirect) {
      this.#redirecter.internalNavigation(
        this.sanitizedContent()!.ctaUrl ?? ''
      );
    } else {
      this.#redirecter.externalNavigation(
        this.sanitizedContent()?.ctaUrl ?? '',
        '_blank'
      );
    }
  }

  onImageLoadError(): void {
    this.#loadedImage.set(false);
  }
}
