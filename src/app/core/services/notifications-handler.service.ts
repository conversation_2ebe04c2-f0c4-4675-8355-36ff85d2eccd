import { inject, Injectable } from '@angular/core';
import { combineLatest, map, Observable, shareReplay, take } from 'rxjs';
import { GetNotificationsByTypeUsecase } from '../application/get-notifications.usecase';
import { GetTrainingContentMediaUsecase } from '../application/get-training-media-content.usecase';
import {
  INotificationsWithNewOnesUIDto,
  ITrainingMediaContentUIDto,
} from '../domain/notification';

@Injectable({ providedIn: 'root' })
export class NotificationsHandlerService {
  readonly #getNotifications = inject(GetNotificationsByTypeUsecase);
  readonly #getTrainingContent = inject(GetTrainingContentMediaUsecase);

  getNotifications$(): Observable<INotificationsWithNewOnesUIDto[]> {
    return combineLatest({
      promos: this.#getNotifications.execute('PROMO'),
      features: this.#getNotifications.execute('FEATURE'),
      infos: this.#getNotifications.execute('INFO'),
    }).pipe(
      map(s => {
        const result: INotificationsWithNewOnesUIDto[] = [];

        if (s.promos) {
          result.push(s.promos);
        }

        if (s.features) {
          result.push(s.features);
        }

        if (s.infos) {
          result.push(s.infos);
        }

        return result;
      }),

      take(1)
    );
  }

  getTrainingContent$(): Observable<ITrainingMediaContentUIDto> {
    return this.#getTrainingContent.execute().pipe(
      map(content => {
        if (!content) {
          return {
            tags: [],
            content: [],
          };
        }

        return content;
      }),

      shareReplay(1)
    );
  }
}
