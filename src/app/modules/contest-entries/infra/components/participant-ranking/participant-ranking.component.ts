import { AsyncPipe } from '@angular/common';
import { Component, inject, Input } from '@angular/core';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconChevronDown, iconChevronUp } from '@aplazo/ui-icons';
import { BehaviorSubject, combineLatest, map } from 'rxjs';
import { ParticipantRankUI } from '../../../domain/contest';

@Component({
  standalone: true,
  selector: 'app-participant-ranking',
  template: `
    @if (vm$ | async; as context) {
      <div class="flex-grow flex-shrink-0 mx-auto flex flex-col justify-center">
        <h2
          class="text-lg text-dark-primary text-center text-pretty max-w-32 mx-auto">
          Mi posición en el ranking
        </h2>
        <article
          class="h-60 relative flex flex-col md:px-8 py-2 border border-dark-background sm:rounded-lg overflow-y-auto">
          <button
            aplzButton
            (click)="scrollList('lower')"
            [disabled]="!context.hasPrevious">
            <aplz-ui-icon name="chevron-up"></aplz-ui-icon>
          </button>
          <ul class="flex flex-col h-full w-full my-2">
            @for (item of context.list; track item.rank) {
              <li
                class="p-3 h-18 text-pretty flex-shrink-0 flex-grow flex items-center bg-white truncate"
                [class.opacity-60]="!item.matched"
                [class.text-md]="!item.matched"
                [class.bg-dark-background]="item.matched"
                [class.font-medium]="item.matched">
                <span class="mr-2 flex-shrink-0">
                  #
                  {{ item.rank }}
                </span>

                <span class="mr-2">
                  {{ item.matched ? 'tienes' : 'tiene' }}
                </span>

                <span class="font-medium">
                  {{ item.totalRegistration }}
                </span>

                <span class="ml-2 flex-shrink-0"> registros completados </span>
              </li>
            }
          </ul>

          <button
            aplzButton
            (click)="scrollList('upper')"
            [disabled]="!context.hasNext">
            <aplz-ui-icon name="chevron-down"></aplz-ui-icon>
          </button>
        </article>
      </div>
    }
  `,
  imports: [AsyncPipe, AplazoIconComponent, AplazoButtonComponent],
})
export class ParticipantRankingComponent {
  readonly #iconRegister = inject(AplazoIconRegistryService);

  readonly _list$ = new BehaviorSubject<ParticipantRankUI[]>([]);

  readonly _pagination$ = new BehaviorSubject<{
    firstIndex: number;
    lastIndex: number;
    total: number;
    hasPrevious: boolean;
    hasNext: boolean;
  }>({
    firstIndex: 0,
    lastIndex: 0,
    total: 0,
    hasPrevious: false,
    hasNext: false,
  });

  readonly vm$ = combineLatest({
    list: this._list$.pipe(),
    pagination: this._pagination$.pipe(),
  }).pipe(
    map(({ list, pagination }) => {
      return {
        list: list.slice(pagination.firstIndex, pagination.lastIndex),
        hasNext: pagination.hasNext,
        hasPrevious: pagination.hasPrevious,
      };
    })
  );

  @Input()
  set list(value: ParticipantRankUI[] | null) {
    if (!value || !Array.isArray(value)) {
      this._list$.next([]);
      this._pagination$.next({
        firstIndex: 0,
        lastIndex: 0,
        total: 0,
        hasPrevious: false,
        hasNext: false,
      });
      return;
    }

    const middleIndex = value.findIndex(i => i.matched === true);
    const truncatedMiddleIndex = middleIndex < 0 ? 0 : middleIndex;

    const firstIndex = Math.max(0, truncatedMiddleIndex - 1);
    const lastIndex = Math.min(value.length, truncatedMiddleIndex + 2);

    this._pagination$.next({
      firstIndex,
      lastIndex,
      total: value.length,
      hasPrevious: firstIndex > 0,
      hasNext: value.length > lastIndex,
    });

    this._list$.next(value);
  }

  constructor() {
    this.#iconRegister.registerIcons([iconChevronDown, iconChevronUp]);
  }

  scrollList(s: 'lower' | 'upper'): void {
    const pag = this._pagination$.getValue();

    let nextFirstIndex = pag.firstIndex;
    let nextLastIndex = pag.lastIndex;

    if (s === 'lower') {
      nextFirstIndex = pag.hasPrevious ? pag.firstIndex - 1 : nextFirstIndex;
      nextLastIndex = pag.hasPrevious ? pag.lastIndex - 1 : nextLastIndex;

      this._pagination$.next({
        ...pag,
        firstIndex: nextFirstIndex,
        lastIndex: nextLastIndex,
        hasPrevious: nextFirstIndex > 0,
        hasNext: pag.total > nextLastIndex,
      });
    } else {
      nextLastIndex = pag.hasNext ? pag.lastIndex + 1 : nextLastIndex;
      nextFirstIndex = pag.hasNext ? pag.firstIndex + 1 : nextFirstIndex;

      this._pagination$.next({
        ...pag,
        firstIndex: nextFirstIndex,
        lastIndex: nextLastIndex,
        hasPrevious: nextFirstIndex > 0,
        hasNext: pag.total > nextLastIndex,
      });
    }
  }
}
