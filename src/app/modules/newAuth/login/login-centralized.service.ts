import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';

import { catchError, from, map, Observable, of, throwError } from 'rxjs';

import { JWTToken } from '../../../core/domain/access-token';
import {
  POS_ENVIRONMENT_CORE,
  PosEnvironmentCoreType,
} from '../../../../app-core/domain/environments';
import { CredentialsV2 } from 'src/app/core/domain/credentialsV2.interface';
import { LoginCentralizedRepository } from './domain/login-centralized.repository';

@Injectable()
export class LoginCentralizedService implements LoginCentralizedRepository {
  constructor(
    private http: HttpClient,
    @Inject(POS_ENVIRONMENT_CORE)
    private environment: PosEnvironmentCoreType
  ) {}

  public execute(credentials: CredentialsV2): Observable<JWTToken> {
    return this.http
      .post<any>(
        `${this.environment.exposedPublicApiUrl}/api/v1/auth/merchant/login`,
        {
          merchantUsername: credentials.merchantUsername,
          merchantPassword: credentials.merchantPassword,
          authType: credentials.authType,
        }
      )
      .pipe(
        map(response => response.authToken.replace('Bearer ', '')),
        catchError(error => throwError(() => error))
      );
  }

  getMerchantDetails(token: string): Observable<{ name: string }> {
    const headers = new HttpHeaders({ Authorization: `Bearer ${token}` });

    return this.http
      .get<any>(
        `${this.environment.posApiUrl}/api/merchant_configs/merchant-details`,
        { headers }
      )
      .pipe(
        map(res => {
          if (
            Object.prototype.hasOwnProperty.call(res, 'content') &&
            Object.prototype.hasOwnProperty.call(res, 'code')
          ) {
            return res.content;
          }

          return res;
        }),
        catchError(() => {
          return of({ name: '' } as { name: string });
        })
      );
  }
}
