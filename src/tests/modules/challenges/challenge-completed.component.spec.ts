import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DialogRef } from '@ngneat/dialog';
import { ChallengeCompletedComponent } from '../../../app/modules/challenges/challenge-completed.component';
import { Challenge } from '../../../app/core/domain/challenge';

describe('ChallengeCompletedComponent', () => {
  let component: ChallengeCompletedComponent;
  let fixture: ComponentFixture<ChallengeCompletedComponent>;
  let dialogRefMock: jasmine.SpyObj<DialogRef<{ challenge: Challenge }>>;

  beforeEach(async () => {
    dialogRefMock = jasmine.createSpyObj('DialogRef', ['close']);
    await TestBed.configureTestingModule({
      providers: [{ provide: DialogRef, useValue: dialogRefMock }],
    }).compileComponents();

    fixture = TestBed.createComponent(ChallengeCompletedComponent);
    component = fixture.componentInstance;
  });

  it('initializes with a challenge from DialogRef', () => {
    const challenge: Challenge = {
      branchId: 0,
      condition: { goalType: 'LOANS', goalValue: 100, minimalLoanAmount: 0 },
      creationDate: '',
      currentScore: 0,
      description: '',
      merchantId: 0,
      periodType: 'week',
      reward: '',
      start: '',
      status: 'Active',
      id: '1',
      challengeName: 'Test Challenge',
      end: new Date().toISOString(),
    };
    dialogRefMock.data = { challenge };
    fixture.detectChanges();
    expect(component.ref.data.challenge).toEqual(challenge);
  });

  it('closes the dialog when close is called', () => {
    component.ref.close();
    expect(dialogRefMock.close).toHaveBeenCalled();
  });
});
