/* eslint-disable max-len */
/* eslint-disable id-blacklist */
//--- stub implementation of createSpyObj with correct type

export type Func = (...args: any[]) => any;

export type SpyObjMethodNames<T = undefined> = T extends undefined
  ? ReadonlyArray<string> | { [methodName: string]: any }
  :
      | ReadonlyArray<keyof T>
      | { [P in keyof T]?: T[P] extends Func ? ReturnType<T[P]> : any };

export type SpyObjPropertyNames<T = undefined> = T extends undefined
  ? ReadonlyArray<string> | { [propertyName: string]: any }
  : ReadonlyArray<keyof T> | { [P in keyof T]?: T[P] };

export type SpyObj<T> = T & {
  [K in keyof T]: T[K] extends Func ? T[K] & Spy<T[K]> : T[K];
};

export interface Calls<Fn extends Func> {
  /** By chaining the spy with calls.any(), will return false if the spy has not been called at all, and then true once at least one call happens. */
  any(): boolean;
  /** By chaining the spy with calls.count(), will return the number of times the spy was called */
  count(): number;
  /** By chaining the spy with calls.argsFor(), will return the arguments passed to call number index */
  argsFor(index: number): Parameters<Fn>;
  /** By chaining the spy with calls.allArgs(), will return the arguments to all calls */
  allArgs(): ReadonlyArray<Parameters<Fn>>;
  /** By chaining the spy with calls.all(), will return the context (the this) and arguments passed all calls */
  all(): ReadonlyArray<CallInfo<Fn>>;
  /** By chaining the spy with calls.mostRecent(), will return the context (the this) and arguments for the most recent call */
  mostRecent(): CallInfo<Fn>;
  /** By chaining the spy with calls.first(), will return the context (the this) and arguments for the first call */
  first(): CallInfo<Fn>;
  /** By chaining the spy with calls.reset(), will clears all tracking for a spy */
  reset(): void;
  /** Set this spy to do a shallow clone of arguments passed to each invocation. */
  saveArgumentsByValue(): void;
}

export interface CallInfo<Fn extends Func> {
  /** The context (the this) for the call */
  object: any;
  /** All arguments passed to the call */
  args: Parameters<Fn>;
  /** The return value of the call */
  returnValue: ReturnType<Fn>;
}

export interface SpyAnd<Fn extends Func> {
  identity: string;

  /** By chaining the spy with and.callThrough, the spy will still track all calls to it but in addition it will delegate to the actual implementation. */
  callThrough(): Spy<Fn>;
  /** By chaining the spy with and.returnValue, all calls to the function will return a specific value. */
  returnValue(val: ReturnType<Fn>): Spy<Fn>;
  /** By chaining the spy with and.returnValues, all calls to the function will return specific values in order until it reaches the end of the return values list. */
  returnValues(...values: Array<ReturnType<Fn>>): Spy<Fn>;
  /** By chaining the spy with and.callFake, all calls to the spy will delegate to the supplied function. */
  callFake(fn: Fn): Spy<Fn>;
  /** Tell the spy to return a promise resolving to the specified value when invoked. */
  resolveTo(val?: PromisedReturnType<Fn>): Spy<Fn>;
  /** Tell the spy to return a promise rejecting with the specified value when invoked. */
  rejectWith(val?: PromisedRejectType<Fn>): Spy<Fn>;
  /** By chaining the spy with and.throwError, all calls to the spy will throw the specified value. */
  throwError(msg: string | Error): Spy;
  /** When a calling strategy is used for a spy, the original stubbing behavior can be returned at any time with and.stub. */
  stub(): Spy;
}

export type PromisedReturnType<Fn extends Func> = Fn extends (
  ...args: any[]
) => PromiseLike<infer TResult>
  ? TResult
  : never;

// eslint-disable-next-line @typescript-eslint/ban-types
export type PromisedRejectType<Fn extends Function> = Fn extends (
  ...args: any[]
) => PromiseLike<unknown>
  ? any
  : never;

export type MatchableArgs<Fn> = Fn extends (...args: infer P) => any
  ? { [K in keyof P]: P[K] | AsymmetricMatcher<any> }
  : never;

export interface AsymmetricMatcher<TValue> {
  asymmetricMatch(
    other: TValue,
    customTesters: ReadonlyArray<CustomEqualityTester>
  ): boolean;
  jasmineToString?(): string;
}

export type CustomEqualityTester = (first: any, second: any) => boolean | void;

export interface Spy<Fn extends Func = Func> {
  (...params: Parameters<Fn>): ReturnType<Fn>;

  and: SpyAnd<Fn>;
  calls: Calls<Fn>;
  withArgs(...args: MatchableArgs<Fn>): Spy<Fn>;
}
