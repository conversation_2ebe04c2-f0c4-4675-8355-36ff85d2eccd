import { Provider } from '@angular/core';
import { PopupRepository } from '../repositories/popup.repository';
import { PopupTriggerService } from '../../application/services/popup-trigger.service';
import { ShowPopupUseCase } from '../../application/usecases/show-popup.usecase';
import { CheckPaymentSuccessUseCase } from '../../application/usecases/check-payment-success.usecase';
import { PopupStorageService } from '../../application/services/popup-storage.service';
import { AplazoPopupDialogService } from '../components/aplazo-popup/aplazo-popup-dialog.service';

export const POPUP_PROVIDERS: Provider[] = [
  PopupRepository,
  PopupTriggerService,
  ShowPopupUseCase,
  CheckPaymentSuccessUseCase,
  PopupStorageService,
  AplazoPopupDialogService,
];
