import { TestBed } from '@angular/core/testing';
import { LoaderService, NotifierService } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
} from '@aplazo/merchant/shared-testing';
import { of } from 'rxjs';
import { NOTIFICATION_CHANNELS } from '../../../../app/core/domain/dtos';
import { PosOfflineRepository } from '../../../../app/core/domain/repositories/pos-offline.repository';
import { SendLinkUsecase } from '../../../../app/modules/share-payment-link/application/send-link.usecase';

describe('SendLinkUsecase', () => {
  let usecase: SendLinkUsecase;
  let repository: jasmine.SpyObj<PosOfflineRepository>;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;
  let notifierWarningSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        SendLinkUsecase,
        provideLoaderTesting(),
        provideNotifierTesting(),
        {
          provide: PosOfflineRepository,
          useValue: jasmine.createSpyObj('PosOfflineRepository', [
            'sendWSPaymentLink',
            'sendPaymentLink',
          ]),
        },
      ],
    });

    usecase = TestBed.inject(SendLinkUsecase);
    repository = TestBed.inject(
      PosOfflineRepository
    ) as jasmine.SpyObj<PosOfflineRepository>;
    const loader = TestBed.inject(LoaderService);
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    const notifier = TestBed.inject(NotifierService);
    notifierWarningSpy = spyOn(notifier, 'warning').and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(SendLinkUsecase);
  });

  it('should show a warning when the phone number is different at the required length', () => {
    const invalidPhoneLength = 123456789;

    usecase.execute({
      loanId: 1,
      phone: invalidPhoneLength,
      target: NOTIFICATION_CHANNELS.SMS,
    });

    expect(repository.sendPaymentLink).toHaveBeenCalledTimes(0);
    expect(repository.sendWSPaymentLink).toHaveBeenCalledTimes(0);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: 'Número de teléfono inválido',
      message: 'El número de teléfono debe tener 10 dígitos',
    });
  });

  it('should show a warning when the target is not selected', () => {
    const validPhone = 1234567890;

    usecase.execute({
      loanId: 1,
      phone: validPhone,
      // @ts-expect-error: Testing an invalid target
      target: '',
    });

    expect(repository.sendPaymentLink).toHaveBeenCalledTimes(0);
    expect(repository.sendWSPaymentLink).toHaveBeenCalledTimes(0);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: 'No se ha seleccionado un tipo de mensaje',
    });
  });

  it('should send a payment link via WhatsApp', () => {
    const validPhone = 1234567890;
    repository.sendWSPaymentLink.and.returnValue(of({} as any));

    usecase.execute({
      loanId: 1,
      phone: validPhone,
      target: NOTIFICATION_CHANNELS.WHATSAPP,
    });

    expect(repository.sendWSPaymentLink).toHaveBeenCalledWith({
      loanId: 1,
      phoneNumber: '521234567890',
    });
    expect(repository.sendWSPaymentLink).toHaveBeenCalledTimes(1);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.sendPaymentLink).toHaveBeenCalledTimes(0);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(0);
  });

  it('should send a payment link via SMS', () => {
    const validPhone = 1234567890;
    repository.sendPaymentLink.and.returnValue(of({} as any));

    usecase.execute({
      loanId: 1,
      phone: validPhone,
      target: NOTIFICATION_CHANNELS.SMS,
    });

    expect(repository.sendPaymentLink).toHaveBeenCalledTimes(1);
    expect(repository.sendPaymentLink).toHaveBeenCalledWith({
      loanId: 1,
      phoneNumber: '521234567890',
    });
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(repository.sendWSPaymentLink).toHaveBeenCalledTimes(0);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(0);
  });

  it('should show a warning when a target is sent but is neither WhatsApp nor SMS', () => {
    const validPhone = 1234567890;
    repository.sendPaymentLink.and.returnValue(of({} as any));

    usecase.execute({
      loanId: 1,
      phone: validPhone,
      target: NOTIFICATION_CHANNELS.EMAIL,
    });

    expect(repository.sendPaymentLink).toHaveBeenCalledTimes(0);
    expect(repository.sendWSPaymentLink).toHaveBeenCalledTimes(0);
    expect(loaderShowSpy).toHaveBeenCalledTimes(1);
    expect(loaderHideSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledTimes(1);
    expect(notifierWarningSpy).toHaveBeenCalledWith({
      title: 'No se ha seleccionado un tipo de mensaje',
    });
  });
});
