import { inject, Injectable } from '@angular/core';
import {
  combineLatest,
  fromEvent,
  Observable,
  startWith,
  Subject,
  takeUntil,
} from 'rxjs';
import { POS_ENVIRONMENT_CORE } from '../../../../../app-core/domain/environments';

export interface SSEPosEvent {
  message: MessageEvent | null;
  error: Event | null;
}

@Injectable({
  providedIn: 'root',
})
export class OrdersWithSSE {
  readonly #environment = inject(POS_ENVIRONMENT_CORE);
  readonly #apiUrl = this.#environment.posApiUrl;

  #source: EventSource | null = null;
  #destroy$: Subject<void> | undefined;

  listen(branchId: number): Observable<SSEPosEvent> {
    this.#destroy$ = new Subject();
    this.#source = this.#create(branchId);

    const message = fromEvent<MessageEvent>(this.#source, 'message').pipe(
      startWith(null),
      takeUntil(this.#destroy$)
    );
    const error = fromEvent(this.#source, 'error').pipe(
      startWith(null),
      takeUntil(this.#destroy$)
    );

    return combineLatest({
      message,
      error,
    });
  }

  close(): void {
    if (this.#source) {
      this.#destroy$?.next();
      this.#destroy$?.complete();
      this.#destroy$ = undefined;
      this.#source.close();
    }
  }

  #create(branchId: number): EventSource {
    return new EventSource(`${this.#apiUrl}/sse?branchId=${branchId}`);
  }
}
