import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ChallengeComponent } from '../../../app/modules/challenges/challenge.component';
import { CurrencyPipe, DatePipe } from '@angular/common';
import { CountdownDirective } from '../../../app/modules/shared/directives/countdown.directive';
import { Challenge } from '../../../app/core/domain/challenge';

describe('ChallengeComponent', () => {
  let component: ChallengeComponent;
  let fixture: ComponentFixture<ChallengeComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DatePipe, CurrencyPipe, CountdownDirective],
    }).compileComponents();

    fixture = TestBed.createComponent(ChallengeComponent);
    component = fixture.componentInstance;
  });

  it('isExpired returns true if challenge end date is in the past', () => {
    component.challenge = {
      end: new Date(Date.now() - 1000).toISOString(),
    } as Challenge;
    expect(component.isExpired()).toBeTrue();
  });

  it('isExpired returns false if challenge end date is in the future', () => {
    component.challenge = {
      end: new Date(Date.now() + 1000).toISOString(),
    } as Challenge;
    expect(component.isExpired()).toBeFalse();
  });
});
