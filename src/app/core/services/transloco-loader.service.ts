import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Translation, TranslocoLoader } from '@jsverse/transloco';
import { Observable, catchError } from 'rxjs';
import { posEnvironmentCore } from '../../../app-core/domain/environments';
@Injectable({ providedIn: 'root' })
export class TranslocoHttpLoader implements TranslocoLoader {
  constructor(private http: HttpClient) {}

  getTranslation(lang: string): Observable<Translation> {
    return this.http
      .get<Translation>(`${posEnvironmentCore.i18nUrl}${lang}.json`)
      .pipe(
        catchError((e: any) => {
          console.error('translation service status: ', e.status);
          return this.getTranslationBackup(lang);
        })
      );
  }

  private getTranslationBackup(lang: string): Observable<Translation> {
    return this.http.get<Translation>(`/assets/i18n/${lang}.json`);
  }
}
