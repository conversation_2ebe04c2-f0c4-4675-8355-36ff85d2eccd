import { TestBed } from '@angular/core/testing';
import { DialogService, DialogRef } from '@ngneat/dialog';
import { AplazoPopupDialogService } from 'src/app/modules/shared/popup/infra/components/aplazo-popup/aplazo-popup-dialog.service';
import { AplazoPopupComponent } from 'src/app/modules/shared/popup/infra/components/aplazo-popup/aplazo-popup.component';
import { StoreService } from 'src/app/core/application/services/store.service';
import { PopupRepository } from 'src/app/modules/shared/popup/infra/repositories/popup.repository';
import { of } from 'rxjs';

class DialogServiceMock {
  open = jasmine.createSpy('open').and.returnValue({
    afterClosed$: of(undefined),
  });
}

describe('AplazoPopupDialogService', () => {
  let service: AplazoPopupDialogService;
  let dialogService: DialogServiceMock;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        AplazoPopupDialogService,
        { provide: DialogService, useClass: DialogServiceMock },
        {
          provide: StoreService,
          useValue: { selectedBranch$: of({ id: 'BRANCH-1' }) },
        },
        {
          provide: PopupRepository,
          useValue: {
            getAvailablePopups: () =>
              of([
                { popupId: 'POPUP-001', startAt: '', endAt: '', priority: 1 },
              ]),
            getPopupHtml: () => of('<h2>HTML</h2>'),
          },
        },
      ],
      imports: [AplazoPopupComponent],
    });
    service = TestBed.inject(AplazoPopupDialogService);
    dialogService = TestBed.inject(DialogService) as any;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should open dialog with AplazoPopupComponent using openPopup', () => {
    const htmlContent = '<h2>Test HTML</h2>';
    service.openPopup(htmlContent);

    expect(dialogService.open).toHaveBeenCalledWith(
      AplazoPopupComponent,
      jasmine.objectContaining({
        data: { htmlContent },
        maxWidth: '600px',
        enableClose: true,
      })
    );
  });

  it('should return observable from openPopup', () => {
    const htmlContent = '<h2>Test HTML</h2>';
    const result = service.openPopup(htmlContent);

    expect(result).toBeDefined();
    result.subscribe(value => {
      expect(value).toBeUndefined();
    });
  });

  it('should show deprecated warning when using openPopupForBranch', () => {
    spyOn(console, 'warn');
    service.openPopupForBranch();

    expect(console.warn).toHaveBeenCalledWith(
      'openPopupForBranch is deprecated. Use openPopup(htmlContent) instead.'
    );
  });
});
