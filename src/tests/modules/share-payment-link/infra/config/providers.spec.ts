import {
  HttpClientTestingModule,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import {
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import {
  POS_ENVIRONMENT_CORE,
  posEnvironmentCore,
} from '../../../../../app-core/domain/environments';
import { RequestOTPUseCase } from '../../../../../app/modules/share-payment-link/no-internet-purchase/application/request-otp.usecase';
import { NoInternetPurchaseRepository } from '../../../../../app/modules/share-payment-link/no-internet-purchase/domain/repositories/no-internet-purchase.repository';
import { provideNoInternetPurchase } from '../../../../../app/modules/share-payment-link/no-internet-purchase/infra/config/providers';

describe('provideNoInternetPurchase', () => {
  let repository: NoInternetPurchaseRepository;
  let usecase: RequestOTPUseCase;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        provideNoInternetPurchase(),
        provideHttpClientTesting(),
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: POS_ENVIRONMENT_CORE,
          useValue: posEnvironmentCore,
        },
      ],
    });

    repository = TestBed.inject(NoInternetPurchaseRepository);
    usecase = TestBed.inject(RequestOTPUseCase);
  });

  it('should provide NoInternetPurchaseRepository', () => {
    expect(repository).toBeTruthy();
  });

  it('should provide RequestOTPUseCase', () => {
    expect(usecase).toBeTruthy();
  });
});
