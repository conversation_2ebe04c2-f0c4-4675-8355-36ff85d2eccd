import {
  HttpErrorResponse,
  HttpEvent,
  HttpHandler,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { ObservabilityService } from '@aplazo/front-observability';
import { RedirectionService } from '@aplazo/merchant/shared';
import { Observable, catchError } from 'rxjs';
import { StoreService } from '../application/services/store.service';
import { ROUTE_CONFIG } from '../domain/config/app-routes.core';

export const ERROR_CODE_MSG: any = {
  403: 'Credenciales inválidas',
  'AU-001': 'Expiró la sesión',
  'OR-001': 'No ha seleccionado una tienda',
  'OR-002': 'Orden no encontrada',
  'OR-003': 'Loan no encontrada',
  'OR-004': 'Orden en estado Activo, no puede ser eliminada',
  'OR-005': 'Monto de Orden Inferior',
  'OR-006': 'ID del Asesor de Ventas no es valido',
  'OR-009':
    'Lo sentimos . Estamos teniendo intermitencias en nuestro sistema por favor contacta a nuestro equipo de soporte',
};

@Injectable({ providedIn: 'root' })
export class GlobalErrorHandlerInterceptor implements HttpInterceptor {
  readonly #observability = inject(ObservabilityService);

  constructor(
    private storeService: StoreService,
    private redirectionService: RedirectionService
  ) {}

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    const isUntrackedFileByExtension = request.url.match(/\.svg$|\.json$/);

    if (isUntrackedFileByExtension) {
      return next.handle(request);
    }

    return next.handle(request).pipe(
      catchError((httpError: HttpErrorResponse) => {
        this.#observability.sendCustomLog({
          message: `http error response >> code: ${httpError.status} >> msg: ${httpError.message} >> text: ${httpError.statusText} >> url: ${httpError.url}`,
          status: 'warn',
        });

        let errorMsg = '';

        const error: {
          msg: string;
          code: string;
          status?: string;
          codeStatus?: number;
        } = httpError.error;

        if (!(error instanceof ProgressEvent)) {
          console.log(error);
          console.log('error instance of ErrorEvent', error.code);
        }
        if (error instanceof ErrorEvent) {
          console.log('this is client side error');
          errorMsg = `Error: ${error.message}`;
        } else if (error && error.code) {
          console.log('this is server side error');
          //notify
          errorMsg =
            ERROR_CODE_MSG[error.code] || 'Ha ocurrido un error inesperado.';
          //special case
          if (error.code === 'AU-001') {
            //session probably expired.
            this.storeService.clearStore();

            this.redirectionService.internalNavigation([
              ROUTE_CONFIG.authentication,
            ]);
          }
          throw httpError;
        } else if (error && (error.status || error.codeStatus)) {
          throw error;
        }

        errorMsg =
          ERROR_CODE_MSG[httpError.status] ||
          'Ha ocurrido un error inesperado.';

        console.warn(errorMsg);
        throw httpError;
      })
    );
  }
}
