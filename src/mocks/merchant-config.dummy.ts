import { UpdateMerchantConfigResponse } from '../app/core/domain/dtos';

export const merchantConfigDummy: UpdateMerchantConfigResponse = {
  id: 54,
  createdAt: '2022-07-28T16:53:08.411389Z',
  updatedAt: '2022-08-01T15:09:13.216784425Z',
  DeletedAt: null,
  merchantId: 12,
  logoUrl: '',
  isBranchCreationEnable: true,
  branches: [
    {
      id: 173,
      createdAt: '2022-08-01T14:53:11.464592Z',
      updatedAt: '2022-08-01T14:53:11.464592Z',
      DeletedAt: null,
      name: 'test 1',
      banned: false,
      merchantConfigId: 54,
      shareLinkFlags: {
        id: 117,
        createdAt: '2022-08-01T14:53:11.470821Z',
        updatedAt: '2022-08-01T14:53:11.470821Z',
        DeletedAt: null,
        isQrAllowed: true,
        isWhatsappAllowed: true,
        isSMSAllowed: true,
        branchId: 173,
      },
    },
    // {
    //   id: 174,
    //   createdAt: '2022-08-01T15:09:13.21943903Z',
    //   updatedAt: '2022-08-01T15:09:13.21943903Z',
    //   DeletedAt: null,
    //   name: 'test 2',
    //   banned: false,
    //   merchantConfigId: 54,
    //   shareLinkFlags: {
    //     id: 118,
    //     createdAt: '2022-08-01T15:09:13.224044957Z',
    //     updatedAt: '2022-08-01T15:09:13.224044957Z',
    //     DeletedAt: null,
    //     isQrAllowed: true,
    //     isWhatsappAllowed: true,
    //     isSMSAllowed: true,
    //     branchId: 174,
    //   },
    // },
  ],
  minimumOrderAmount: 100,
};
export const configWithBranchesAndCreationFlagDisabled: UpdateMerchantConfigResponse =
  {
    id: 54,
    createdAt: '2022-07-28T16:53:08.411389Z',
    updatedAt: '2022-08-01T15:09:13.216784425Z',
    DeletedAt: null,
    merchantId: 12,
    logoUrl: '',
    isBranchCreationEnable: false,
    branches: [
      {
        id: 173,
        createdAt: '2022-08-01T14:53:11.464592Z',
        updatedAt: '2022-08-01T14:53:11.464592Z',
        DeletedAt: null,
        name: 'test 1',
        banned: false,
        merchantConfigId: 54,
        shareLinkFlags: {
          id: 117,
          createdAt: '2022-08-01T14:53:11.470821Z',
          updatedAt: '2022-08-01T14:53:11.470821Z',
          DeletedAt: null,
          isQrAllowed: true,
          isWhatsappAllowed: true,
          isSMSAllowed: true,
          branchId: 173,
        },
      },
    ],
    minimumOrderAmount: 100,
  };
export const configWithoutBranchesAndCreationFlagDisabled: UpdateMerchantConfigResponse =
  {
    id: 54,
    createdAt: '2022-07-28T16:53:08.411389Z',
    updatedAt: '2022-08-01T15:09:13.216784425Z',
    DeletedAt: null,
    merchantId: 12,
    logoUrl: '',
    isBranchCreationEnable: false,
    branches: [],
    minimumOrderAmount: 100,
  };
