export enum DataFoundationOrigin {
  POSUI = 'posui',
  POSUI_LITE = 'posui-lite',
}

export type LoginEventDataFoundation = {
  email: string;
  success: boolean;
};

export type StoreFrontEventDataFoundation = {
  storeFrontId: number;
  autoSelect: boolean;
};

export type NewOrderEventDataFoundation = {
  storeFrontId: number;
};

export type AddProductEventDataFoundation = {
  id: string;
  name: string;
  price: number;
  qty: number;
};

export type ShareByQrEventDataFoundation = CancelOrderEventDataFoundation;

export type ShareOrderEventDataFoundation = CancelOrderEventDataFoundation;

export type CancelOrderEventDataFoundation = {
  order_id: number;
};
