import { Route } from '@angular/router';
import { NewBranchAddUseCase } from './application/new-branch-add.usecase';
import { NewBranchLogoutUseCase } from './application/new-branch-logout.usecase';
import { NewBranchComponent } from './new-branch.component';

const newBranchRoutes: Route[] = [
  {
    path: '',
    providers: [NewBranchLogoutUseCase, NewBranchAddUseCase],
    component: NewBranchComponent,
  },
];

export default newBranchRoutes;
