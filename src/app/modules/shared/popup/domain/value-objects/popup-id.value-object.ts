/**
 * PopupId value object.
 * Represents a unique identifier for popups with validation and immutability.
 */
export class PopupId {
  constructor(private readonly value: string) {
    if (!this.isValid(value)) {
      throw new Error('Invalid PopupId: ID must be a non-empty string');
    }
  }

  /**
   * Gets the string value of the ID.
   * @returns The ID as a string
   */
  getValue(): string {
    return this.value;
  }

  /**
   * Checks if this ID equals another ID.
   * @param other - The other PopupId to compare with
   * @returns True if the IDs are equal, false otherwise
   */
  equals(other: PopupId): boolean {
    return this.value === other.value;
  }

  /**
   * Creates a string representation of the ID.
   * @returns The ID as a string
   */
  toString(): string {
    return this.value;
  }

  /**
   * Validates if the provided value is a valid popup ID.
   * @param value - The value to validate
   * @returns True if the value is valid, false otherwise
   */
  private isValid(value: any): boolean {
    return typeof value === 'string' && value.trim().length > 0;
  }

  /**
   * Creates a PopupId from a string value.
   * @param value - The string value to create the ID from
   * @returns A new PopupId instance
   */
  static fromString(value: string): PopupId {
    return new PopupId(value);
  }

  /**
   * Creates a random PopupId.
   * @returns A new PopupId with a random UUID
   */
  static random(): PopupId {
    const randomId = crypto.randomUUID();
    return new PopupId(randomId);
  }
}
