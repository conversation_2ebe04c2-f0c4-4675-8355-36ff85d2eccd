.image-container {
  display: inline-flex;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;

  img {
    max-width: 100%;
    object-fit: contain;
  }
}

.divider {
  position: relative;
  display: inline-block;
  width: 1rem;
  height: 1rem;

  &::before {
    content: '';
    position: absolute;
    top: 15%;
    left: 50%;
    width: 2px;
    height: 100%;
    background-color: var(--color-gray-2);
  }
}
