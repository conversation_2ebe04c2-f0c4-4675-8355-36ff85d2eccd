import { TestBed } from '@angular/core/testing';
import {
  BrowserSessionStorageService,
  JwtDecoderService,
} from '@aplazo/merchant/shared';
import { LocalJwtDecoder } from '@aplazo/merchant/shared-testing';
import { POS_ENVIRONMENT_CORE } from '../../../app-core/domain/environments';
import { MerchantConfig } from '../../../app/core/domain/merchant-config';
import { GeneralStoreService } from '../../../app/core/services/store.service';
import configData from '../merchant-config.db.json';

describe('StoreService', () => {
  let service: GeneralStoreService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        GeneralStoreService,
        {
          provide: JwtDecoderService,
          useClass: LocalJwtDecoder,
        },
        {
          provide: POS_ENVIRONMENT_CORE,
          useValue: {
            APP_NAME: 'test',
          },
        },
        {
          provide: BrowserSessionStorageService,
          useValue: {
            getItem: () => void 0,
            setItem: () => void 0,
            removeItem: () => void 0,
            clearStorage: () => void 0,
          },
        },
      ],
    });

    service = TestBed.inject(GeneralStoreService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(GeneralStoreService);
  });

  it('should set and get token', () => {
    const token = 'test-token';

    service.setToken(token);

    expect(service.getToken()).toEqual(token);
  });

  it('should remove token if set to null', () => {
    service.setToken('test-token');

    expect(service.getToken()).toEqual('test-token');

    const token: string | null = null;

    service.setToken(token);

    expect(service.getToken()).toBeNull();
  });

  it('should set and get merchant config', done => {
    const config: MerchantConfig = configData.content;

    let result: any = null;

    service.merchantConfig$.subscribe(data => {
      result = data;
    });

    service.setMerchantConfig(config);

    expect(result).toEqual(config);

    done();
  });
});
