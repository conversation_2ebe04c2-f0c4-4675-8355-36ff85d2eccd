import { inject, Injectable } from '@angular/core';
import { BaseUsecase, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import { catchError, map, Observable, of, take } from 'rxjs';
import { ContestRegionUI } from '../../domain/contest';
import { ContestRepository } from '../../domain/repositories/contest.repository';

@Injectable()
export class GetAllContestsRegions
  implements BaseUsecase<any, Observable<ContestRegionUI>>
{
  readonly #repository = inject(ContestRepository);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(): Observable<ContestRegionUI> {
    return this.#repository.retrieveRegions().pipe(
      map(response =>
        response.reduce((acc, region) => {
          if (!acc[region.state_c_code]) {
            acc[region.state_c_code] = region;
          }
          return acc;
        }, {} as ContestRegionUI)
      ),
      catchError(e => this.#errorHandler.handle(e, of({} as ContestRegionUI))),
      take(1)
    );
  }
}
