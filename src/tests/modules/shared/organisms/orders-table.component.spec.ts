import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Ng<PERSON><PERSON>C<PERSON>,
  NgSwitchDefault,
} from '@angular/common';
import { TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { AplazoDynamicPipe, AplazoMatchMediaService } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoDropdownComponents } from '@aplazo/shared-ui/dropdown';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoLoanStatusLabelColorDirective,
  AplazoLoanStatusMapperPipe,
} from '@aplazo/shared-ui/merchant';
import {
  AplazoSimpleTableComponent,
  AplazoSimpleTableComponents,
} from '@aplazo/shared-ui/simple-table';
import { lastValueFrom, of, take } from 'rxjs';
import { StoreService } from '../../../../app/core/application/services/store.service';
import { OrdersTableComponent } from '../../../../app/modules/shared/organisms/orders-table/orders-table.component';
import i18N from '../../../../assets/i18n/orders/es.json';

const setup = (args: { shareLinkEnabled: boolean; isLargeScreen: boolean }) => {
  TestBed.configureTestingModule({
    imports: [
      AplazoSimpleTableComponents,
      AplazoButtonComponent,
      AplazoIconComponent,
      AplazoCardComponent,
      AplazoDropdownComponents,
      AplazoLoanStatusLabelColorDirective,
      AplazoLoanStatusMapperPipe,
      AplazoDynamicPipe,
      NgFor,
      NgIf,
      NgSwitch,
      NgSwitchCase,
      NgSwitchDefault,
      DatePipe,
      AsyncPipe,
      NgClass,
    ],
    providers: [
      {
        provide: StoreService,
        useValue: {
          hasBranchShareLinkEnabled$: of(args.shareLinkEnabled),
        },
      },
      {
        provide: AplazoMatchMediaService,
        useValue: {
          matchLgScreen$: of({ matches: args.isLargeScreen }),
        },
      },
      AplazoIconRegistryService,
    ],
  });

  const fixture = TestBed.createComponent(OrdersTableComponent);
  const component = fixture.componentInstance;

  component.i18text = i18N.table;
  fixture.detectChanges();

  return { fixture, component };
};

describe('OrdersTableComponent', () => {
  const pendingOrder = {
    id: 32382,
    createdAt: '2024-07-04T15:21:20.58555Z',
    updatedAt: '2024-07-04T15:21:30.751371Z',
    DeletedAt: null,
    date: '2024-07-04 15:21:20.583528741 +0000 UTC m=+251412.994677032',
    status: 'Created',
    price: 200,
    url: 'https://checkout-offline.aplazo.net/main/60deac2b-018a-46eb-bbe9-52d4264a6878',
    loanId: 118927,
    merchantId: 199,
    products: [],
    branchId: 268,
    sellsAgentId: 0,
    isWalmartOrder: false,
  };

  it('should be created', () => {
    const { component } = setup({
      shareLinkEnabled: true,
      isLargeScreen: true,
    });

    expect(component).toBeTruthy();
  });

  it('should get empty orders', async () => {
    const { component } = setup({
      shareLinkEnabled: true,
      isLargeScreen: true,
    });

    const result = await lastValueFrom(component.orders$.pipe(take(1)));

    expect(result).toEqual([]);
  });

  it('should not render anything when i18text is not provided', () => {
    const { fixture, component } = setup({
      shareLinkEnabled: true,
      isLargeScreen: true,
    });

    component.i18text = null;
    fixture.detectChanges();

    const compiled = fixture.nativeElement as HTMLElement;

    expect(compiled?.children?.length).toEqual(0);
  });

  it('should render a table without body when orders are empty', () => {
    const { fixture } = setup({
      shareLinkEnabled: true,
      isLargeScreen: true,
    });

    const table = fixture.debugElement.query(
      By.directive(AplazoSimpleTableComponent)
    );

    const tableBodyRows = table.queryAll(
      By.css('.aplazo-simple-table__body-row')
    );

    expect(table).toBeTruthy();
    expect(tableBodyRows).toEqual([]);
  });

  it('should render a table with orders', () => {
    const { fixture, component } = setup({
      shareLinkEnabled: true,
      isLargeScreen: true,
    });

    component.orders = [pendingOrder];
    fixture.detectChanges();

    const table = fixture.debugElement.query(
      By.directive(AplazoSimpleTableComponent)
    );

    const tableBodyRows = table.queryAll(
      By.css('.aplazo-simple-table__body-row')
    );

    expect(table).toBeTruthy();
    expect(tableBodyRows.length).toEqual(1);
  });

  it('should emit cancelOrder event', async () => {
    const { fixture, component } = setup({
      shareLinkEnabled: true,
      isLargeScreen: true,
    });
    const cancelOrderSpy = spyOn(
      component.cancelOrder,
      'emit'
    ).and.callThrough();

    component.cancel(pendingOrder);
    fixture.detectChanges();

    expect(cancelOrderSpy).toHaveBeenCalledTimes(1);
    expect(cancelOrderSpy).toHaveBeenCalledWith(pendingOrder);
  });

  it('should emit checkStatus event', async () => {
    const { fixture, component } = setup({
      shareLinkEnabled: true,
      isLargeScreen: true,
    });
    const checkStatusSpy = spyOn(
      component.checkOrderStatus,
      'emit'
    ).and.callThrough();

    component.orders = [pendingOrder];
    fixture.detectChanges();

    component.checkStatus(pendingOrder);
    fixture.detectChanges();

    expect(checkStatusSpy).toHaveBeenCalledTimes(1);
    expect(checkStatusSpy).toHaveBeenCalledWith(pendingOrder);
  });

  it('should emit shareOrder event', async () => {
    const { fixture, component } = setup({
      shareLinkEnabled: true,
      isLargeScreen: true,
    });
    const shareSpy = spyOn(component.shareOrder, 'emit').and.callThrough();

    component.share(pendingOrder);
    fixture.detectChanges();

    expect(shareSpy).toHaveBeenCalledTimes(1);
    expect(shareSpy).toHaveBeenCalledWith(pendingOrder);
  });

  it('should emit showRefundOrder event', () => {
    const { fixture, component } = setup({
      shareLinkEnabled: true,
      isLargeScreen: true,
    });
    const showRefundSpy = spyOn(
      component.showRefundInfo,
      'emit'
    ).and.callThrough();
    const wmOrder = {
      ...pendingOrder,
      isWalmartOrder: true,
    };

    component.orders = [wmOrder];
    fixture.detectChanges();

    component.showWMrefundInfo(wmOrder);
    fixture.detectChanges();

    expect(showRefundSpy).toHaveBeenCalledTimes(1);
    expect(showRefundSpy).toHaveBeenCalledWith(wmOrder);
  });
});
