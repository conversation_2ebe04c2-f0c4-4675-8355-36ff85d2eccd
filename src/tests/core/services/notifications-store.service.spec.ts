import { TestBed } from '@angular/core/testing';
import { BrowserLocalStorageService } from '@aplazo/merchant/shared';
import { INotificationsWithNewOnesUIDto } from '../../../app/core/domain/notification';
import { NotificationsStore } from '../../../app/core/services/notifications-store.service';

describe('NotificationsStore', () => {
  let service: NotificationsStore;
  let localStorageMock: jasmine.SpyObj<BrowserLocalStorageService>;

  beforeEach(() => {
    localStorageMock = jasmine.createSpyObj('BrowserLocalStorageService', [
      'getItem',
      'setItem',
    ]);

    TestBed.configureTestingModule({
      providers: [
        NotificationsStore,
        { provide: BrowserLocalStorageService, useValue: localStorageMock },
      ],
    });

    service = TestBed.inject(NotificationsStore);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(NotificationsStore);
  });

  it('should set notifications correctly', done => {
    const mockNotifications: INotificationsWithNewOnesUIDto[] = [
      {
        type: 'PROMO',
        hasNewOnes: true,
        newOnesCounter: 2,
        newOnesIds: [1, 2],
        notifications: new Map(),
      },
    ];

    service.setNotifications(mockNotifications);

    service.hasNewNotifications$().subscribe(hasNew => {
      expect(hasNew).toBeTruthy();
      done();
    });
  });

  it('should count new notifications correctly', done => {
    const mockNotifications: INotificationsWithNewOnesUIDto[] = [
      {
        type: 'PROMO',
        hasNewOnes: true,
        newOnesCounter: 2,
        newOnesIds: [1, 2],
        notifications: new Map(),
      },
      {
        type: 'INFO',
        hasNewOnes: true,
        newOnesCounter: 3,
        newOnesIds: [3, 4, 5],
        notifications: new Map(),
      },
    ];

    service.setNotifications(mockNotifications);

    service.newNotificationsCounter$().subscribe(count => {
      expect(count).toBe(5);
      done();
    });
  });

  it('should get notifications by type correctly', done => {
    const mockNotifications = new Map([['today', []]]);
    const mockData: INotificationsWithNewOnesUIDto[] = [
      {
        type: 'PROMO',
        hasNewOnes: true,
        newOnesCounter: 2,
        newOnesIds: [1, 2],
        notifications: mockNotifications,
      },
    ];

    service.setNotifications(mockData);

    service.getNotificationsByType$('PROMO').subscribe(notifications => {
      expect(notifications).toEqual(mockNotifications);
      done();
    });
  });

  it('should mark all notifications as read', () => {
    localStorageMock.getItem.and.returnValue('{}');

    const mockNotifications: INotificationsWithNewOnesUIDto[] = [
      {
        type: 'PROMO',
        hasNewOnes: true,
        newOnesCounter: 2,
        newOnesIds: [1, 2],
        notifications: new Map(),
      },
    ];

    service.setNotifications(mockNotifications);
    service.markAllAsRead('PROMO');

    service.newNotificationsByTypeCounter$().subscribe(notifications => {
      const promoNotification = notifications.find(n => n?.type === 'PROMO');
      expect(promoNotification?.counter).toBe(0);
      expect(promoNotification?.ids).toEqual([]);
    });

    expect(localStorageMock.setItem).toHaveBeenCalled();
  });

  it('should handle empty notifications array', done => {
    service.setNotifications([]);

    service.hasNewNotifications$().subscribe(hasNew => {
      expect(hasNew).toBeFalsy();
      done();
    });
  });
});
