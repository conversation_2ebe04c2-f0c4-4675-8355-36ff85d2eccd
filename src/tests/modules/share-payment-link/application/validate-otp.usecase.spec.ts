import { HttpErrorResponse } from '@angular/common/http';
import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { TransactionBehaviorService } from '@aplazo/front-analytics/kount';
import {
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { NoInternetPurchaseRepository } from 'src/app/modules/share-payment-link/no-internet-purchase/domain/repositories/no-internet-purchase.repository';
import { ValidateOTPUseCase } from '../../../../app/modules/share-payment-link/no-internet-purchase/application/validate-otp.usecase';

describe('ValidateOTPUseCase', () => {
  let usecase: ValidateOTPUseCase;
  let repository: jasmine.SpyObj<NoInternetPurchaseRepository>;
  let successNotifier: jasmine.Spy;
  let errorHandler: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        ValidateOTPUseCase,
        {
          provide: NoInternetPurchaseRepository,
          useValue: jasmine.createSpyObj('NoInternetPurchaseRepository', [
            'validateOTP',
          ]),
        },
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: TransactionBehaviorService,
          useValue: {
            sessionId: '1234test',
          },
        },
      ],
    });

    usecase = TestBed.inject(ValidateOTPUseCase);
    repository = TestBed.inject(
      NoInternetPurchaseRepository
    ) as jasmine.SpyObj<NoInternetPurchaseRepository>;

    successNotifier = spyOn(
      TestBed.inject(NotifierService),
      'success'
    ).and.callThrough();
    errorHandler = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(ValidateOTPUseCase);
  });

  it('should throw an error if the phone number is invalid', fakeAsync(() => {
    let result: any;

    usecase
      .execute({
        phoneNumber: 'a123',
        loanId: 1,
        code: '1234',
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe('Guard::againstInvalidNumbers');
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.validateOTP).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error if the loan id is invalid', fakeAsync(() => {
    let result: any;

    usecase
      .execute({
        phoneNumber: '1234567890',
        // @ts-expect-error: testing invalid loan id
        loanId: 'a',
        code: '1234',
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe('Guard::againstInvalidNumbers');
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.validateOTP).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error if the code is empty', fakeAsync(() => {
    let result: any;

    usecase
      .execute({
        phoneNumber: '1234567890',
        loanId: 1,
        code: '',
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe('ValidateOTPUseCase::otp::empty');
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.validateOTP).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error if the phone number length is invalid', fakeAsync(() => {
    let result: any;

    usecase
      .execute({
        phoneNumber: '123456789',
        loanId: 1,
        code: '1234',
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe('ValidateOTPUseCase::phoneNumber::invalidLength');
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.validateOTP).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
  }));

  it('should throw RuntimeMerchantError when repository fails with controlled error APZ_OCL_0004', fakeAsync(() => {
    repository.validateOTP.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 400,
            error: {
              code: 'APZ_OCL_0004',
            },
          })
      )
    );

    let result: any;

    usecase
      .execute({
        phoneNumber: '1234567890',
        loanId: 1,
        code: '1234',
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe('ValidateOTPUseCase::ApzOcl::APZ_OCL_0004');
    expect(repository.validateOTP).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when repository fails with an unknown error', fakeAsync(() => {
    repository.validateOTP.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
          })
      )
    );

    let result: any;

    usecase
      .execute({
        phoneNumber: '1234567890',
        loanId: 1,
        code: '1234',
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.validateOTP).toHaveBeenCalledTimes(1);
    expect(successNotifier).toHaveBeenCalledTimes(0);
  }));

  it('should notify success when repository succeeds', fakeAsync(() => {
    repository.validateOTP.and.returnValue(of(void 0));

    let result: any;

    usecase
      .execute({
        phoneNumber: '1234567890',
        loanId: 1,
        code: '1234',
      })
      .subscribe({
        next: res => {
          result = res;
        },
        error: fail,
      });

    tick();

    expect(result).toBeUndefined();
    expect(successNotifier).toHaveBeenCalledTimes(1);
    expect(repository.validateOTP).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(0);
  }));
});
