import { Component, inject, signal, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { PopupTriggerService } from '../application/services/popup-trigger.service';
import { PopupStorageService } from '../application/services/popup-storage.service';
import { PopupRepository } from '../infra/repositories/popup.repository';
import { StoreService } from '../../../../core/application/services/store.service';
import { firstValueFrom, Subscription } from 'rxjs';

@Component({
  selector: 'app-popup-demo',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    AplazoButtonComponent,
    AplazoCardComponent,
  ],
  styles: [
    `
      .popup-demo-widget {
        position: fixed;
        bottom: 24px;
        right: 24px;
        z-index: 9999;
        max-width: 520px;
        min-width: 420px;
        max-height: 80vh;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.18);
        border-radius: 16px;
        background: white;
        transition: box-shadow 0.2s;
        overflow: auto;
      }
      .popup-demo-toggle-btn {
        position: fixed;
        bottom: 24px;
        right: 24px;
        z-index: 10000;
        border-radius: 50%;
        width: 56px;
        height: 56px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        cursor: pointer;
        border: none;
        outline: none;
        transition: background 0.2s;
      }
      .popup-demo-toggle-btn:hover {
        background: #f3f3f3;
      }
      .popup-demo-close-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        background: transparent;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #888;
      }
      .popup-demo-content {
        max-height: calc(80vh - 32px);
        overflow-y: auto;
        padding: 16px;
      }
      .popup-demo-textarea {
        width: 100%;
        min-height: 120px;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
        resize: vertical;
      }
      .popup-demo-section {
        margin-bottom: 16px;
      }
      .popup-demo-section:last-child {
        margin-bottom: 0;
      }
      .popup-demo-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
      }
      @media (max-width: 768px) {
        .popup-demo-widget {
          max-width: calc(100vw - 48px);
          min-width: 320px;
          right: 12px;
          bottom: 12px;
        }
        .popup-demo-toggle-btn {
          right: 12px;
          bottom: 12px;
        }
        .popup-demo-grid {
          grid-template-columns: 1fr;
        }
      }
    `,
  ],
  template: `
    <button
      *ngIf="!isOpen()"
      class="popup-demo-toggle-btn"
      (click)="isOpen.set(true)">
      🧪
    </button>
    <div *ngIf="isOpen()" class="popup-demo-widget">
      <button
        class="popup-demo-close-btn"
        (click)="isOpen.set(false)"
        title="Cerrar">
        ×
      </button>
      <aplz-ui-card size="full">
        <div class="p-6 space-y-6">
          <h2 class="text-2xl font-bold text-gray-800">🎭 Popup System Demo</h2>

          <!-- Current State -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">📊 Current State</h3>
            <div class="grid grid-cols-2 gap-4 text-sm">
              <div><strong>Branch ID:</strong> {{ currentBranchId() }}</div>
              <div><strong>Today:</strong> {{ today() }}</div>
              <div>
                <strong>Available Popups:</strong>
                {{ availablePopups().length }}
              </div>
              <div><strong>Shown Today:</strong> {{ shownToday() }}</div>
            </div>
          </div>

          <!-- Configuration -->
          <div class="bg-blue-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">⚙️ Configuration</h3>
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium mb-1">Branch ID:</label>
                <input
                  type="text"
                  [(ngModel)]="config.branchId"
                  class="w-full px-3 py-2 border rounded-md"
                  placeholder="e.g., 36" />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1"
                  >Trigger Type:</label
                >
                <select
                  [(ngModel)]="config.triggerType"
                  class="w-full px-3 py-2 border rounded-md">
                  <option value="manual">Manual</option>
                  <option value="login">After Login</option>
                  <option value="payment">After Payment</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium mb-1"
                  >Popup Width (px):</label
                >
                <input
                  type="number"
                  [(ngModel)]="config.popupWidth"
                  class="w-full px-3 py-2 border rounded-md"
                  min="300"
                  max="1600"
                  step="10"
                  placeholder="900" />
              </div>
              <div>
                <label class="block text-sm font-medium mb-1"
                  >Popup Height (px):</label
                >
                <input
                  type="number"
                  [(ngModel)]="config.popupHeight"
                  class="w-full px-3 py-2 border rounded-md"
                  min="200"
                  max="1200"
                  step="10"
                  placeholder="auto" />
              </div>
            </div>
            <div class="mt-4">
              <label class="block text-sm font-medium mb-1"
                >Custom HTML (solo manual):</label
              >
              <textarea
                [(ngModel)]="config.customHtml"
                rows="4"
                class="w-full px-3 py-2 border rounded-md font-mono text-xs"></textarea>
            </div>
          </div>

          <!-- Actions -->
          <div class="bg-green-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">🎯 Actions</h3>
            <div class="flex flex-wrap gap-3">
              <button
                aplzButton
                aplzAppearance="solid"
                aplzColor="aplazo"
                (click)="triggerPopup()"
                [disabled]="isLoading()">
                {{ isLoading() ? 'Loading...' : '🚀 Trigger Popup' }}
              </button>

              <button
                aplzButton
                aplzAppearance="stroked"
                aplzColor="dark"
                (click)="checkPopupStatus()">
                🔍 Check Status
              </button>

              <button
                aplzButton
                aplzAppearance="stroked"
                aplzColor="warning"
                (click)="clearTodayRecords()">
                🗑️ Clear Today's Records
              </button>

              <button
                aplzButton
                aplzAppearance="stroked"
                aplzColor="danger"
                (click)="clearAllRecords()">
                🗑️ Clear All Records
              </button>
            </div>
          </div>

          <!-- ✅ ADDED: Debug Actions -->
          <div class="bg-orange-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">🐛 Debug Actions</h3>
            <div class="flex flex-wrap gap-3">
              <button
                aplzButton
                aplzAppearance="stroked"
                aplzColor="dark"
                (click)="debugPopupStatus()">
                🔍 Debug Status
              </button>

              <button
                aplzButton
                aplzAppearance="stroked"
                aplzColor="warning"
                (click)="clearPermanentDismiss()">
                🗑️ Clear Permanent Dismiss
              </button>
            </div>
          </div>

          <!-- Results -->
          <div class="bg-yellow-50 p-4 rounded-lg" *ngIf="lastResult()">
            <h3 class="text-lg font-semibold mb-3">📋 Last Result</h3>
            <pre class="text-sm bg-white p-3 rounded border overflow-auto">{{
              lastResult() | json
            }}</pre>
          </div>

          <!-- Available Popups -->
          <div
            class="bg-purple-50 p-4 rounded-lg"
            *ngIf="availablePopups().length > 0">
            <h3 class="text-lg font-semibold mb-3">📋 Available Popups</h3>
            <div class="space-y-2">
              <div
                *ngFor="let popup of availablePopups()"
                class="bg-white p-3 rounded border">
                <div class="flex justify-between items-center">
                  <div>
                    <strong>{{ popup.popupId }}</strong> (Priority:
                    {{ popup.priority }})
                  </div>
                  <div class="text-sm text-gray-600">
                    {{ popup.startAt }} - {{ popup.endAt }}
                  </div>
                </div>
                <!-- ✅ ADDED: Popup Status Details -->
                <div class="mt-2 text-xs space-y-1">
                  <div class="flex justify-between">
                    <span>Shown Today:</span>
                    <span
                      [class]="
                        isPopupShownToday(popup.popupId)
                          ? 'text-red-600 font-bold'
                          : 'text-green-600'
                      ">
                      {{ isPopupShownToday(popup.popupId) ? 'YES' : 'NO' }}
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span>Permanently Dismissed:</span>
                    <span
                      [class]="
                        isPopupPermanentlyDismissed(popup.popupId)
                          ? 'text-red-600 font-bold'
                          : 'text-green-600'
                      ">
                      {{
                        isPopupPermanentlyDismissed(popup.popupId)
                          ? 'YES'
                          : 'NO'
                      }}
                    </span>
                  </div>
                  <div class="flex justify-between">
                    <span>Can Show:</span>
                    <span
                      [class]="
                        canPopupShow(popup.popupId)
                          ? 'text-green-600 font-bold'
                          : 'text-red-600'
                      ">
                      {{ canPopupShow(popup.popupId) ? 'YES' : 'NO' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Storage Records -->
          <div class="bg-red-50 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-3">💾 Storage Records</h3>
            <div class="space-y-2 max-h-40 overflow-auto">
              <div
                *ngFor="let record of storageRecords()"
                class="bg-white p-2 rounded border text-sm">
                <strong>{{ record.popupId }}</strong> - Branch:
                {{ record.branchId }} - Date: {{ record.date }}
              </div>
              <div
                *ngIf="storageRecords().length === 0"
                class="text-gray-500 text-sm">
                No records found
              </div>
            </div>
          </div>
        </div>
      </aplz-ui-card>
    </div>
  `,
})
export class PopupDemoComponent implements OnDestroy {
  private popupTriggerService = inject(PopupTriggerService);
  public popupStorageService = inject(PopupStorageService);
  private popupRepository = inject(PopupRepository);
  private storeService = inject(StoreService);

  // Widget open/close state
  isOpen = signal(false);

  // Reactive state
  isLoading = signal(false);
  lastResult = signal<any>(null);
  currentBranchId = signal<string>('');
  today = signal<string>('');
  availablePopups = signal<any[]>([]);
  shownToday = signal<number>(0);
  storageRecords = signal<any[]>([]);

  // ✅ ADDED: Flag to prevent cycling
  private isInitializing = false;
  private branchSubscription?: Subscription;

  // Configuration
  config = {
    branchId: '36',
    triggerType: 'manual',
    customHtml: `
<div style="width: 900px; max-width: 98vw; min-width: 500px; background: #7eeaff; border-radius: 32px; box-shadow: 0 4px 24px rgba(0,0,0,0.18); padding: 32px 24px; min-height: 340px; font-family: 'Montserrat', Arial, sans-serif; display: flex;">
  <!-- Columna izquierda -->
  <div style="flex: 2; padding-right: 24px;">
    <div style="font-size: 2.1rem; font-weight: bold; margin-bottom: 12px; color: #111;">
      ¿Tienes problemas para generar el ticket? <br>¡Resuelve en segundos!
    </div>
    <div style="font-size: 1.15rem; margin-bottom: 10px;">
      📋 Sigue los pasos para reintentar el proceso de cobro en <b>POSUI</b>.
    </div>
    <div style="font-size: 1.15rem; margin-bottom: 10px;">
      🎥 Consulta el <b>video tutorial disponible</b> en la sección Aplazoversity.
    </div>
    <div style="font-size: 1.15rem; margin-bottom: 24px;">
      ✅ ¡Así evitas perder la venta!
    </div>
    <button style="background: #111; color: #fff; border: none; border-radius: 24px; padding: 12px 36px; font-size: 1.2rem; font-weight: bold; cursor: pointer; box-shadow: 0 2px 8px rgba(0,0,0,0.10);">
      Ver tutorial
    </button>
  </div>
  <!-- Columna derecha -->
  <div style="flex: 1; display: flex; align-items: center; justify-content: center;">
    <div style="width: 200px; height: 200px; background: #111; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #7eeaff; font-size: 4rem;">
      🎥
    </div>
  </div>
</div>`,
    popupWidth: 900,
    popupHeight: 340,
  };

  constructor() {
    this.initializeState();
  }

  ngOnDestroy() {
    if (this.branchSubscription) {
      this.branchSubscription.unsubscribe();
    }
  }

  private initializeState() {
    if (this.isInitializing) {
      return; // Prevent multiple initializations
    }

    this.isInitializing = true;
    this.today.set(new Date().toISOString().split('T')[0]);

    // Get current branch - use subscription to avoid cycling
    this.branchSubscription = this.storeService.selectedBranch$.subscribe(
      branch => {
        if (branch?.id) {
          const branchId = String(branch.id);
          this.currentBranchId.set(branchId);
          this.config.branchId = branchId;
          this.loadAvailablePopups();
        }
      }
    );

    this.loadStorageRecords();
    this.isInitializing = false;
  }

  private loadAvailablePopups() {
    if (this.config.branchId && !this.isInitializing) {
      const obs = this.popupRepository.getAvailablePopups(this.config.branchId);
      if (obs && typeof obs.subscribe === 'function') {
        obs.subscribe(popups => {
          this.availablePopups.set(popups);
          this.updateShownToday();
        });
      } else {
        // fallback para tests rotos
        this.availablePopups.set([]);
        this.updateShownToday();
      }
    }
  }

  private updateShownToday() {
    const today = this.today();
    const shownCount = this.availablePopups().filter(popup => {
      const wasShownToday = this.popupStorageService.wasPopupShownToday(
        popup.popupId,
        this.config.branchId,
        today
      );
      const isPermanentlyDismissed =
        this.popupStorageService.isPopupPermanentlyDismissed(
          popup.popupId,
          this.config.branchId
        );
      return wasShownToday || isPermanentlyDismissed;
    }).length;
    this.shownToday.set(shownCount);
  }

  private loadStorageRecords() {
    // Access public method for demo purposes
    const records = this.popupStorageService.getShownRecords();
    this.storageRecords.set(records || []);
  }

  async triggerPopup() {
    this.isLoading.set(true);
    try {
      let result: boolean;
      if (this.config.triggerType === 'manual' && this.config.customHtml) {
        // Mostrar popup con HTML personalizado
        const event = new CustomEvent('popup-demo-html', {
          detail: {
            html: this.config.customHtml,
            width: this.config.popupWidth,
            height: this.config.popupHeight,
          },
        });
        window.dispatchEvent(event);
        result = true;
      } else {
        switch (this.config.triggerType) {
          case 'login':
            result = await firstValueFrom(
              this.popupTriggerService.triggerAfterLogin(this.config.branchId)
            ).catch(() => false);
            break;
          case 'payment':
            result = await firstValueFrom(
              this.popupTriggerService.triggerAfterPaymentSuccess(
                this.config.branchId
              )
            ).catch(() => false);
            break;
          default:
            result = await firstValueFrom(
              this.popupTriggerService.triggerPopupIfNeeded({
                branchId: this.config.branchId,
              })
            ).catch(() => false);
        }
      }

      this.lastResult.set({
        success: result,
        timestamp: new Date().toISOString(),
        config: { ...this.config },
        triggerType: this.config.triggerType,
      });
      // Refresh state
      this.loadAvailablePopups();
      this.loadStorageRecords();
    } catch (error) {
      console.error('🔍 [PopupDemo] Error during trigger:', error);
      this.lastResult.set({
        error: error,
        timestamp: new Date().toISOString(),
        config: { ...this.config },
      });
    } finally {
      this.isLoading.set(false);
    }
  }

  // ✅ ADDED: Debug method to check popup status
  debugPopupStatus() {
    console.log('🔍 [PopupDemo] === DEBUG POPUP STATUS ===');
    console.log('  - Current branch ID:', this.config.branchId);
    console.log('  - Today:', this.today());
    console.log('  - Available popups:', this.availablePopups());

    this.availablePopups().forEach(popup => {
      const isPermanentlyDismissed =
        this.popupStorageService.isPopupPermanentlyDismissed(
          popup.popupId,
          this.config.branchId
        );
      const wasShownToday = this.popupStorageService.wasPopupShownToday(
        popup.popupId,
        this.config.branchId,
        this.today()
      );
      const shouldShow = !isPermanentlyDismissed && !wasShownToday;

      console.log(`  - Popup ${popup.popupId}:`, {
        priority: popup.priority,
        permanentlyDismissed: isPermanentlyDismissed,
        shownToday: wasShownToday,
        shouldShow: shouldShow,
      });
    });

    // Check storage records
    const records = this.popupStorageService.getShownRecords();
    console.log('  - Storage records:', records);

    // Test shouldShowPopup
    this.popupTriggerService
      .shouldShowPopup({
        branchId: this.config.branchId,
      })
      .subscribe(shouldShow => {
        console.log(
          '  - PopupTriggerService.shouldShowPopup result:',
          shouldShow
        );
      });
  }

  // ✅ ADDED: Clear permanent dismiss for testing
  clearPermanentDismiss() {
    this.availablePopups().forEach(popup => {
      this.popupStorageService.clearPermanentDismiss(
        popup.popupId,
        this.config.branchId
      );
    });
    this.loadStorageRecords();
    this.updateShownToday();

    this.lastResult.set({
      action: 'clear_permanent_dismiss',
      timestamp: new Date().toISOString(),
      message: 'Permanent dismiss cleared for all popups',
    });
  }

  checkPopupStatus() {
    this.popupTriggerService
      .shouldShowPopup({
        branchId: this.config.branchId,
      })
      .subscribe(shouldShow => {
        this.lastResult.set({
          shouldShow,
          timestamp: new Date().toISOString(),
          config: { ...this.config },
          action: 'status_check',
        });
      });
  }

  clearTodayRecords() {
    this.popupStorageService.clearRecordsForDate(this.today());
    this.loadStorageRecords();
    this.updateShownToday();

    this.lastResult.set({
      action: 'clear_today',
      timestamp: new Date().toISOString(),
      date: this.today(),
    });
  }

  clearAllRecords() {
    this.popupStorageService.clearAllRecords();
    this.loadStorageRecords();
    this.updateShownToday();

    this.lastResult.set({
      action: 'clear_all',
      timestamp: new Date().toISOString(),
    });
  }

  // ✅ ADDED: Public methods for template access
  public isPopupShownToday(popupId: string): boolean {
    return this.popupStorageService.wasPopupShownToday(
      popupId,
      this.config.branchId,
      this.today()
    );
  }

  public isPopupPermanentlyDismissed(popupId: string): boolean {
    return this.popupStorageService.isPopupPermanentlyDismissed(
      popupId,
      this.config.branchId
    );
  }

  public canPopupShow(popupId: string): boolean {
    return (
      !this.isPopupShownToday(popupId) &&
      !this.isPopupPermanentlyDismissed(popupId)
    );
  }
}
