import { TestBed } from '@angular/core/testing';
import {
  LoaderService,
  NotifierService,
  RedirectionService,
} from '@aplazo/merchant/shared';
import {
  LocalLoader,
  LocalNotifier,
  LocalRedirecter,
} from '@aplazo/merchant/shared-testing';
import { StoreService } from '../../../app/core/application/services/store.service';
import {
  POS_APP_ROUTES,
  ROUTE_CONFIG,
} from '../../../app/core/domain/config/app-routes.core';
import { MerchantsService } from '../../../app/core/services/merchants.service';
import { NewBranchAddUseCase } from '../../../app/modules/new-branch/application/new-branch-add.usecase';
import { LocalStore } from '../../core/services/impl/local-store.service';

describe('NewBranchAddUseCase', () => {
  let useCase: NewBranchAddUseCase;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      providers: [
        NewBranchAddUseCase,
        {
          provide: MerchantsService,
          useValue: jasmine.createSpyObj('MerchantsService', [
            'updateMerchantBranch',
          ]),
        },
        {
          provide: RedirectionService,
          useClass: LocalRedirecter,
        },
        {
          provide: NotifierService,
          useClass: LocalNotifier,
        },
        {
          provide: StoreService,
          useValue: LocalStore,
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: POS_APP_ROUTES,
          useValue: ROUTE_CONFIG,
        },
      ],
    });

    useCase = TestBed.inject(NewBranchAddUseCase);
    TestBed.inject(MerchantsService);
    TestBed.inject(StoreService);
    TestBed.inject(RedirectionService);
    TestBed.inject(NotifierService);
    TestBed.inject(POS_APP_ROUTES);
  });

  it('should be created', () => {
    expect(useCase).toBeTruthy();
  });

  // it("should call loader's show and hide method when useCase.execute is called", fakeAsync(() => {
  //   merchantServiceSpy.updateMerchantBranch.and.returnValue(
  //     of(merchantConfigDummy)
  //   );

  //   useCase.execute({ branches: [], LogoUrl: '' });

  //   flush();
  //   tick();

  //   expect(loaderServiceMock.show).toHaveBeenCalledTimes(1);
  //   expect(loaderServiceMock.hide).toHaveBeenCalledTimes(1);
  // }));

  // it('should call setSelectedBranch when updateMerchantConfig return branches', fakeAsync(() => {
  //   useCase.execute({ branches: [], LogoUrl: '' });

  //   flush();
  //   tick();

  //   expect(storeServiceMock.setSelectedBranch).toHaveBeenCalledTimes(1);
  // }));

  // it("should call notifier's success when updateMerchantConfig return branches", fakeAsync(() => {
  //   merchantServiceSpy.updateMerchantBranch.and.returnValue(
  //     of(merchantConfigDummy)
  //   );

  //   useCase.execute({ branches: [], LogoUrl: '' });

  //   flush();
  //   tick();

  //   expect(notifierServiceMock.success).toHaveBeenCalledTimes(1);
  //   expect(notifierServiceMock.success).toHaveBeenCalledWith({
  //     title: 'Tienda creada satisfactoriamente',
  //   });
  // }));
});
