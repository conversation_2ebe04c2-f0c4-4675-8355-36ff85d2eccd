import { DOCUMENT } from '@angular/common';
import { ApplicationRef } from '@angular/core';
import {
  discardPeriodicTasks,
  fakeAsync,
  flush,
  TestBed,
} from '@angular/core/testing';
import { SwUpdate } from '@angular/service-worker';
import { ToastrService } from 'ngx-toastr';
import { Observable, of, Subject, VirtualTimeScheduler } from 'rxjs';
import { CustomToastrComponent } from 'src/app/core/components/custom-toastr.component';
import {
  SERVICE_WORKER_UPDATER_CONFIG,
  ServiceWorkerUpdaterService,
} from '../../../app/core/services/service-worker-updater.service';

const setup = (args?: {
  config?: Partial<{
    pollInterval: number;
    dialogTitle: string;
    dialogMessage: string;
  }>;
  swUpdate?: Partial<{
    isEnabled: boolean;
    versionUpdates: Observable<any>;
    unrecoverable: Observable<any>;
    checkForUpdate: boolean;
  }>;
  appRef?: Partial<{
    isStable: Observable<boolean>;
  }>;
  caches?: {
    keys: () => Promise<string[]>;
    delete: () => Promise<boolean>;
  };
  toastrResult?: boolean | null | undefined;
}) => {
  const defaultConfig = {
    config: {},
    swUpdate: {
      isEnabled: true,
      versionUpdates: of({
        type: 'VERSION_READY',
      }),
      unrecoverable: new Subject<any>(),
      checkForUpdate: false,
    },
    appRef: {
      isStable: of(true),
    },
    caches: {
      keys: () => Promise.resolve([]),
      delete: () => Promise.resolve(true),
    },
    toastrResult: null,
  };
  const config = {
    config: args?.config ?? defaultConfig.config,
    swUpdate: {
      isEnabled: args?.swUpdate?.isEnabled ?? defaultConfig.swUpdate.isEnabled,
      versionUpdates:
        args?.swUpdate?.versionUpdates ?? defaultConfig.swUpdate.versionUpdates,
      unrecoverable:
        args?.swUpdate?.unrecoverable ?? defaultConfig.swUpdate.unrecoverable,
      checkForUpdate:
        args?.swUpdate?.checkForUpdate ?? defaultConfig.swUpdate.checkForUpdate,
    },
    appRef: args?.appRef ?? defaultConfig.appRef,
    document: {
      defaultView: {
        caches: {
          keys: args?.caches?.keys ?? defaultConfig.caches.keys,
          delete: args?.caches?.delete ?? defaultConfig.caches.delete,
        },
      },
    },
    toastrResult:
      args && 'toastrResult' in args && args.toastrResult != null
        ? args.toastrResult
        : defaultConfig.toastrResult,
  };

  TestBed.configureTestingModule({
    providers: [
      {
        provide: ToastrService,
        useValue:
          config.toastrResult == null
            ? null
            : {
                show: () => {
                  return {
                    onAction: of({
                      confirmation: config.toastrResult,
                    }),
                  };
                },
              },
      },
      {
        provide: SERVICE_WORKER_UPDATER_CONFIG,
        useValue: config.config,
      },
      {
        provide: SwUpdate,
        useValue: {
          isEnabled: config.swUpdate.isEnabled,
          versionUpdates: config.swUpdate.versionUpdates,
          unrecoverable: config.swUpdate.unrecoverable,
          checkForUpdate: () => Promise.resolve(config.swUpdate.checkForUpdate),
        },
      },
      {
        provide: ApplicationRef,
        useValue: {
          isStable: config.appRef.isStable,
        },
      },
      {
        provide: DOCUMENT,
        useValue: {
          defaultView: {
            ...config.document.defaultView,
          },
          location: {
            reload: () => {
              void 0;
            },
          },
        },
      },
    ],
  });

  const swService = TestBed.inject(ServiceWorkerUpdaterService);
  const swUpdate = TestBed.inject(SwUpdate);
  const toastrService = TestBed.inject(ToastrService);
  const documentMock = TestBed.inject(DOCUMENT);

  return {
    swService,
    swUpdate,
    toastrService,
    documentMock,
  };
};

describe('ServiceWorkerUpdaterService', () => {
  it('should be created', () => {
    const { swService } = setup();

    expect(swService).toBeTruthy();
  });

  it('should not start if the service is not enabled', () => {
    const { swService, swUpdate, toastrService } = setup({
      swUpdate: {
        isEnabled: false,
      },
      toastrResult: true,
    });
    const toastrSpy = spyOn(toastrService, 'show').and.callThrough();

    swService.start();

    expect(swUpdate.isEnabled).toBe(false);
    expect(swService.isServiceEnabled).toBe(false);

    expect(toastrSpy).not.toHaveBeenCalled();
  });

  it('should show a toastr when a new version is available and toastr is defined', fakeAsync(() => {
    const { swService, toastrService, documentMock } = setup({
      swUpdate: {
        isEnabled: true,
        checkForUpdate: true,
      },
      toastrResult: true,
    });

    const toastrSpy = spyOn(toastrService, 'show').and.callThrough();
    const documentSpy = spyOn(
      documentMock.location,
      'reload'
    ).and.callThrough();

    swService.start();

    expect(toastrSpy).toHaveBeenCalledTimes(1);
    expect(toastrSpy).toHaveBeenCalledWith(
      'Una nueva versión está disponible. ¿Quieres actualizar ahora?',
      'Actualización disponible',
      {
        closeButton: true,
        positionClass: 'toast-bottom-right',
        toastComponent: CustomToastrComponent,
        disableTimeOut: true,
        tapToDismiss: false,
        onActivateTick: true,
      }
    );
    expect(documentSpy).toHaveBeenCalledTimes(1);

    discardPeriodicTasks();
  }));

  it('should not show a toastr when a new version is available and toastr is not defined', fakeAsync(() => {
    const { swService, toastrService, documentMock } = setup({
      swUpdate: {
        isEnabled: true,
      },
    });

    const documentSpy = spyOn(
      documentMock.location,
      'reload'
    ).and.callThrough();

    swService.start();

    expect(toastrService).toBeNull();
    expect(documentSpy).not.toHaveBeenCalled();

    discardPeriodicTasks();
  }));

  it('should reload the page and clear caches on unrecoverable error', fakeAsync(() => {
    const { swService, documentMock } = setup({
      swUpdate: {
        isEnabled: true,
        unrecoverable: of({ reason: 'Test unrecoverable error' }),
      },
      caches: {
        keys: () => Promise.resolve(['cache1', 'cache2']),
        delete: () => Promise.resolve(true),
      },
    });

    const reloadSpy = spyOn(documentMock.location, 'reload').and.callThrough();
    const cachesDeleteSpy = spyOn(
      documentMock.defaultView!.caches,
      'delete'
    ).and.callThrough();
    const cachesKeysSpy = spyOn(
      documentMock.defaultView!.caches,
      'keys'
    ).and.callThrough();

    swService.start();

    flush();

    expect(cachesKeysSpy).toHaveBeenCalledTimes(1);
    expect(cachesDeleteSpy).toHaveBeenCalledTimes(2);
    expect(cachesDeleteSpy).toHaveBeenCalledWith('cache1');
    expect(cachesDeleteSpy).toHaveBeenCalledWith('cache2');
    expect(reloadSpy).toHaveBeenCalledTimes(1);

    discardPeriodicTasks();
  }));

  it('should not reload page if user cancels update via toastr', fakeAsync(() => {
    const { swService, toastrService, documentMock } = setup({
      swUpdate: {
        isEnabled: true,
      },
      toastrResult: false,
    });

    const toastrSpy = spyOn(toastrService, 'show').and.callThrough();
    const documentSpy = spyOn(
      documentMock.location,
      'reload'
    ).and.callThrough();

    swService.start();

    expect(toastrSpy).toHaveBeenCalledTimes(1);
    expect(documentSpy).not.toHaveBeenCalled();

    discardPeriodicTasks();
  }));

  it('should check for updates periodically', fakeAsync(() => {
    const pollInterval = 1;
    const { swService, swUpdate } = setup({
      config: { pollInterval },
      swUpdate: { isEnabled: true },
    });
    const checkForUpdateSpy = spyOn(swUpdate, 'checkForUpdate').and.returnValue(
      Promise.resolve(true)
    );

    const scheduler = new VirtualTimeScheduler(undefined, pollInterval * 4);

    swService.start(scheduler);

    scheduler.flush();
    expect(checkForUpdateSpy)
      .withContext('one call for appStable, the rest for the polling')
      .toHaveBeenCalledTimes(pollInterval * 4 + 1);
  }));
});
