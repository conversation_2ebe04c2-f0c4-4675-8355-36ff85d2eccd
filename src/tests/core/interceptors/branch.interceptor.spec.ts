import {
  HttpClient,
  provideHttpClient,
  withInterceptors,
} from '@angular/common/http';
import {
  HttpClientTestingModule,
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { StoreService } from 'src/app/core/application/services/store.service';
import { branchInterceptor } from 'src/app/core/interceptors/branch.interceptor';

const setup = (branch: unknown = null) => {
  TestBed.configureTestingModule({
    imports: [HttpClientTestingModule],
    providers: [
      provideHttpClient(withInterceptors([branchInterceptor])),
      provideHttpClientTesting(),
      {
        provide: StoreService,
        useValue: {
          selectedBranch$: of(branch),
        },
      },
    ],
  });
  const httpController = TestBed.inject(HttpTestingController);
  const http = TestBed.inject(HttpClient);
  const store = TestBed.inject(StoreService);

  return { httpController, http, store };
};

describe('branchInterceptor', () => {
  it('should add x-selected-branch header', () => {
    const testBranch = { id: 1 };
    const { httpController, http } = setup(testBranch);

    http.get('/test').subscribe();
    const req = httpController.expectOne('/test');
    expect(req.request.headers.get('x-selected-branch')).toBe(
      testBranch.id.toString()
    );

    httpController.verify();
  });

  it('should not add x-selected-branch header to svg resource', () => {
    const { httpController, http } = setup({ id: 1 });

    http.get('/test.svg').subscribe();

    const req = httpController.expectOne('/test.svg');

    expect(req.request.headers.get('x-selected-branch')).toBeNull();

    httpController.verify();
  });

  it('should not add x-selected-branch header to json resource', () => {
    const { httpController, http } = setup({ id: 1 });

    http.get('/test.json').subscribe();

    const req = httpController.expectOne('/test.json');

    expect(req.request.headers.get('x-selected-branch')).toBeNull();

    httpController.verify();
  });

  it('should not add x-selected-branch header if branch is null', () => {
    const { httpController, http } = setup();

    http.get('/test').subscribe();

    const req = httpController.expectOne('/test');

    expect(req.request.headers.get('x-selected-branch')).toBeNull();

    httpController.verify();
  });
});
