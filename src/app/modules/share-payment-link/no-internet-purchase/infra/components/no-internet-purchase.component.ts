import { Async<PERSON><PERSON><PERSON>, NgIf, NgTemplateOutlet } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { RuntimeMerchantError } from '@aplazo/merchant/shared';
import {
  AplazoTruncateLengthDirective,
  AplazoUppercaseDirective,
  OnlyNumbersDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconSpinCircle } from '@aplazo/ui-icons';
import {
  BehaviorSubject,
  catchError,
  EMPTY,
  first,
  map,
  Observable,
  shareReplay,
  tap,
} from 'rxjs';
import { RequestOTPUseCase } from '../../application/request-otp.usecase';
import { ValidateOTPUseCase } from '../../application/validate-otp.usecase';
import {
  NO_INTERNET_PURCHASE_ERROR_MESSAGES,
  NoInternetPurchaseError,
} from '../../domain/entities/no-internet-purchase';

export const steps = [
  'idle',
  'loadingOTPRequest',
  'loadingOTPValidation',
  'validateOTP',
] as const;

export type NoInternetPurchaseStep = (typeof steps)[number];

@Component({
  standalone: true,
  selector: 'aplazo-no-internet-purchase',
  templateUrl: './no-internet-purchase.component.html',
  imports: [
    NgIf,
    AsyncPipe,
    NgTemplateOutlet,
    ReactiveFormsModule,
    OnlyNumbersDirective,
    AplazoTruncateLengthDirective,
    AplazoFormFieldDirectives,
    AplazoButtonComponent,
    AplazoIconComponent,
    AplazoUppercaseDirective,
  ],
})
export class NoInternetPurchaseComponent implements OnInit {
  readonly #requestOtp = inject(RequestOTPUseCase);
  readonly #validateOtp = inject(ValidateOTPUseCase);
  readonly #iconRegistry = inject(AplazoIconRegistryService);

  readonly otpErrorKey = 'otpError';

  @ViewChild('idle', { static: true, read: TemplateRef })
  _idleTemplate!: TemplateRef<any>;

  @ViewChild('validateOTP', { static: true, read: TemplateRef })
  _validateOTPTemplate!: TemplateRef<any>;

  readonly #state$ = new BehaviorSubject<NoInternetPurchaseStep>('idle');
  readonly #loanId$ = new BehaviorSubject<number>(0);
  readonly #asyncError$ = new BehaviorSubject<
    Record<string, ValidationErrors | null>
  >({});

  readonly step$ = this.#state$.pipe(
    map(step => {
      if (step === 'idle' || step === 'loadingOTPRequest') {
        return this._idleTemplate;
      }

      if (step === 'validateOTP' || step === 'loadingOTPValidation') {
        return this._validateOTPTemplate;
      }

      return null;
    })
  );
  readonly isLoading$ = this.#state$.pipe(
    map(step => ['loadingOTPRequest', 'loadingOTPValidation'].includes(step)),
    shareReplay(1)
  );

  @Output()
  cancel = new EventEmitter<void>();

  @Input()
  set loanId(value: number | undefined | null) {
    this.#loanId$.next(value ?? 0);
  }

  readonly phoneNIP = new FormControl<string>('', {
    nonNullable: true,
    validators: [
      Validators.required,
      Validators.minLength(10),
      Validators.maxLength(10),
    ],
    asyncValidators: [this.#validateAfterTrigger()],
  });

  readonly requestOtpForm = new FormGroup({
    phoneNIP: this.phoneNIP,
  });

  readonly otpCode = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
    asyncValidators: [this.#validateAfterTrigger()],
  });

  readonly validateOtpForm = new FormGroup({
    otpCode: this.otpCode,
  });

  constructor() {
    this.#iconRegistry.registerIcons([iconSpinCircle]);
  }

  validateCode(): void {
    this.validateOtpForm.markAllAsTouched();

    if (this.validateOtpForm.touched && this.validateOtpForm.valid) {
      this.#state$.next('loadingOTPValidation');

      this.#validateOtp
        .execute({
          phoneNumber: this.phoneNIP.value,
          loanId: this.#loanId$.getValue(),
          code: this.otpCode.value,
        })
        .pipe(
          tap(() => {
            this.resetAndClose();
            this.#asyncError$.next({});
          }),
          catchError(err => {
            this.#state$.next('validateOTP');

            const isControlledError = err instanceof RuntimeMerchantError;
            const errors = this.#asyncError$.getValue();
            const code = this.otpCode.value;

            if (
              isControlledError &&
              err.code.startsWith('ValidateOTPUseCase::ApzOcl::') &&
              err.code !== 'ValidateOTPUseCase::ApzOcl::kycCode'
            ) {
              const key = err.code.replace(
                'ValidateOTPUseCase::ApzOcl::',
                ''
              ) as NoInternetPurchaseError;

              errors[code] = {
                [this.otpErrorKey]: NO_INTERNET_PURCHASE_ERROR_MESSAGES[key],
              };

              this.#asyncError$.next(errors);
            } else if (
              isControlledError &&
              err.code === 'ValidateOTPUseCase::ApzOcl::insufficientCredit'
            ) {
              errors[code] = {
                [this.otpErrorKey]: err.message,
              };

              this.#asyncError$.next(errors);
            }
            this.otpCode.updateValueAndValidity();

            return EMPTY;
          })
        )
        .subscribe();
    }
  }

  requestOTP(): void {
    this.requestOtpForm.markAllAsTouched();

    if (this.requestOtpForm.touched && this.requestOtpForm.valid) {
      this.#state$.next('loadingOTPRequest');

      this.#requestOtp
        .execute({
          phoneNumber: this.phoneNIP.value,
          loanId: this.#loanId$.getValue(),
        })
        .pipe(
          tap(() => {
            this.#state$.next('validateOTP');
            this.#asyncError$.next({});
          }),
          catchError(err => {
            this.#state$.next('idle');

            const isControlledError = err instanceof RuntimeMerchantError;
            const errors = this.#asyncError$.getValue();
            const phone = '' + this.phoneNIP.value;

            if (
              isControlledError &&
              err.code.startsWith('RequestOTPUseCase::ApzOcl::') &&
              err.code !== 'RequestOTPUseCase::ApzOcl::kycCode' &&
              err.code !== 'RequestOTPUseCase::ApzOcl::insufficientCredit'
            ) {
              const key = err.code.replace(
                'RequestOTPUseCase::ApzOcl::',
                ''
              ) as NoInternetPurchaseError;

              errors[phone] = {
                [this.otpErrorKey]: NO_INTERNET_PURCHASE_ERROR_MESSAGES[key],
              };

              this.#asyncError$.next(errors);
            } else if (
              isControlledError &&
              (err.code === 'RequestOTPUseCase::ApzOcl::kycCode' ||
                err.code === 'RequestOTPUseCase::ApzOcl::insufficientCredit')
            ) {
              errors[phone] = {
                [this.otpErrorKey]: err.message,
              };

              this.#asyncError$.next(errors);
            }
            this.phoneNIP.updateValueAndValidity();

            return EMPTY;
          })
        )
        .subscribe();
    }
  }

  clearOTPRequest(): void {
    this.#asyncError$.next({});
    this.phoneNIP.reset();
    this.#state$.next('idle');
  }

  clearValidateOTP(): void {
    this.otpCode.reset();
    this.#state$.next('idle');
  }

  reset(): void {
    this.clearOTPRequest();
    this.clearValidateOTP();
  }

  resetAndClose(): void {
    this.reset();
    this.cancel.emit();
  }

  ngOnInit(): void {
    this.reset();
  }

  #validateAfterTrigger(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      const currentNIP = control.value;

      return this.#asyncError$.pipe(
        first(),
        map(stateError => {
          const errors = stateError[currentNIP];

          if (errors) {
            return errors;
          }

          return null;
        })
      );
    };
  }
}
