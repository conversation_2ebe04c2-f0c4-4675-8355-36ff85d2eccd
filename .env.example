## app related
NODE_ENV=stageOrDevelopOrProductionOrProdOrSomethingElse
NG_APP_PRODUCTION=false
NG_APP_API_URL=https://example.com/api/
NG_APP_APP_NAME=APP_NAME
NG_APP_POS_API_URL=https://example.com/pos
NG_APP_POS_WS_URL=wss://example.com/pos
NG_APP_HOSTNAME=https://example.com/posui
NG_APP_LANDINGPAGE=https://example.com/
NG_APP_REGISTER_MERCHANT_URL=https://example.com/partners
NG_APP_CUSTOMER_LOGIN_URL=https://example.com/customer/
NG_APP_AUTH_API_URL=https://example.com/auth/api
NG_APP_GTM_ID=GTM-XXXXXX
NG_APP_PROMO_API_URL=https://example.com/promotions/
NG_APP_FEATURE_FLAGS_API_KEY=your-feature-flags-api-key
NG_APP_FEATURE_FLAGS_ENV=environment
NG_APP_DATADOG_APPLICATION_ID=your-datadog-application-id
NG_APP_DATADOG_CLIENT_TOKEN=your-datadog-client-token
NG_APP_DATADOG_ENV=environment
NG_APP_DATADOG_SERVICE=service-name
NG_APP_MERCHANT_DASH_API_URL=https://example.com/merchant-dashboard/
NG_APP_WEBCHAT_API_KEY=your-webchat-api-key
NG_APP_WEBCHAT_BRAND_ID=your-webchat-brand-id
NG_APP_PAYMENTS_CORE_API_URL=https://example.com/payments
NG_APP_MSPAYMENT_MICRO=https://example.com/payments/
NG_APP_KOUNT_CLIENT_ID=your-kount-client-id
NG_APP_KOUNT_HOSTNAME=https://example.com/kount
NG_APP_KOUNT_IS_SINGLE_PAGE_APP=true
NG_APP_MS_MERCHANT_MICRO=https://example.com/merchant
NG_APP_BIFROST_URL=https://example.com/bifrost
NG_APP_CUSTOMER_REGISTRATION_MICRO=https://example.com/customer-registration
NG_APP_EXPOSED_PUBLIC_API_URL=https://core.aplazo.net
## for docker
AUTH_TOKEN=myAwesomeToken
ENV=build-stg
DEV_ENV=stg

