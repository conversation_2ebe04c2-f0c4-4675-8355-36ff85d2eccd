import { Component, OnInit } from '@angular/core';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoStatCardComponent } from '@aplazo/shared-ui/merchant';
import {
  AsyncPipe,
  CurrencyPipe,
  DatePipe,
  NgForOf,
  NgIf,
} from '@angular/common';
import { Observable, switchMap, take, tap } from 'rxjs';
import { DialogService } from '@ngneat/dialog';
import { ChallengeComponent } from './challenge.component';
import { Challenge } from '../../core/domain/challenge';
import { PosOfflineRepository } from '../../core/domain/repositories/pos-offline.repository';
import { StoreService } from '../../core/application/services/store.service';
import { ChallengeCompletedComponent } from './challenge-completed.component';

@Component({
  standalone: true,
  selector: 'aplazo-rewards-page',
  templateUrl: './challenges-page.component.html',
  imports: [
    AplazoCardComponent,
    AplazoStatCardComponent,
    AsyncPipe,
    CurrencyPipe,
    DatePipe,
    NgForOf,
    NgIf,
    ChallengeComponent,
  ],
})
export class ChallengesPageComponent implements OnInit {
  challenges$: Observable<Challenge[]>;

  constructor(
    private posService: PosOfflineRepository,
    private storeService: StoreService,
    private dialogService: DialogService
  ) {}

  ngOnInit() {
    this.challenges$ = this.storeService.selectedBranch$.pipe(
      switchMap(selectedBranch =>
        this.posService.getChallenges(selectedBranch?.id ?? 0)
      ),
      tap(challenges => {
        if (challenges.length == 0) return;

        const challenge = challenges[0];

        if (challenge.status == 'Completed') {
          if (localStorage.getItem(challenge.id) === 'true') return;

          const ref = this.dialogService.open(ChallengeCompletedComponent, {
            data: {
              challenge: challenges[0],
            },
          });

          ref.afterClosed$
            .pipe(take(1))
            .subscribe(() => localStorage.setItem(challenge.id, 'true'));
        }
      })
    );
  }
}
