import { Observable } from 'rxjs';
import { INotificationResponseDto, NotificationType } from '../notification';

export abstract class NotificationsRepository {
  abstract getNotificationsByTypeAndMerchantId(
    type: NotificationType,
    merchantId?: number | string
  ): Observable<INotificationResponseDto[]>;

  abstract getNotificationsByTypeAndBranchId(
    type: NotificationType,
    branchId?: number
  ): Observable<INotificationResponseDto[]>;
}
