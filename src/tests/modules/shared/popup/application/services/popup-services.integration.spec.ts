import { TestBed } from '@angular/core/testing';
import { PopupTriggerService } from 'src/app/modules/shared/popup/application/services/popup-trigger.service';
import { PopupStorageService } from 'src/app/modules/shared/popup/application/services/popup-storage.service';
import { PopupRepository } from 'src/app/modules/shared/popup/infra/repositories/popup.repository';
import { StoreService } from 'src/app/core/application/services/store.service';
import { ShowPopupUseCase } from 'src/app/modules/shared/popup/application/usecases/show-popup.usecase';
import { fakeAsync, tick } from '@angular/core/testing';
import { of } from 'rxjs';

describe('Popup Services Integration', () => {
  let triggerService: PopupTriggerService;
  let storageService: PopupStorageService;
  let popupRepositorySpy: jasmine.SpyObj<PopupRepository>;
  let localStorageSpy: jasmine.SpyObj<Storage>;
  let originalLocalStorage: Storage;

  beforeEach(() => {
    popupRepositorySpy = jasmine.createSpyObj('PopupRepository', [
      'getAvailablePopups',
    ]);
    originalLocalStorage = window.localStorage;
    Object.defineProperty(window, 'localStorage', {
      value: jasmine.createSpyObj('Storage', [
        'getItem',
        'setItem',
        'removeItem',
      ]),
      configurable: true,
    });
    localStorageSpy = window.localStorage as any;

    TestBed.configureTestingModule({
      providers: [
        PopupTriggerService,
        PopupStorageService,
        { provide: PopupRepository, useValue: popupRepositorySpy },
        {
          provide: StoreService,
          useValue: jasmine.createSpyObj('StoreService', ['getBranchId']),
        },
        {
          provide: ShowPopupUseCase,
          useValue: jasmine.createSpyObj('ShowPopupUseCase', ['execute']),
        },
      ],
    });

    triggerService = TestBed.inject(PopupTriggerService);
    storageService = TestBed.inject(PopupStorageService);
  });

  afterEach(() => {
    Object.defineProperty(window, 'localStorage', {
      value: originalLocalStorage,
    });
    storageService.clearAllRecords();
  });

  it('should return true and mark popup as shown when a popup is available and not shown today', fakeAsync(() => {
    const today = new Date().toISOString().split('T')[0];
    const branchId = '123';
    const popupId = 'popup1';
    localStorageSpy.getItem.and.returnValue('[]');
    popupRepositorySpy.getAvailablePopups.and.returnValue(
      of([{ popupId, startAt: '2024-01-01', endAt: '2024-12-31', priority: 1 }])
    );
    const showPopupUseCaseSpy = TestBed.inject(
      ShowPopupUseCase
    ) as jasmine.SpyObj<ShowPopupUseCase>;
    showPopupUseCaseSpy.execute.and.returnValue(
      of({
        success: true,
        popupShown: {
          popupId,
          startAt: '2024-01-01',
          endAt: '2024-12-31',
          priority: 1,
        },
      })
    );

    let result: boolean;
    triggerService.shouldShowPopup({ branchId }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result!).toBe(true);
    expect(localStorageSpy.setItem).not.toHaveBeenCalled(); // Not marked yet, only checked

    // Simulate marking as shown via use case (assuming it's called)
    storageService.markPopupAsShown(popupId, branchId, today);
    expect(localStorageSpy.setItem).toHaveBeenCalledWith(
      'aplazo_popups_shown',
      jasmine.stringContaining(popupId)
    );
  }));

  it('should return false when a popup was already shown today', fakeAsync(() => {
    const today = new Date().toISOString().split('T')[0];
    const branchId = '123';
    const popupId = 'popup1';
    const mockRecords = [
      {
        popupId,
        branchId,
        date: today,
        timestamp: Date.now(),
      },
    ];
    localStorageSpy.getItem.and.returnValue(JSON.stringify(mockRecords));
    popupRepositorySpy.getAvailablePopups.and.returnValue(
      of([{ popupId, startAt: '2024-01-01', endAt: '2024-12-31', priority: 1 }])
    );

    let result: boolean;
    triggerService.shouldShowPopup({ branchId }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result!).toBe(false);
  }));

  it('should cleanup old records through trigger service', fakeAsync(() => {
    const today = new Date();
    const oldDate = new Date(today.getTime() - 31 * 24 * 60 * 60 * 1000);
    const mockRecords = [
      {
        popupId: 'popup1',
        branchId: '123',
        date: oldDate.toISOString().split('T')[0],
        timestamp: oldDate.getTime(),
      },
      {
        popupId: 'popup2',
        branchId: '123',
        date: today.toISOString().split('T')[0],
        timestamp: today.getTime(),
      },
    ];
    localStorageSpy.getItem.and.returnValue(JSON.stringify(mockRecords));

    triggerService.cleanupOldRecords();

    const lastCall = localStorageSpy.setItem.calls.mostRecent().args[1];
    expect(lastCall).toContain('popup2');
    expect(lastCall).not.toContain('popup1');
  }));
});
