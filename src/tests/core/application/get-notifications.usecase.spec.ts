import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { provideUseCaseErrorHandlerTesting } from '@aplazo/merchant/shared-testing';
import { lastValueFrom, of, take, throwError } from 'rxjs';
import { GetNotificationsByTypeUsecase } from 'src/app/core/application/get-notifications.usecase';
import { StoreService } from '../../../app/core/application/services/store.service';
import { NotificationsRepository } from '../../../app/core/domain/repositories/notifications.repository';
import { DateUtilsService } from '../../../app/core/services/date-utils.service';
import { Branch } from 'src/app/core/domain/entities';
import branches from '../../local-branches.db.json';
import { BrowserLocalStorageService } from '@aplazo/merchant/shared';

const setup = (branch: Partial<Branch> | null = null) => {
  const repositoryMock = jasmine.createSpyObj<NotificationsRepository>(
    'NotificationsRepository',
    ['getNotificationsByTypeAndBranchId']
  );

  TestBed.configureTestingModule({
    providers: [
      DateUtilsService,
      provideUseCaseErrorHandlerTesting(),
      {
        provide: StoreService,
        useValue: {
          selectedBranch$: of(branch),
        },
      },
      {
        provide: NotificationsRepository,
        useValue: repositoryMock,
      },
      {
        provide: BrowserLocalStorageService,
        useValue: jasmine.createSpyObj('BrowserLocalStorageService', [
          'getItem',
        ]),
      },
    ],
  });

  const usecase = TestBed.inject(GetNotificationsByTypeUsecase);
  const repository = TestBed.inject(
    NotificationsRepository
  ) as jasmine.SpyObj<NotificationsRepository>;

  return { usecase, repository: repositoryMock };
};

describe('GetNotificationsUseCase', () => {
  const successResponse = [
    {
      id: 14,
      commType: 'INFO',
      title: 'Prueba, notificacion INFO',
      description:
        'Si te preguntas si Kueski Pay es seguro o si Aplazo es seguro para tu Ecommerce en este video te diré cuál es la mejor opción BNPL para tu Negocio, además vamos a comparar como funcionan estas dos pasarelas de pago que permiten pagar a crédito sin tarjeta.',
      status: 'A',
      platform: 'P',
      merchants: '2239',
      lastUpdate: '2024-03-04T23:14:33.288930Z',
      applyAll: true,
      complement: {
        urlVideo: 'https://www.youtube.com/watch?v=1ulI-t9qT7U',
        urlThumbnail: 'string',
        tags: ['FAQ'],
      },
    },
  ];

  it('should return null when merchantId is null', async () => {
    const { usecase, repository } = setup();

    const result = await lastValueFrom(usecase.execute('INFO').pipe(take(1)));

    expect(result)
      .withContext('handle scenario where merchantId is null')
      .toBeNull();
    expect(repository.getNotificationsByTypeAndBranchId).toHaveBeenCalledTimes(
      0
    );
  });

  it('should return null when repository throws error', async () => {
    const { usecase, repository } = setup(branches[0]);
    repository.getNotificationsByTypeAndBranchId.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            error: 'Internal Server Error',
            status: 500,
            statusText: 'Internal Server Error',
          })
      )
    );

    const result = await lastValueFrom(usecase.execute('INFO').pipe(take(1)));

    expect(result)
      .withContext('handle scenario where repository throws error')
      .toBeNull();
    expect(repository.getNotificationsByTypeAndBranchId).toHaveBeenCalledTimes(
      1
    );
  });

  it('should return notifications when branchId is not null', async () => {
    const { usecase, repository } = setup(branches[0]);
    repository.getNotificationsByTypeAndBranchId.and.returnValue(
      of(successResponse) as any
    );
    const expected = {
      type: 'INFO',
      newOnesCounter: 1,
      hasNewOnes: true,
      newOnesIds: [14],
      notifications: new Map([
        [
          'MARZO 2024',
          [
            {
              ...successResponse[0],
              isNewOne: false,
              lastUpdate: new Date('2024-03-04T23:14:33.288930Z'),
              lastUpdateTimestamp: new Date(
                '2024-03-04T23:14:33.288930Z'
              ).getTime(),
              merchants: [2239],
            },
          ],
        ],
      ]),
    };

    const result = await lastValueFrom(usecase.execute('INFO').pipe(take(1)));

    expect(repository.getNotificationsByTypeAndBranchId).toHaveBeenCalledTimes(
      1
    );
    expect(result?.hasNewOnes).toEqual(expected.hasNewOnes);
    expect(result?.newOnesCounter).toEqual(expected.newOnesCounter);
    expect(result?.type).toEqual(expected.type as any);
    expect((result?.notifications.get('MARZO 2024') as any)[0].id).toEqual(
      (expected.notifications.get('MARZO 2024') as any)[0].id as any
    );
    expect(
      (result?.notifications.get('MARZO 2024') as any)[0].description
    ).toEqual(
      (expected.notifications.get('MARZO 2024') as any)[0].description as any
    );
  });
});
