import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { FinishWithBarcodeUsecase } from '../../application/finish-with-barcode.usecase';
import { BarCodeRepository } from '../../domain/barcode.repository';
import { BarCodeWithHttpRepository } from '../repositories/barcode-with-http.repository';

export function provideBarcode(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: BarCodeRepository,
      useClass: BarCodeWithHttpRepository,
    },
    FinishWithBarcodeUsecase,
  ]);
}
