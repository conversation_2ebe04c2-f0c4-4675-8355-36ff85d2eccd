import { DOCUMENT } from '@angular/common';
import {
  APP_INITIALIZER,
  ApplicationRef,
  EnvironmentProviders,
  inject,
  Injectable,
  InjectionToken,
  Injector,
  makeEnvironmentProviders,
  OnDestroy,
} from '@angular/core';
import { SwUpdate } from '@angular/service-worker';
import { ToastrService } from 'ngx-toastr';
import {
  asyncScheduler,
  concat,
  defer,
  filter,
  first,
  interval,
  of,
  SchedulerLike,
  Subject,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';
import { CustomToastrComponent } from '../components/custom-toastr.component';

export const SERVICE_WORKER_UPDATER_CONFIG = new InjectionToken<
  Partial<{
    pollInterval: number;
    dialogTitle: string;
    dialogMessage: string;
  }>
>('SERVICE_WORKER_UPDATER_CONFIG');

export function provideServiceWorkerUpdater(
  config: Partial<{
    pollInterval: number;
    dialogTitle: string;
    dialogMessage: string;
  }>
): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: SERVICE_WORKER_UPDATER_CONFIG,
      useValue: config,
    },
    ServiceWorkerUpdaterService,
    {
      provide: APP_INITIALIZER,
      multi: true,
      useFactory: () => {
        const swUpdater = inject(ServiceWorkerUpdaterService);

        return () => {
          swUpdater.start();
        };
      },
    },
  ]);
}

/**
 * Service that provides a facade for handling PWA updates
 */
@Injectable({
  providedIn: 'root',
})
export class ServiceWorkerUpdaterService implements OnDestroy {
  readonly #injector = inject(Injector);
  readonly #swUpdater = inject(SwUpdate);
  readonly #appRef = inject(ApplicationRef);
  readonly #document = inject(DOCUMENT);
  readonly #config = inject(SERVICE_WORKER_UPDATER_CONFIG);
  readonly #toastr = this.#injector.get(ToastrService);

  readonly #destroy = new Subject<void>();

  readonly #DEFAULT_CHECK_INTERVAL = 6 * 60 * 60 * 1000; // 6 hours
  readonly #DEFAULT_MESSAGE = {
    title: 'Actualización disponible',
    message: 'Una nueva versión está disponible. ¿Quieres actualizar ahora?',
    cancelButton: 'Cancelar',
    acceptButton: 'Actualizar',
  };

  readonly #appIsStable$ = this.#appRef.isStable.pipe(
    first(isStable => isStable === true)
  );
  readonly #everyCertainPeriod$ = (scheduler: SchedulerLike) =>
    interval(
      this.#config.pollInterval ?? this.#DEFAULT_CHECK_INTERVAL,
      scheduler
    );
  readonly #pollAtInterval$ = (scheduler: SchedulerLike) =>
    concat(this.#appIsStable$, this.#everyCertainPeriod$(scheduler));

  readonly isServiceEnabled = this.#swUpdater.isEnabled;

  start(scheduler: SchedulerLike = asyncScheduler): void {
    if (!this.isServiceEnabled) {
      console.warn('Service worker updates are not enabled');
      return;
    }

    this.#swUpdater.versionUpdates
      .pipe(
        filter(swEvent => swEvent.type === 'VERSION_READY'),
        switchMap(swEvent => {
          if (this.#toastr) {
            const toast = this.#toastr.show(
              this.#config.dialogMessage ?? this.#DEFAULT_MESSAGE.message,
              this.#config.dialogTitle ?? this.#DEFAULT_MESSAGE.title,
              {
                closeButton: true,
                positionClass: 'toast-bottom-right',
                toastComponent: CustomToastrComponent,
                disableTimeOut: true,
                tapToDismiss: false,
                onActivateTick: true,
              }
            );

            return toast.onAction.pipe(first());
          }

          return of({ confirmation: false });
        }),

        tap(result => {
          if (result?.confirmation === true) {
            this.#document.location.reload();
          }
        }),
        takeUntil(this.#destroy)
      )
      .subscribe();

    this.#swUpdater.unrecoverable
      .pipe(
        tap(event => {
          console.warn('debug :: unrecoverable :: ', event);
          if (
            this.#document?.defaultView &&
            Object.prototype.hasOwnProperty.bind(
              this.#document.defaultView,
              'caches'
            )
          ) {
            this.#document.defaultView.caches.keys().then(cacheNames => {
              cacheNames.forEach(cacheName => {
                this.#document!.defaultView!.caches.delete(cacheName);
              });
            });
          }
          this.#document.location.reload();
        })
      )
      .pipe(takeUntil(this.#destroy))
      .subscribe();

    this.#pollAtInterval$(scheduler)
      .pipe(
        switchMap(() =>
          defer(() => {
            return this.#swUpdater.checkForUpdate();
          })
        ),
        takeUntil(this.#destroy)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy.next();
    this.#destroy.complete();
  }
}
