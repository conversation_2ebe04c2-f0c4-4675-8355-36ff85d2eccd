import { Observable } from 'rxjs';
import { PopupId } from '../value-objects/popup-id.value-object';

/**
 * Core popup entity interface.
 * Represents a popup that can be displayed to users.
 */
export interface Popup {
  id: PopupId;
  title: string;
  content: string;
  priority: number;
  isActive: boolean;
  createdAt: Date;
  expiresAt?: Date;
}

/**
 * Popup data for storage operations.
 * Contains user interaction data and dismissal preferences.
 */
export interface PopupData {
  lastShownDate?: string;
  isPermanentlyDismissed: boolean;
  dismissalReason?: string;
  shownCount: number;
}

/**
 * Repository interface for popup data operations.
 * Defines the contract for popup data access following Clean Architecture principles.
 */
export interface PopupRepository {
  /**
   * Retrieves all available popups.
   * @returns Observable that emits an array of popups
   */
  getPopups(): Observable<Popup[]>;

  /**
   * Retrieves popups by priority.
   * @param priority - The priority level to filter by
   * @returns Observable that emits an array of popups with the specified priority
   */
  getPopupsByPriority(priority: number): Observable<Popup[]>;
}

/**
 * Display port interface for popup presentation.
 * Defines the contract for popup display operations.
 */
export interface PopupDisplayPort {
  /**
   * Shows a popup to the user.
   * @param popup - The popup to display
   * @returns Observable that completes when popup is shown
   */
  showPopup(popup: Popup): Observable<void>;

  /**
   * Hides the currently displayed popup.
   * @returns Observable that completes when popup is hidden
   */
  hidePopup(): Observable<void>;
}

/**
 * Storage port interface for popup data persistence.
 * Defines the contract for popup data storage operations.
 */
export interface PopupStoragePort {
  /**
   * Saves popup data to storage.
   * @param data - The popup data to save
   * @returns Observable that completes when data is saved
   */
  savePopupData(data: PopupData): Observable<void | string>;

  /**
   * Retrieves popup data from storage.
   * @returns Observable that emits the stored popup data or an error string
   */
  getPopupData(): Observable<PopupData | string | null>;

  /**
   * Clears all popup data from storage.
   * @returns Observable that completes when data is cleared or emits an error string
   */
  clearPopupData(): Observable<void | string>;
}
