import { DOCUMENT } from '@angular/common';
import {
  Component,
  EventEmitter,
  inject,
  Output,
  SecurityContext,
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { lastValueFrom } from 'rxjs';
import { AnalyticsService } from '../../../../core/application/services/analytics.service';
import { ROUTE_CONFIG } from '../../../../core/domain/config/app-routes.core';
import { GetTermsConditionsLinkUsecase } from '../../application/usecases/get-terms-conditions-link.usecase';

@Component({
  standalone: true,
  selector: 'app-contest-invitation',
  template: `
    <article
      class="py-10 px-10 md:px-30 grid grid-cols-1 md:grid-cols-2 justify-center w-full font-light text-dark-secondary text-lg">
      <div class="grid sm:grid-cols-1 gap-x-2 px-6">
        <div class="max-w-md">
          <h3 class="text-xl font-medium my-6">¡Gana con Premios APLAZO!</h3>
          <p class="mb-6">
            Inscríbete y participa. Empieza a acumular puntos y gana premios
            como: pantallas, celulares, audifonos y mucho más.
          </p>
          <p class="mb-6 font-bold">¿Cómo participar?</p>
          <ol class="mb-6 list-decimal pl-6">
            <li class="my-3">
              <span class="font-bold">Inscríbete ahora</span> presionando el
              botón.
            </li>
            <li class="my-3">
              <span class="font-bold"
                >Recibe un código QR y un enlace único</span
              >
              por WhatsApp.
            </li>
            <li class="my-3">
              <span class="font-bold">Registra nuevos clientes</span> desde esta
              plataforma y acumula puntos.
            </li>
          </ol>
          <p class="mb-6">¡Es rápido y fácil!</p>
          <p class="mb-6">
            ¡Mientras más clientes registres de forma exitosa, más puntos ganas
            y más premios puedes llevarte.
          </p>
          <p class="mb-6">Presiona el botón de abajo y ¡comienza a ganar!</p>
          <blockquote
            class="p-4 my-4 border-s-4 border-dark-tertiary bg-dark-background rounded-lg">
            <p
              class="text-xl italic font-medium leading-relaxed text-dark-secondary">
              Revisa los
              <button class="text-accent" (click)="openPdf()">
                términos y condiciones
              </button>
              y ve
              <a
                class="text-accent"
                [href]="videoUrl"
                rel="noreferrer noopener"
                target="_blank">
                este video
              </a>
              para entender más sobre los Premios APLAZO.
            </p>
          </blockquote>
          <div class="my-10 flex justify-center">
            <button
              aplzButton
              aplzAppearance="solid"
              aplzColor="dark"
              size="lg"
              [rounded]="true"
              (click)="registrate()">
              ¡Inscríbete ahora!
            </button>
          </div>
        </div>
      </div>
      <div class="grid sm:grid-cols-1 gap-x-2 px-6 items-center">
        <video
          class="w-[640px] h-[360px] flex-shrink-0"
          controls
          preload="auto"
          width="640"
          height="360">
          <source
            src="https://cdn.aplazo.mx/reglas/los_premios_aplazo_video.mp4"
            type="video/mp4" />
          Tu navegador no soporta la reproducción de videos.
        </video>
      </div>
    </article>
  `,
  imports: [AplazoButtonComponent],
})
export class AplazoContestInvitationComponent {
  readonly #sanitizer = inject(DomSanitizer);
  readonly #document = inject(DOCUMENT);
  readonly #analytics = inject(AnalyticsService);
  readonly #getTermsConditionsLinkUsecase = inject(
    GetTermsConditionsLinkUsecase
  );

  @Output()
  registrationEvent = new EventEmitter<void>();

  readonly appRoutes = ROUTE_CONFIG;

  readonly videoUrl =
    this.#sanitizer.sanitize(
      SecurityContext.URL,
      'https://cdn.aplazo.mx/reglas/los_premios_aplazo_video.mp4'
    ) ?? '';

  async openPdf(): Promise<void> {
    const windowFeatures =
      'noopener,noreferrer,toolbar=yes,scrollbars=yes,resizable=yes,top=250,left=500,width=900,height=600';

    try {
      const url = await lastValueFrom(
        this.#getTermsConditionsLinkUsecase.execute()
      );

      this.#document.defaultView?.open(url, 'Aplazo Document', windowFeatures);
    } catch (error) {
      console.warn('Error opening PDF', error);
    }
  }

  registrate(): void {
    this.registrationEvent.emit();
    this.#analytics.track('customClick', {
      buttonName: 'contestRegistration',
      label: 'contest-entries',
      timestamp: new Date().getTime(),
    });
  }
}
