import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { LoaderService } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { lastValueFrom, of, take, throwError } from 'rxjs';
import { GetTrainingContentMediaUsecase } from '../../../app/core/application/get-training-media-content.usecase';
import { NotificationsRepository } from '../../../app/core/domain/repositories/notifications.repository';
import { DateUtilsService } from '../../../app/core/services/date-utils.service';
import { StoreService } from '../../../app/core/application/services/store.service';
import branches from '../../local-branches.db.json';

describe('GetTrainingContentMediaUsecase', () => {
  let usecase: GetTrainingContentMediaUsecase;
  let repository: jasmine.SpyObj<NotificationsRepository>;
  let loaderShowSpy: jasmine.Spy;
  let loaderHideSpy: jasmine.Spy;

  const successResponse = [
    {
      id: 10,
      commType: 'TRAINING',
      title: 'Customers - TRAINING 20230220',
      description: 'Prueba de sección TRAINING APLAZOVERSITY',
      status: 'A',
      platform: 'P',
      merchants: '2239',
      lastUpdate: '2024-03-15T19:08:15.516151Z',
      applyAll: false,
      complement: {
        urlVideo: 'https://youtu.be/LVVssVlPBMc',
        urlThumbnail:
          'https://aplazoassets.s3.us-west-2.amazonaws.com/landing-pages/buen-fin/buen-fin.webp',
        tags: ['CUSTOMER'],
      },
    },
  ];

  const repositoryMock = jasmine.createSpyObj<NotificationsRepository>(
    'NotificationsRepository',
    ['getNotificationsByTypeAndBranchId']
  );
  const branch = branches[0];
  beforeEach(() => {
    repositoryMock.getNotificationsByTypeAndBranchId.calls.reset();
    TestBed.configureTestingModule({
      providers: [
        DateUtilsService,
        provideLoaderTesting(),
        provideUseCaseErrorHandlerTesting(),
        {
          provide: StoreService,
          useValue: {
            selectedBranch$: of(branch),
          },
        },
        {
          provide: NotificationsRepository,
          useValue: repositoryMock,
        },
        GetTrainingContentMediaUsecase,
      ],
    });

    usecase = TestBed.inject(GetTrainingContentMediaUsecase);
    repository = TestBed.inject(
      NotificationsRepository
    ) as jasmine.SpyObj<NotificationsRepository>;
    const loader = TestBed.inject(LoaderService);
    loaderShowSpy = spyOn(loader, 'show').and.callThrough();
    loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
  });

  it('should notificationType for the usecase be TRAINING', () => {
    expect(usecase.notificationType()).toBe('TRAINING');
  });

  it('should return null when repository throws error', async () => {
    repository.getNotificationsByTypeAndBranchId.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            error: 'Internal Server Error',
            status: 500,
            statusText: 'Internal Server Error',
          })
      )
    );

    const result = await lastValueFrom(usecase.execute().pipe(take(1)));

    expect(result)
      .withContext('handle scenario where repository throws error')
      .toBeNull();
    expect(repository.getNotificationsByTypeAndBranchId).toHaveBeenCalledTimes(
      1
    );
    expect(loaderShowSpy)
      .withContext('should show the loader once')
      .toHaveBeenCalledTimes(1);
    expect(loaderHideSpy)
      .withContext('should hide the loader once')
      .toHaveBeenCalledTimes(1);
  });

  it('should return notifications when merchantId is not null', async () => {
    repository.getNotificationsByTypeAndBranchId.and.returnValue(
      of(successResponse) as any
    );
    const expected = {
      tags: ['Clientes'],
      content: [
        {
          title: 'Customers - TRAINING 20230220',
          description: 'Prueba de sección TRAINING APLAZOVERSITY',
          urlVideo: 'https://youtu.be/LVVssVlPBMc',
          isValidVideoUrl: true,
          urlThumbnail:
            'https://aplazoassets.s3.us-west-2.amazonaws.com/landing-pages/buen-fin/buen-fin.webp',
          tags: ['Clientes'],
          id: 10,
        },
      ],
    };

    const result = await lastValueFrom(usecase.execute().pipe(take(1)));

    expect(loaderShowSpy)
      .withContext('should show the loader once')
      .toHaveBeenCalledTimes(1);
    expect(loaderHideSpy)
      .withContext('should hide the loader once')
      .toHaveBeenCalledTimes(1);
    expect(repository.getNotificationsByTypeAndBranchId).toHaveBeenCalledTimes(
      1
    );
    expect(result).toEqual(expected as any);
  });
});
