import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import {
  HttpClientTestingModule,
  HttpTestingController,
  provideHttpClientTesting,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { POS_ENVIRONMENT_CORE } from '../../../../../app-core/domain/environments';
import { NoInternetPurchaseWithHttp } from '../../../../../app/modules/share-payment-link/no-internet-purchase/infra/repositories/no-internet-purchase.repository';

describe('NoInternetPurchaseWithHttp', () => {
  let httpController: HttpTestingController;
  let repository: NoInternetPurchaseWithHttp;
  let postSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        provideHttpClientTesting(),
        NoInternetPurchaseWithHttp,
        {
          provide: POS_ENVIRONMENT_CORE,
          useValue: {
            bifrostUrl: 'https://pos.aplazo.net',
          },
        },
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    repository = TestBed.inject(NoInternetPurchaseWithHttp);
    postSpy = spyOn(TestBed.inject(HttpClient), 'post').and.callThrough();
  });

  afterEach(() => {
    httpController.verify();
  });

  it('should be created', () => {
    expect(repository).toBeTruthy();
    expect(repository).toBeInstanceOf(NoInternetPurchaseWithHttp);
  });

  describe('requestOTP', () => {
    it('should request OTP successfully', () => {
      const expectedResponse = null;

      repository
        .requestOTP({
          loanId: 1,
          phoneNumber: '**********',
        })
        .subscribe({
          next: resp => {
            expect(resp).toBeNull();
            expect(postSpy).toHaveBeenCalledTimes(1);
          },
        });

      const req = httpController.expectOne(
        'https://pos.aplazo.net/api/v1/no-internet-purchases'
      );

      expect(req.request.method).toBe('POST');

      req.flush(expectedResponse);
    });

    it('should request OTP fail', () => {
      const expectedResponse = null;

      repository
        .requestOTP({
          loanId: 1,
          phoneNumber: '**********',
        })
        .subscribe({
          next: fail,
          error: err => {
            expect(err).toBeInstanceOf(HttpErrorResponse);
            expect(postSpy).toHaveBeenCalledTimes(1);
          },
        });

      const req = httpController.expectOne(
        'https://pos.aplazo.net/api/v1/no-internet-purchases'
      );

      expect(req.request.method).toBe('POST');

      req.flush(expectedResponse, {
        status: 400,
        statusText: 'Bad Request',
      });
    });
  });

  describe('validateOTP', () => {
    it('should validate OTP successfully', () => {
      const expectedResponse = null;

      repository
        .validateOTP({
          loanId: 1,
          phoneNumber: '**********',
          code: '123456',
        })
        .subscribe({
          next: resp => {
            expect(resp).toBeNull();
            expect(postSpy).toHaveBeenCalledTimes(1);
          },
        });

      const req = httpController.expectOne(
        'https://pos.aplazo.net/api/v1/no-internet-purchases/validate-code'
      );

      expect(req.request.method).toBe('POST');

      req.flush(expectedResponse);
    });

    it('should validate OTP fail', () => {
      const expectedResponse = null;

      repository
        .validateOTP({
          loanId: 1,
          phoneNumber: '**********',
          code: '123456',
        })
        .subscribe({
          next: fail,
          error: err => {
            expect(err).toBeInstanceOf(HttpErrorResponse);
            expect(postSpy).toHaveBeenCalledTimes(1);
          },
        });

      const req = httpController.expectOne(
        'https://pos.aplazo.net/api/v1/no-internet-purchases/validate-code'
      );

      expect(req.request.method).toBe('POST');

      req.flush(expectedResponse, {
        status: 400,
        statusText: 'Bad Request',
      });
    });
  });
});
