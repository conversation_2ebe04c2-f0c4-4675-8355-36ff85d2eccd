FROM mcr.microsoft.com/playwright:v1.50.1-jammy AS build

ARG AUTH_TOKEN

ENV AUTH_TOKEN=$AUTH_TOKEN

WORKDIR /usr/src/app

# Create coverage directory with proper permissions
RUN mkdir -p /usr/src/app/coverage && chmod 777 /usr/src/app/coverage

COPY package*.json ./
COPY .npmrc ./
COPY .env ./

RUN --mount=type=cache,target=/root/.npm npm ci --also-dev && npx playwright install chromium

COPY . .

CMD node --run test
