import { ILoanRequestUIDto, LoanRequest } from 'src/app/core/domain/cart';

describe('LoanRequest', () => {
  const loanUI: ILoanRequestUIDto = {
    branchId: 1,
    cartId: 'abcd1234',
    hostname: 'http://localhost:4200',
    products: [
      {
        count: 2,
        description: 'description',
        externalId: 'externalId',
        imageUrl: 'imageUrl',
        price: 50,
        title: 'title',
      },
    ],
    sellsAgentId: 1,
    totalPrice: '100',
  };

  it("should can't create a loan with invalid price", () => {
    expect(() =>
      LoanRequest.create({
        ...loanUI,
        totalPrice: '-100',
      })
    ).toThrowError();
  });

  it("should can't create a loan with infinite price", () => {
    expect(() => {
      LoanRequest.create({
        ...loanUI,
        totalPrice: 'Infinity',
      });
    }).toThrowError();
  });

  it("should can't create a loan with negative sellsAgentId", () => {
    expect(() =>
      LoanRequest.create({
        ...loanUI,
        sellsAgentId: -1,
      })
    ).toThrowError();
  });

  it("should can't create a loan with infinity sellsAgentId", () => {
    expect(() =>
      LoanRequest.create({
        ...loanUI,
        sellsAgentId: Infinity,
      })
    ).toThrowError();
  });

  it('should create a loan with valid data', () => {
    const loan = LoanRequest.create(loanUI);

    expect(loan).toBeInstanceOf(LoanRequest);
    expect(loan.cartId).toBe(loanUI.cartId);
    expect(loan.products).toBe(loanUI.products);
    expect(loan.discount).toEqual({ title: 'sin descuento', price: 0 });
    expect(loan.taxes).toEqual({ title: 'IVA', price: 16 });
    expect(loan.shipping).toEqual({ title: 'Recoger en tienda', price: 0 });
    expect(loan.totalPrice).toBe(100);
    expect(loan.successUrl).toBe(
      `${loanUI.hostname}/success?branchId=${loanUI.branchId}`
    );
    expect(loan.errorUrl).toBe(
      `${loanUI.hostname}/secured/error-page?branchId=${loanUI.branchId}`
    );
    expect(loan.sellsAgentId).toBe(1);
  });
});
