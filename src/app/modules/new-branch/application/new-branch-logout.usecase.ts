import { Injectable } from '@angular/core';
import { RedirectionService } from '@aplazo/merchant/shared';
import { StoreService } from '../../../core/application/services/store.service';

@Injectable({ providedIn: 'root' })
export class NewBranchLogoutUseCase {
  constructor(
    private storeService: StoreService,
    private redirectionService: RedirectionService
  ) {}

  public execute(): void {
    this.storeService.clearStore();
    this.redirectionService.internalNavigation('/');
  }
}
