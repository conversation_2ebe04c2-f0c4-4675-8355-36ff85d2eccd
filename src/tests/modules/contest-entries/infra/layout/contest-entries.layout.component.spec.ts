import { provideHttpClient } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { Component } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { provideRouter } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { AnalyticsService } from 'src/app/core/application/services/analytics.service';
import {
  AplazoContestEntriesLayoutComponent,
  contestLabelRoutes,
} from 'src/app/modules/contest-entries/infra/layout/contest-entries.layout.component';

@Component({
  standalone: true,
  selector: 'app-test',
  template: '<div>Test</div>',
})
class TestComponent {}

const routeDefinitions = Object.entries(contestLabelRoutes).map(
  ([label, path]) => ({
    label,
    path,
  })
);

const routePaths = routeDefinitions.map(({ path }) => path);
const routeLabels = routeDefinitions.map(({ label }) => label);

const setup = async () => {
  TestBed.configureTestingModule({
    providers: [
      {
        provide: AnalyticsService,
        useValue: jasmine.createSpyObj('AnalyticsService', ['track']),
      },
      provideRouter([
        {
          path: '',
          component: AplazoContestEntriesLayoutComponent,
          children: [
            ...routePaths.map(path => ({
              path,
              component: TestComponent,
            })),
          ],
        },
      ]),
      provideHttpClient(),
      provideHttpClientTesting(),
    ],
  });

  const routerHarness = await RouterTestingHarness.create('/');
  const fixture = routerHarness.fixture;

  fixture.detectChanges();

  const layoutElement = fixture.debugElement.query(
    By.directive(AplazoContestEntriesLayoutComponent)
  );

  const analyticsService = TestBed.inject(
    AnalyticsService
  ) as jasmine.SpyObj<AnalyticsService>;

  return {
    routerHarness,
    analyticsService,
    layoutElement,
  };
};

describe('AplazoContestEntriesLayoutComponent', () => {
  it('should create', async () => {
    const { layoutElement } = await setup();

    expect(layoutElement).toBeDefined();
  });

  it('should have 2 tabs', async () => {
    const { layoutElement } = await setup();

    const links = layoutElement.componentInstance.links;

    expect(links.length).toBe(2);
  });

  it('should show the position tab', async () => {
    const { layoutElement } = await setup();

    const links = layoutElement.componentInstance.links;

    expect(links).toEqual([...routeLabels]);
  });

  it('should call changeTab when the tab changes', async () => {
    const { layoutElement } = await setup();
    const spy = spyOn(layoutElement.componentInstance, 'changeTab');

    const tabGroup = layoutElement.query(By.css('aplz-ui-tab-group'));

    tabGroup.triggerEventHandler('tabSelectionChange', { index: 1 });

    expect(spy).toHaveBeenCalledWith({ index: 1 });
    expect(spy).toHaveBeenCalledTimes(1);
  });
});
