import { Injectable } from '@angular/core';
import { CanActivate, Router, UrlTree } from '@angular/router';
import { map, Observable, take } from 'rxjs';
import { StoreService } from '../application/services/store.service';

@Injectable({
  providedIn: 'root',
})
export class OnlyLoggedInGuard implements CanActivate {
  constructor(
    private storeService: StoreService,
    private router: Router
  ) {}

  public canActivate(): Observable<boolean | UrlTree> {
    return this.storeService.isLoggedIn$.pipe(
      take(1),
      map(isLoggedIn => {
        if (!isLoggedIn) {
          this.storeService.clearStore();
          return this.router.parseUrl('/');
        }
        return true;
      })
    );
  }
}
