import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { lastValueFrom, take } from 'rxjs';
import { StoreService } from '../application/services/store.service';
import { isValidByRole } from '../domain/user-role';

export const hasValidRoleGuard: CanActivateFn = async () => {
  const store = inject(StoreService);
  const router = inject(Router);

  const role = await lastValueFrom(store.userRole$.pipe(take(1)));

  if (!isValidByRole(role)) {
    store.clearStore();
    return router.parseUrl('/');
  }

  return true;
};
