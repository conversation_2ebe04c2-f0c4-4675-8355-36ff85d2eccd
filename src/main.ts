import { createApplication } from '@angular/platform-browser';
import { catchError, defer, map, of, switchMap } from 'rxjs';
import { ajax } from 'rxjs/ajax';
import { posEnvironmentCore } from './app-core/domain/environments';
import { LiteAppComponent } from './app-lite/app-lite.component';
import { liteAppConfig } from './app-lite/app-lite.config';
import { AppComponent } from './app/app.component';
import { appConfig } from './app/app.config';

ajax
  .getJSON<{ allowedUA: boolean }>(posEnvironmentCore.uaUrl)
  .pipe(
    catchError((e: unknown) => {
      console.error('UA_AGENT_FETCH::ERROR::', e);

      return of({ allowedUA: true });
    }),

    switchMap(ua => {
      if (!ua.allowedUA) {
        return defer(() => createApplication(liteAppConfig)).pipe(
          map(app => ({
            name: 'lite',
            app,
          }))
        );
      }

      return defer(() => createApplication(appConfig)).pipe(
        map(app => ({
          name: 'app',
          app,
        }))
      );
    }),

    map(({ name, app }) => {
      if (name === 'lite') {
        return app.bootstrap(LiteAppComponent, '#aplazo-app-root');
      }

      return app.bootstrap(AppComponent, '#aplazo-app-root');
    }),

    catchError(err => {
      console.error(`OutsideAPP::ERROR::${err}`);

      throw err;
    })
  )
  .subscribe();
