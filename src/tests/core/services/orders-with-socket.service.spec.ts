import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import {
  BrowserSessionStorageService,
  ConnectionStatusService,
  JwtDecoderService,
} from '@aplazo/merchant/shared';
import { provideNotifierTesting } from '@aplazo/merchant/shared-testing';
import { Subject, of, throwError } from 'rxjs';
import { tap } from 'rxjs/operators';
import { StoreService } from 'src/app/core/application/services/store.service';
import { OrdersWithSocketService } from 'src/app/core/services/orders-with-socket.service';
import { POS_ENVIRONMENT_CORE } from '../../../app-core/domain/environments';

describe('OrdersWithSocketService - Payment Error Handling', () => {
  let mockPosOfflineService: any;
  let mockRefresher$: Subject<void>;
  let mockStoreService: any;

  beforeEach(() => {
    mockPosOfflineService = {
      checkOrderStatus: jasmine
        .createSpy('checkOrderStatus')
        .and.returnValue(of(true)),
    };
    mockRefresher$ = new Subject<void>();
    mockStoreService = {
      getToken: () => 'test-token',
      token$: of('test-token'),
      merchantConfig$: of(null),
      decodedToken$: of(null),
      selectedBranch$: of(null),
      isLoggedIn$: of(true),
      userRole$: of('ROLE_MERCHANT'),
      merchantName$: of('Test Merchant'),
      integrationType$: of('POSUI'),
      selectedBranchName$: of(null),
      selectedBranchShareLinkFlags$: of(null),
      hasBranchShareLinkEnabled$: of(false),
      selectedBranchFeatureFlags$: of(null),
      hasBranchCreationEnabled$: of(false),
      branches$: of([]),
      hasBranches$: of(false),
      minimumOrderAmount$: of(null),
      merchantLogoUrl$: of(null),
      clearStore: () => {},
      setStoreFromSessionStorage: () => Promise.resolve(),
      setMerchantName: () => Promise.resolve(),
      getMerchantId$: () => of(null),
      setMerchantConfig: () => {},
      setToken: () => {},
      setDecodedToken: () => {},
      setSelectedBranch: () => {},
    };
  });

  it('should emit paymentFailure$ when a payment error occurs', fakeAsync(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        OrdersWithSocketService,
        { provide: 'PosOfflineService', useValue: mockPosOfflineService },
        { provide: 'Refresher$', useValue: mockRefresher$ },
        {
          provide: POS_ENVIRONMENT_CORE,
          useValue: {
            posWsUrl: 'wss://test.com',
            posApiUrl: 'https://test.com',
            paymentsCoreApiUrl: 'https://test.com',
            appName: 'test',
          },
        },
        { provide: StoreService, useValue: mockStoreService },
        { provide: JwtDecoderService, useValue: {} },
        { provide: BrowserSessionStorageService, useValue: {} },
        {
          provide: ConnectionStatusService,
          useValue: {
            status$: of({ isOnline: true, isOffline: false }),
          },
        },
        provideNotifierTesting(),
      ],
    });
    const service = TestBed.inject(OrdersWithSocketService);

    service.paymentFailure$.subscribe(result => {
      expect(result).toEqual({
        branchId: '36',
        cashierMessage: 'Ocurrió un error inesperado. Intentar nuevamente.',
        code: '1014',
        createdAt: '2025-05-27T20:56:45.017Z',
        customerId: 186592,
        loanId: 186592,
        merchantId: 199,
      });
    });
    mockRefresher$.next();
    tick();
  }));

  it('should not retry if error is clean', fakeAsync(() => {
    const mockService = {
      refreshOrdersList: jasmine.createSpy('refreshOrdersList'),
      paymentFailure$: throwError(() => ({ wasClean: true })).pipe(
        tap({
          error: err =>
            console.error('Mock clean paymentFailure$ errored:', err),
          complete: () => console.log('Mock clean paymentFailure$ completed'),
        })
      ),
    } as any;

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        { provide: OrdersWithSocketService, useValue: mockService },
        { provide: 'PosOfflineService', useValue: mockPosOfflineService },
        { provide: 'Refresher$', useValue: mockRefresher$ },
        {
          provide: POS_ENVIRONMENT_CORE,
          useValue: {
            posWsUrl: '',
            posApiUrl: '',
            paymentsCoreApiUrl: '',
            appName: '',
          },
        },
        { provide: StoreService, useValue: mockStoreService },
        { provide: JwtDecoderService, useValue: {} },
        { provide: BrowserSessionStorageService, useValue: {} },
        {
          provide: ConnectionStatusService,
          useValue: {
            status$: of({ isOnline: true, isOffline: false }),
          },
        },
        provideNotifierTesting(),
      ],
    });
    const service = TestBed.inject(OrdersWithSocketService);

    let finalResult: any = null;
    let errored = false;
    let receivedError: any = null;
    let completionCalled = false;

    service.paymentFailure$.subscribe({
      next: (result: any) => {
        finalResult = result;
        console.log(
          'Next emitted by paymentFailure$ (clean error test):',
          result
        );
      },
      error: (err: { wasClean: boolean }) => {
        errored = true;
        receivedError = err;
        console.error('Stream errored during clean error test:', err);
      },
      complete: () => {
        completionCalled = true;
        console.log('Stream completed during clean error test');
      },
    });

    tick();

    expect(finalResult).toBeNull();
    expect(errored).toBeTrue();
    expect(receivedError?.wasClean).toBeTrue();
    expect(completionCalled).toBeFalse();
  }));
});
