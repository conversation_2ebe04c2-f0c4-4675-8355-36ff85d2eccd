<aplz-ui-card>
  <div class="sm:w-2/3 mx-auto" *ngIf="textUI$ | async as textUI; else loading">
    <ng-container
      *ngTemplateOutlet="
        currentContent;
        context: { $implicit: textUI }
      "></ng-container>
  </div>
</aplz-ui-card>

<ng-template #loading>
  <div class="flex items-center justify-center py-6">
    <aplz-ui-icon name="spin-circle" size="xl" [spin]="true"></aplz-ui-icon>
  </div>
</ng-template>
<ng-template #priceForm let-textUI>
  <h4 class="font-medium text-xl text-center my-6">
    {{ textUI.title }}
  </h4>

  <form
    class="flex flex-col gap-y-6"
    [formGroup]="formGroup"
    (ngSubmit)="openConfirmation()">
    <aplz-ui-form-field>
      <aplz-ui-form-label>
        {{ textUI.priceControl.label }}
      </aplz-ui-form-label>
      <input
        type="text"
        aplzFormInput
        formControlName="price"
        [placeholder]="textUI.priceControl.placeholder"
        mask="separator.2"
        thousandSeparator=","
        prefix="$"
        inputmode="decimal" />

      <p aplzFormError *ngIf="priceControl.hasError('required')">
        {{ textUI.priceControl.errorRequired }}
      </p>
      <p
        aplzFormError
        *ngIf="
          !priceControl.hasError('required') && priceControl.hasError('min')
        ">
        {{ textUI.priceControl.errorRequired }}
      </p>
    </aplz-ui-form-field>

    <aplazo-agent-id
      *ngIf="(isSellAgentIdEnable$ | async) === true"
      [injectedText]="textUI.agentId"
      [formControl]="agentIdControl"></aplazo-agent-id>

    <button
      aplzButton
      aplzAppearance="solid"
      aplzColor="dark"
      size="md"
      type="submit"
      [fullWidth]="true"
      [disabled]="!isValidForm">
      {{ textUI.formActions.confirm }}
    </button>
  </form>
</ng-template>

<ng-template #confirmMessage let-textUI>
  <article class="p-4">
    <h4 class="font-medium text-xl text-center mt-6 mb-8">
      <span>
        {{ textUI.confirmDialog.questionFirst }}
      </span>
      <span class="text-accent-primary font-bold">
        {{ price | aplzDynamicPipe: 'currency' }}
      </span>
      <span> {{ textUI.confirmDialog.questionSecond }} </span>
    </h4>

    <div class="flex justify-center gap-x-4 my-4">
      <button
        aplzButton
        aplzAppearance="basic"
        aplzColor="light"
        size="md"
        (click)="save()">
        {{ textUI.confirmDialog.actions.cancel }}
      </button>

      <button
        aplzButton
        aplzAppearance="solid"
        aplzColor="dark"
        size="md"
        [disabled]="!isValidForm"
        (click)="save(true)">
        {{ textUI.confirmDialog.actions.confirm }}
      </button>
    </div>
  </article>
</ng-template>
