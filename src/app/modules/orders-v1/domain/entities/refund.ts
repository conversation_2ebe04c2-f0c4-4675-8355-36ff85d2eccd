export const REFUND_STATUS = ['REQUESTED', 'REFUNDED', 'REJECTED'] as const;

export type RefundStatus = (typeof REFUND_STATUS)[number];

export const REFUND_TYPES = ['R', 'RP'] as const;

export type RefundType = (typeof REFUND_TYPES)[number];

export class Refund {
  public readonly statusLoan: string;
  public readonly statusRefund: RefundStatus;
  public readonly statusPayment: string;
  public readonly reason: string;
  public readonly created: string;
  public readonly cartId: string;
  public readonly loanId: number;
  public readonly merchantCancelId: number;
  public readonly refundAmount: number;
  public readonly saleAmount: number;
  public readonly requesType: RefundType;
}
