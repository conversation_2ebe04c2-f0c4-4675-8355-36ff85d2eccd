import { inject, Injectable } from '@angular/core';
import { BehaviorSubject, map, Observable } from 'rxjs';
import {
  INotificationsWithNewOnesUIDto,
  NotificationType,
  NotificationUI,
} from '../domain/notification';
import { BrowserLocalStorageService } from '@aplazo/merchant/shared';

@Injectable({ providedIn: 'root' })
export class NotificationsStore {
  readonly #browserStorage = inject(BrowserLocalStorageService);

  readonly #localStorageNotificationsKey = 'AplazoReadedNotifications';

  readonly #notifications$ = new BehaviorSubject<
    INotificationsWithNewOnesUIDto[]
  >([]);

  setNotifications(notifications: INotificationsWithNewOnesUIDto[]): void {
    this.#notifications$.next(
      Array.isArray(notifications) ? notifications : []
    );
  }

  hasNewNotifications$(): Observable<boolean> {
    return this.#notifications$.pipe(
      map(notifications =>
        notifications.some(notification => {
          if (!notification) {
            return false;
          }

          return notification?.hasNewOnes;
        })
      )
    );
  }

  newNotificationsCounter$(): Observable<number> {
    return this.#notifications$.pipe(
      map(notifications =>
        notifications.reduce((acc, notification) => {
          if (!notification) {
            return acc;
          }

          return acc + notification.newOnesCounter;
        }, 0)
      )
    );
  }

  newNotificationsByTypeCounter$(): Observable<
    Array<{
      type: NotificationType;
      counter: number;
      ids: number[];
    } | null>
  > {
    return this.#notifications$.pipe(
      map(notifications =>
        notifications.map(notification => {
          if (!notification) {
            return null;
          }

          return {
            type: notification.type,
            counter: notification.newOnesCounter,
            ids: notification.newOnesIds,
          };
        })
      )
    );
  }

  markAllAsRead(type: NotificationType): void {
    const notifications = this.#notifications$.getValue().slice();

    const currentIdx = notifications.findIndex(i => i.type === type);

    if (currentIdx === -1) {
      return;
    }

    const current = { ...notifications[currentIdx] };

    const rawStored = this.#browserStorage.getItem(
      this.#localStorageNotificationsKey
    );
    let stored: { [key: string]: boolean } = {};
    try {
      if (rawStored) {
        stored = JSON.parse(rawStored);
      }
    } catch (error) {
      console.error('Error parsing stored notifications', error);
    }
    current.newOnesIds.forEach(id => {
      if (!stored[id]) {
        stored[id] = true;
      }
    });
    this.#browserStorage.setItem(
      this.#localStorageNotificationsKey,
      JSON.stringify(stored)
    );
    current.newOnesIds = [];
    current.newOnesCounter = 0;
    current.hasNewOnes = false;

    notifications.splice(currentIdx, 1, current);

    this.#notifications$.next(notifications);
  }

  getNotificationsByType$(
    type: NotificationType
  ): Observable<Map<string, NotificationUI[]>> {
    return this.#notifications$.pipe(
      map(notifications => {
        const notification = notifications.find(item => item?.type === type);

        if (!notification) {
          return new Map<string, NotificationUI[]>();
        }

        return notification.notifications;
      })
    );
  }
}
