import { Injectable, inject } from '@angular/core';
import { DialogService } from '@ngneat/dialog';
import { AplazoPopupComponent } from './aplazo-popup.component';
import { Observable, of } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class AplazoPopupDialogService {
  private dialog = inject(DialogService);

  /**
   * Abre el diálogo con el HTML del popup.
   * @param htmlContent - Contenido HTML del popup
   * @returns Observable que emite cuando el diálogo se cierra con información de dismiss
   */
  openPopup(
    htmlContent: string
  ): Observable<{ permanentDismiss: boolean } | null> {
    console.log(
      '🎭 [AplazoPopupDialogService] Opening popup with HTML content, length:',
      htmlContent.length
    );

    try {
      // Validar que el servicio de diálogo esté disponible
      if (!this.dialog) {
        console.error(
          '❌ [AplazoPopupDialogService] Dialog service not available'
        );
        return of(null);
      }

      // ✅ ADDED: Verify DialogService is properly injected
      if (typeof this.dialog.open !== 'function') {
        console.error(
          '❌ [AplazoPopupDialogService] Dialog service open method not available'
        );
        return of(null);
      }

      const dialogRef = this.dialog.open(AplazoPopupComponent, {
        data: { htmlContent },
        maxWidth: '600px',
        enableClose: true,
      });

      // Validar que el dialogRef se creó correctamente
      if (!dialogRef) {
        console.error(
          '❌ [AplazoPopupDialogService] Failed to create dialog reference'
        );
        return of(null);
      }

      // Validar que afterClosed$ esté disponible
      if (!dialogRef.afterClosed$) {
        console.error(
          '❌ [AplazoPopupDialogService] afterClosed$ not available on dialog reference'
        );
        return of(null);
      }

      console.log('🎭 [AplazoPopupDialogService] Dialog opened successfully');

      return dialogRef.afterClosed$.pipe(
        tap(result => {
          console.log(
            '🎭 [AplazoPopupDialogService] Dialog closed with result:',
            result
          );
        }),
        catchError(error => {
          console.error(
            '❌ [AplazoPopupDialogService] Error in dialog stream:',
            error
          );
          return of(null);
        })
      );
    } catch (error) {
      console.error(
        '❌ [AplazoPopupDialogService] Error opening popup:',
        error
      );
      return of(null);
    }
  }

  /**
   * Método legacy para compatibilidad - ahora usa openPopup directamente
   * @deprecated Use openPopup(htmlContent) instead
   */
  openPopupForBranch() {
    console.warn(
      'openPopupForBranch is deprecated. Use openPopup(htmlContent) instead.'
    );
    // Por ahora, mostrar un popup de prueba
    this.openPopup(
      '<h2>Popup de prueba</h2><p>Este es un popup de prueba.</p>'
    );
  }
}
