import { TestBed } from '@angular/core/testing';
import { lastValueFrom, of, take, timer, VirtualTimeScheduler } from 'rxjs';
import { RefreshLoginUsecase } from '../../../app/core/application/refresh-login.usecase';
import { StoreService } from '../../../app/core/application/services/store.service';
import { IUserJwt } from '../../../app/core/domain/access-token';

const setup = (token: Partial<IUserJwt> | null = null, timeout = 1) => {
  TestBed.configureTestingModule({
    providers: [
      {
        provide: StoreService,
        useValue: {
          decodedToken$: of(token),
          setStoreFromSessionStorage: async () => {
            await lastValueFrom(timer(timeout));
          },
        },
      },
      RefreshLoginUsecase,
    ],
  });

  const usecase = TestBed.inject(RefreshLoginUsecase);

  return { usecase };
};

describe('RefreshLoginUsecase', () => {
  it('should return default values when token is null', async () => {
    const { usecase } = setup(null);

    const result = await lastValueFrom(usecase.execute().pipe(take(1)));

    expect(result)
      .withContext('should return default values when token is null')
      .toEqual({ merchantId: 0, isLoggedIn: false });
  });

  it('should return the merchantId and loggedIn when token is valid', async () => {
    const merchantId = 1234;
    const { usecase } = setup({ merchantId });

    const result = await lastValueFrom(usecase.execute().pipe(take(1)));

    expect(result)
      .withContext(
        'should return the merchantId and loggedIn when token is valid'
      )
      .toEqual({ merchantId, isLoggedIn: true });
  });

  it('should handle timeout from sessionStorage', done => {
    const merchantId = 1234;
    const schd = new VirtualTimeScheduler();

    const { usecase } = setup({ merchantId }, 20000);

    usecase
      .execute(schd)
      .pipe(take(1))
      .subscribe(result => {
        expect(result)
          .withContext(
            'should return the merchantId and loggedIn when token is valid'
          )
          .toEqual({ merchantId: 0, isLoggedIn: false });
        done();
      });

    schd.flush();
  });
});
