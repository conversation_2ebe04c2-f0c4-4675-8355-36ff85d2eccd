import { Inject, Injectable } from '@angular/core';
import {
  LoaderService,
  NotifierService,
  RedirectionService,
} from '@aplazo/merchant/shared';
import { catchError, filter, map, take, tap, throwError } from 'rxjs';
import { StoreService } from '../../../core/application/services/store.service';
import {
  POS_APP_ROUTES,
  RouteConfig,
} from '../../../core/domain/config/app-routes.core';
import { UpdateMerchantConfigDTO } from '../../../core/domain/dtos';
import { MerchantsService } from '../../../core/services/merchants.service';

@Injectable({ providedIn: 'root' })
export class NewBranchAddUseCase {
  constructor(
    private merchantsService: MerchantsService,
    private redirectionService: RedirectionService,
    private notifier: NotifierService,
    private storeService: StoreService,
    private loader: LoaderService,
    @Inject(POS_APP_ROUTES)
    private appRoutes: RouteConfig
  ) {}

  execute(branch: UpdateMerchantConfigDTO): void {
    const idLoader = this.loader.show();

    try {
      this.merchantsService
        .updateMerchantBranch(branch)
        .pipe(
          map(config => config.branches || []),
          filter(branches => branches.length > 0),
          tap(branches => {
            const [createdBranch] = branches;

            this.storeService.setSelectedBranch(createdBranch);
          }),

          tap(() => {
            this.notifier.success({
              title: 'Tienda creada satisfactoriamente',
            });
          }),
          take(1),
          catchError(error => throwError(() => error))
        )
        .subscribe(() => {
          this.redirectionService.internalNavigation([
            this.appRoutes.aplazoRoot,
          ]);
        });
    } catch (error) {
      console.error(error);

      this.notifier.error({
        title: 'Ups! Algo salió mal',
        message: 'No se pudo agregar la sucursal',
      });
    } finally {
      this.loader.hide(idLoader);
    }
  }
}
