import { NgIf } from '@angular/common';
import { Component, ElementRef, ViewChild, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { DialogRef } from '@ngneat/dialog';
import { nanoid } from 'nanoid';
import { NgxMaskDirective } from 'ngx-mask';
import { IProductRequestUIDto } from '../../../core/domain/product-request';

export interface IProductFormInjectedTextUI {
  title: string;
  productId?: string;
  product: {
    label: string;
    placeholder: string;
    errorRequired: string;
    errorMaxLength: string;
  };
  sku: {
    label: string;
    placeholder: string;
    errorRequired: string;
  };
  price: {
    label: string;
    placeholder: string;
    errorRequired: string;
    errorMin: string;
  };
  quantity: {
    label: string;
    placeholder: string;
    errorRequired: string;
    errorMin: string;
  };
  actions: {
    cancel: string;
    confirm: string;
  };
}

@Component({
  standalone: true,
  selector: 'aplazo-product-form',
  imports: [
    NgIf,
    ReactiveFormsModule,
    NgxMaskDirective,
    AplazoCardComponent,
    AplazoFormFieldDirectives,
    AplazoButtonComponent,
  ],
  templateUrl: './product-form.component.html',
})
export class ProductFormComponent {
  readonly #dialogRef: DialogRef<
    {
      injectedText: IProductFormInjectedTextUI;
      product?: IProductRequestUIDto;
    },
    IProductRequestUIDto
  > = inject(DialogRef);

  data = this.#dialogRef.data;

  @ViewChild('skuInput', { static: false })
  set focusElement(element: ElementRef<HTMLInputElement>) {
    if (element) {
      element.nativeElement.focus();
    }
  }

  skuControl = new FormControl<string | null>(
    this.#dialogRef.data?.product?.sku ?? null,
    [Validators.required]
  );
  productControl = new FormControl<string | null>(
    this.#dialogRef.data?.product?.name ?? null,
    [Validators.required, Validators.maxLength(255)]
  );
  priceControl = new FormControl<number | null>(
    this.#dialogRef.data?.product?.price
      ? parseInt(this.#dialogRef.data?.product?.price)
      : null,
    [Validators.required, Validators.min(1)]
  );
  quantityControl = new FormControl<number | null>(
    this.#dialogRef.data?.product?.quantity ?? null,
    [Validators.required, Validators.min(1)]
  );

  formGroup = new FormGroup({
    sku: this.skuControl,
    name: this.productControl,
    price: this.priceControl,
    quantity: this.quantityControl,
  });

  get isValidForm(): boolean {
    return this.formGroup.valid && this.formGroup.dirty;
  }

  addProductToCart(): void {
    this.formGroup.markAllAsTouched();
    if (this.formGroup.valid && this.formGroup.dirty) {
      const productId = this.#dialogRef.data?.product?.productId
        ? this.#dialogRef.data?.product.productId
        : nanoid();

      const price = this.priceControl.value
        ? String(this.priceControl.value)
        : '';

      const product: IProductRequestUIDto = {
        sku: this.skuControl.value ?? '',
        name: this.productControl.value ?? '',
        price,
        quantity: Number(this.quantityControl.value),
        productId,
      };

      this.#dialogRef.close(product);
    }
  }

  close(): void {
    this.#dialogRef.close();
  }
}
