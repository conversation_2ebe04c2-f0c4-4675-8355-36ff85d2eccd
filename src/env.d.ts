declare interface Env {
  readonly NODE_ENV: string;
  readonly NG_APP_PRODUCTION: boolean;
  readonly NG_APP_API_URL: string;
  readonly NG_APP_APP_NAME: string;
  readonly NG_APP_POS_API_URL: string;
  readonly NG_APP_POS_WS_URL: string;
  readonly NG_APP_HOSTNAME: string;
  readonly NG_APP_LANDINGPAGE: string;
  readonly NG_APP_REGISTER_MERCHANT_URL: string;
  readonly NG_APP_CUSTOMER_LOGIN_URL: string;
  readonly NG_APP_AUTH_API_URL: string;
  readonly NG_APP_GTM_ID: string;
  readonly NG_APP_PROMO_API_URL: string;
  readonly NG_APP_FEATURE_FLAGS_API_KEY: string;
  readonly NG_APP_FEATURE_FLAGS_ENV: string;
  readonly NG_APP_DATADOG_APPLICATION_ID: string;
  readonly NG_APP_DATADOG_CLIENT_TOKEN: string;
  readonly NG_APP_DATADOG_ENV: string;
  readonly NG_APP_DATADOG_SERVICE: string;
  readonly NG_APP_MERCHANT_DASH_API_URL: string;
  readonly NG_APP_WEBCHAT_API_KEY: string;
  readonly NG_APP_WEBCHAT_BRAND_ID: string;
  readonly NG_APP_PAYMENTS_CORE_API_URL: string;
  readonly NG_APP_MSPAYMENT_MICRO: string;
  readonly NG_APP_KOUNT_CLIENT_ID: string;
  readonly NG_APP_KOUNT_ENVIRONMENT: string;
  readonly NG_APP_MS_MERCHANT_MICRO: string;
  readonly NG_APP_BIFROST_URL: string;
  readonly NG_APP_CUSTOMER_REGISTRATION_MICRO: string;
  readonly NG_APP_EXPOSED_PUBLIC_API_URL: string;
  readonly NG_APP_UA_URL: string;
  readonly NG_APP_I18N_URL: string;
}

declare interface ImportMeta {
  readonly env: Env;
}
