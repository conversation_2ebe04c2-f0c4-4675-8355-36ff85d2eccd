import { inject, Injectable } from '@angular/core';
import { ConnectionStatusService } from '@aplazo/merchant/shared';
import {
  audit,
  catchError,
  EMPTY,
  map,
  merge,
  MonoTypeOperatorFunction,
  Observable,
  of,
  pipe,
  retry,
  share,
  startWith,
  Subject,
  switchMap,
  take,
  tap,
  timer,
} from 'rxjs';
import {
  webSocket,
  WebSocketSubject,
  WebSocketSubjectConfig,
} from 'rxjs/webSocket';
import { POS_ENVIRONMENT_CORE } from '../../../app-core/domain/environments';
import { StoreService } from '../application/services/store.service';
import { ORDER_MESSAGES } from '../domain/order-message-type.enum';
import { PAYMENT_ERROR_MESSAGES } from '../domain/paymnet-error-message-type.enum';
import {
  ErrorResponseDto,
  Order,
  OrdersResponseDto,
  PaymentFailureEventBody,
} from '../domain/order.interface';
import { PosOfflineService } from './pos-offline.service';

@Injectable({
  providedIn: 'root',
})
export class OrdersWithSocketService {
  readonly #environment = inject(POS_ENVIRONMENT_CORE);
  readonly #store = inject(StoreService);
  readonly #connectionStatusService = inject(ConnectionStatusService);
  readonly #paymentFailureSubject = new Subject<void>();
  readonly #posOfflineService = inject(PosOfflineService);

  readonly paymentFailure$ = this.#paymentFailureSubject.pipe(
    startWith(void 0),
    this.#emitLastWhenOnline(),
    switchMap(() => this.#handlePaymentErrors())
  );

  readonly #url = `${this.#environment.posWsUrl}/ws?auth=${
    this.#store.getToken() || ''
  }`;
  readonly #socketConfig: WebSocketSubjectConfig<OrdersResponseDto> = {
    url: this.#url,
  };
  readonly #socket: WebSocketSubject<OrdersResponseDto> = webSocket(
    this.#socketConfig
  );

  readonly #socketErrorConfig: WebSocketSubjectConfig<ErrorResponseDto> = {
    url: this.#url,
  };
  readonly #socketError: WebSocketSubject<ErrorResponseDto> = webSocket(
    this.#socketErrorConfig
  );

  readonly #refresher$ = new Subject<void>();

  readonly orders$ = this.#refresher$.pipe(
    startWith(void 0),

    this.#emitLastWhenOnline(),

    switchMap(() =>
      merge(this.#retrieveOrders(), this.#expectedEventHandler())
    ),
    switchMap(r => {
      if (!Array.isArray(r.body)) {
        this.refreshOrdersList();
        return EMPTY;
      }

      return of(r);
    }),
    map(r => r?.body ?? []),
    map(orders => orders.sort((a, b) => b.loanId - a.loanId)),

    this.#retryOnSocketError<Order[]>(),

    share()
  );

  refreshOrdersList(): void {
    this.#refresher$.next();
  }

  #emitLastWhenOnline(): MonoTypeOperatorFunction<void> {
    return pipe(
      audit(() => {
        return this.#connectionStatusService.status$.pipe(map(s => s.isOnline));
      })
    );
  }

  #retrieveOrders(): Observable<OrdersResponseDto> {
    return this.#socket.multiplex(
      () => {
        // Sent a message on initial connection
        const branchOrNull =
          sessionStorage.getItem('APLAZO_currentBranch') ?? null;

        const branchId = branchOrNull ? JSON.parse(branchOrNull).id : 0;

        return {
          connectionInit: branchId,
        };
      },
      () => {
        // Sent a message on final disconnection
        const branchOrNull =
          sessionStorage.getItem('APLAZO_currentBranch') ?? null;

        const branchId = branchOrNull ? JSON.parse(branchOrNull).id : 0;

        return {
          connectionFinalize: branchId,
        };
      },
      message => {
        return message.type === ORDER_MESSAGES.TODAY_ORDERS;
      }
    );
  }

  #expectedEventHandler(): Observable<OrdersResponseDto> {
    return this.#socket.multiplex(
      () => {
        return {
          heartbeat: 0,
        };
      },
      () => {
        return {
          heartbeat: 0,
        };
      },
      message => {
        return (
          message.type === ORDER_MESSAGES.UPDATED_ORDER ||
          message.type === ORDER_MESSAGES.DELETED_ORDER ||
          message.type === ORDER_MESSAGES.NEW_ORDER ||
          message.type === ('' as any)
        );
      }
    );
  }

  #retryOnSocketError<T>(): MonoTypeOperatorFunction<T> {
    return pipe(
      retry({
        resetOnSuccess: true,
        delay: err => {
          if (err?.wasClean) {
            return EMPTY;
          }
          return timer(1000).pipe(take(1));
        },
      })
    );
  }

  #paymentErrorHandler(): Observable<any> {
    return this.#socketError.multiplex(
      () => {
        return {
          heartbeat: 0,
        };
      },
      () => {
        return {
          heartbeat: 0,
        };
      },
      message => {
        return (
          message.type ===
            PAYMENT_ERROR_MESSAGES.FIRST_INSTALLMENT_PAYMENT_FAILED ||
          message.type === PAYMENT_ERROR_MESSAGES.FIRST_INSTALLMENT_FAILED ||
          message.type === ('' as any)
        );
      }
    );
  }

  #handlePaymentErrors(): Observable<PaymentFailureEventBody> {
    return this.#paymentErrorHandler().pipe(
      map(error => error.body),
      this.#retryOnSocketError<PaymentFailureEventBody>(),
      tap(e => {
        this.#refresher$.next();
        this.#posOfflineService
          .checkOrderStatus(e.loanId)
          .pipe(
            catchError(err => {
              console.error('PaymentError::', err);
              return EMPTY;
            })
          )
          .subscribe();
      })
    );
  }
}
