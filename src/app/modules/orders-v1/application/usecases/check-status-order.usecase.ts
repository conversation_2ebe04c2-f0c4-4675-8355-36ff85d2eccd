import { Injectable } from '@angular/core';
import { LoaderService, NotifierService } from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, take, tap } from 'rxjs';
import { AnalyticsService } from '../../../../core/application/services/analytics.service';
import { Order } from '../../../../core/domain/order.interface';
import {
  CommonPosOfflineResponse,
  PosOfflineRepository,
} from '../../../../core/domain/repositories/pos-offline.repository';

@Injectable({ providedIn: 'root' })
export class CheckStatusOrderUsecase {
  constructor(
    private offlineService: PosOfflineRepository,
    private loader: LoaderService,
    private notifier: NotifierService,
    private analytics: AnalyticsService
  ) {}

  execute(order: Order): Observable<CommonPosOfflineResponse<Order>> {
    const idLoader = this.loader.show();

    this.analytics.track('customClick', {
      buttonName: 'checkOrderStatusButton',
      timestamp: new Date().getTime(),
    });

    return this.offlineService.checkOrderStatus(order.loanId).pipe(
      take(1),
      tap(({ content }) => {
        if (content && content.status !== order.status) {
          this.notifier.success({
            title: 'Orden Actualizada',
          });
        } else {
          this.notifier.warning({
            title: 'Orden sin cambios',
            message: 'Aún no se ha actualizado el estatus de la orden',
          });
        }
      }),

      catchError((error: any) => {
        console.error(error);

        this.notifier.warning({
          title: 'Algo salio mal!',
          message: 'No se pudo verificar el estatus  de la orden',
        });

        throw error;
      }),

      finalize(() => {
        this.loader.hide(idLoader);
      })
    );
  }
}
