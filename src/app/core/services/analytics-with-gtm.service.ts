import { Injectable, inject } from '@angular/core';
import { TagManagerService } from '@aplazo/front-analytics/tag-manager';
import { GtmMerchantEventDescription } from '@aplazo/front-analytics/tag-manager/application/gtm-event-merchant';
import { combineLatestWith, take, tap } from 'rxjs';
import {
  AnalyticsService,
  GTMMerchantEventName,
} from '../application/services/analytics.service';
import { StoreService } from '../application/services/store.service';

@Injectable({ providedIn: 'root' })
export class AnalyticsWithGtmService implements AnalyticsService {
  readonly #storeService = inject(StoreService);
  readonly #tagService: TagManagerService = inject(TagManagerService);

  track(
    eventName: GTMMerchantEventName,
    data?: Partial<GtmMerchantEventDescription>
  ) {
    this.#storeService.selectedBranch$
      .pipe(
        combineLatestWith(
          this.#storeService.getMerchantId$(),
          this.#storeService.merchantName$
        ),

        tap(([branch, merchantId, merchantName]) => {
          const defaultDescription = {
            genericInfo: '',
            startDate: '',
            endDate: '',
            status: '',
            searchTerm: '',
            pageNum: 0,
            pageSize: 0,
            loanId: 0,
            graphType: '',
            buttonName: '',
          };

          this.#tagService.trackEvent({
            event: eventName,
            merchantId: merchantId ?? 0,
            merchantName: merchantName ?? '',
            branchId: branch?.id ?? 0,
            branchName: branch?.name ?? '',
            description: data ? { ...defaultDescription, ...data } : undefined,
          });
        }),

        take(1)
      )
      .subscribe();
  }
}
