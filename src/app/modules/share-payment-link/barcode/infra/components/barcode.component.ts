import {
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  output,
  ViewEncapsulation,
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoDetailsComponents } from '@aplazo/shared-ui/details';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { lastValueFrom, map, take } from 'rxjs';
import { Branch } from '../../../../../core/domain/entities';
import { Order } from '../../../../../core/domain/order.interface';
import { FinishWithBarcodeUsecase } from '../../application/finish-with-barcode.usecase';

@Component({
  standalone: true,
  selector: 'app-barcode',
  template: `
    <section class="min-h-72">
      <h1 class="text-xl font-medium mt-8 mb-4">
        {{ textUI()?.title }}
      </h1>

      <aplz-ui-details>
        <summary aplzDetailsHeader>
          <h2 class="text-sm font-medium text-dark-secondary">
            {{ textUI()?.instructionTitle }}
          </h2>
        </summary>
        <ul
          class="list-disc list-inside text-sm text-dark-secondary text-pretty">
          @for (item of textUI()?.steps; track item) {
            <li class="mb-2">
              {{ item }}
            </li>
          }
        </ul>
      </aplz-ui-details>

      <form
        [formGroup]="form"
        (ngSubmit)="finish()"
        class="mt-8 mb-10 flex flex-col gap-8 w-full items-center">
        <aplz-ui-form-field class="w-full">
          <aplz-ui-form-label>
            {{ textUI()?.form?.label ?? '' }}
          </aplz-ui-form-label>
          <input
            type="text"
            aplzFormInput
            formControlName="customerId"
            [placeholder]="textUI()?.form?.placeholder ?? ''" />
          <span aplzFormHint>
            {{ textUI()?.form?.hint ?? '' }}
          </span>

          <ng-container aplzFormError>
            @if (customerId.touched && customerId.hasError('required')) {
              <p>
                {{ textUI()?.form?.required ?? '' }}
              </p>
            }
          </ng-container>
        </aplz-ui-form-field>

        <div class="flex flex-wrap gap-2 justify-end w-full">
          <button
            aplzButton
            aplzAppearance="solid"
            aplzColor="dark"
            size="md"
            [rounded]="true">
            {{ textUI()?.actions?.finish }}
          </button>
        </div>
      </form>
    </section>
  `,
  imports: [
    AplazoDetailsComponents,
    AplazoFormFieldDirectives,
    AplazoButtonComponent,

    ReactiveFormsModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class BarcodeComponent {
  readonly #i18n = inject(I18NService);
  readonly #finishWithBarCode = inject(FinishWithBarcodeUsecase);

  readonly close = output<void>();
  readonly data = input.required<{
    order: Order | null;
    branch: Branch | null;
  }>();

  readonly textUI = toSignal(
    this.#i18n
      .getTranslateObjectByKey<{
        title: string;
        steps: Record<string, string>;
        actions: Record<string, string>;
        form: Record<string, string>;
      }>({
        key: 'content',
        scope: 'barcode-payment',
      })
      .pipe(
        map(text => {
          const {
            title = '',
            steps = {},
            actions = {},
            form = {},
          } = text || {};
          const entries = Object.entries(steps);
          const finalSteps: string[] = [];
          let instructionTitle = 'Detalles';

          entries.forEach(([key, value]) => {
            const finalIndex = isNaN(Number(key)) ? -1 : Number(key);

            if (finalIndex > 0 && value) {
              finalSteps[finalIndex] = value;
            }

            if (finalIndex === 0) {
              instructionTitle = value;
            }
          });

          return {
            title,
            steps: finalSteps.filter(Boolean),
            instructionTitle,
            actions: actions ?? { title: 'Finalizar' },
            form,
          };
        })
      )
  );

  readonly customerId = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required],
  });

  readonly form = new FormGroup({
    customerId: this.customerId,
  });

  async finish(): Promise<void> {
    this.form.markAllAsTouched();

    try {
      if (this.form.valid) {
        const successMessage = await lastValueFrom(
          this.#i18n
            .getTranslateObjectByKey<{
              title: string;
            }>({
              key: 'successMessage',
              scope: 'barcode-payment',
            })
            .pipe(
              take(1),
              map(t => t.title ?? '')
            )
        );

        await lastValueFrom(
          this.#finishWithBarCode
            .execute({
              customerId: this.customerId.value,
              loanId: this.data().order?.loanId ?? null,
              successMessage,
            })
            .pipe(take(1))
        );

        this.close.emit();
        this.form.reset();
      }
    } catch (error) {
      console.warn(error);
    }
  }
}
