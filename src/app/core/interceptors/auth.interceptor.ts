import { HttpErrorResponse, HttpInterceptorFn } from '@angular/common/http';
import { inject } from '@angular/core';
import { catchError, throwError } from 'rxjs';
import { SecuredV1LogoutUsecase } from '../../modules/secured-v1/secured-v1-logout.usecase';
import { parseUrl } from '../domain/url-parser';

export const authInterceptor: HttpInterceptorFn = (request, next) => {
  const isSvgResource = request.url.match(/\.svg$|\.json$/);
  const url = parseUrl(request.url);
  const isSSE = url?.pathname.endsWith('sse') ?? false;

  if (isSvgResource || isSSE) {
    return next(request);
  }

  const logout = inject(SecuredV1LogoutUsecase);

  return next(request).pipe(
    catchError((error: HttpErrorResponse) => {
      if (error.status === 401 || error.status === 403) {
        logout.execute();
      }
      return throwError(() => error);
    })
  );
};
