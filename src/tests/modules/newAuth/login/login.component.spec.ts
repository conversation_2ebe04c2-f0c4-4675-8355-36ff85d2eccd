import { <PERSON><PERSON><PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { TagManagerService } from '@aplazo/front-analytics/tag-manager';
import { RedirectionService } from '@aplazo/merchant/shared';
import { provideRedirecterTesting } from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoLogoComponent } from '@aplazo/shared-ui/logo';
import { LoginFormComponent } from '@aplazo/shared-ui/merchant';
import { of } from 'rxjs';
import { LoginCentralizedUseCase } from 'src/app/modules/newAuth/login/application/login-centralized.usecase';
import { provideI18NTesting } from 'src/tests/i18n.local';
import {
  POS_ENVIRONMENT_CORE,
  posEnvironmentCore,
} from '../../../../app-core/domain/environments';
import {
  POS_APP_ROUTES,
  ROUTE_CONFIG,
} from '../../../../app/core/domain/config/app-routes.core';
import { Credentials } from '../../../../app/core/domain/credentials.interface';
import { LoginComponent } from '../../../../app/modules/newAuth/login/login.component';

describe('LoginComponent', () => {
  let component: LoginComponent;
  let fixture: ComponentFixture<LoginComponent>;
  let redirecter: RedirectionService;

  let usecaseSpy: jasmine.SpyObj<LoginCentralizedUseCase>;
  let internalNavSpy: jasmine.Spy;
  let externalNavSpy: jasmine.Spy;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        AplazoLogoComponent,
        LoginFormComponent,
        AplazoButtonComponent,
        AsyncPipe,
        NgIf,
      ],
      providers: [
        {
          provide: POS_APP_ROUTES,
          useValue: ROUTE_CONFIG,
        },
        {
          provide: POS_ENVIRONMENT_CORE,
          useValue: posEnvironmentCore,
        },
        provideRedirecterTesting(),
        {
          provide: TagManagerService,
          useValue: {
            trackEvent: () => {
              void 0;
            },
          },
        },
        provideI18NTesting('login'),
        {
          provide: LoginCentralizedUseCase,
          useValue: jasmine.createSpyObj('LoginCentralizedUseCase', [
            'execute',
          ]),
        },
      ],
    });
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(LoginComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    redirecter = TestBed.inject(RedirectionService);
    usecaseSpy = TestBed.inject(
      LoginCentralizedUseCase
    ) as jasmine.SpyObj<LoginCentralizedUseCase>;

    internalNavSpy = spyOn(redirecter, 'internalNavigation').and.callThrough();
    externalNavSpy = spyOn(redirecter, 'externalNavigation').and.callThrough();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should call externalNavigation when goToRegister method is execute', () => {
    component.goToRegisterMerchantRoute();

    expect(externalNavSpy).toHaveBeenCalledTimes(1);
  });

  it('should call externalNavigation when goToCustomerLogin method is execute', () => {
    component.goToCustomerLoginRoute();

    expect(externalNavSpy).toHaveBeenCalledTimes(1);
  });

  it('should call internalNavigation when goToForgotPassword method is execute', () => {
    component.goToForgotPasswordRoute();

    expect(internalNavSpy).toHaveBeenCalledTimes(1);
  });

  it('should call execute method from usecase when login is executed', fakeAsync(() => {
    usecaseSpy.execute.and.returnValue(of({ merchantId: 123 } as any));

    const testCredentials: Credentials = {
      username: '<EMAIL>',
      password: '2323',
    };

    component.login(testCredentials);

    tick();

    expect(internalNavSpy).toHaveBeenCalledTimes(1);
  }));
});
