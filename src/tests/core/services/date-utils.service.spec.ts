import { TestBed } from '@angular/core/testing';
import { DateUtilsService } from '../../../app/core/services/date-utils.service';

describe('DateUtilsService', () => {
  let service: DateUtilsService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [DateUtilsService],
    });

    service = TestBed.inject(DateUtilsService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should return today date', () => {
    const today = new Date();
    const todayDate = service.today();

    expect(todayDate).withContext('should be today').toEqual(today);
  });

  it('should return the end of the day', () => {
    const date = new Date('2021-01-01T00:00:00');
    const endOfDay = new Date('2021-01-01T23:59:59');

    const result = service.endOfDay(date);

    expect(result.getTime())
      .withContext('should be the end of the day')
      .toEqual(endOfDay.getTime() + 999);
  });

  it('should parse a date from ISO string', () => {
    const date = '2021-01-01T00:00:00';
    const parsed = service.parseFromISO(date);

    expect(parsed)
      .withContext('should be a valid date')
      .toEqual(new Date(date));
  });

  it('should return null if the date is invalid', () => {
    const parsed = service.parseFromISO('invalid date');

    expect(parsed).withContext('should be null').toBeNull();
  });

  it('should return true if the date is today or yesterday', () => {
    const today = service.today();
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    const todayResult = service.isTodayOrYesterday(today.toISOString());
    const yesterdayResult = service.isTodayOrYesterday(yesterday.toISOString());

    expect(todayResult)
      .withContext('today validation should be true')
      .toBeTrue();
    expect(yesterdayResult)
      .withContext('yesterday validation should be true')
      .toBeTrue();
  });

  it('should return false if the date is invalid', () => {
    const result = service.isTodayOrYesterday('invalid date');

    expect(result)
      .withContext('should be false when date is invalid')
      .toBeFalse();
  });

  it('should return false if the date is not today or yesterday', () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() - 2);

    const result = service.isTodayOrYesterday(tomorrow.toISOString());

    expect(result)
      .withContext('should be false when date is not today or yesterday')
      .toBeFalse();
  });

  it('should return the date in string format MMMM yyyy', () => {
    const testDate = new Date('02/02/2024');
    const expected = 'febrero 2024';

    expect(service.getMonthYear(testDate))
      .withContext('should return the date in string format MMMM yyyy')
      .toEqual(expected);
  });
});
