import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  BaseUsecase,
  Guard,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, Observable, take, tap, throwError } from 'rxjs';
import {
  NO_INTERNET_PURCHASE_ERROR_MESSAGES,
  NO_INTERNET_PURCHASE_ERRORS,
  NoInternetPurchaseError,
  NoInternetPurchaseOTPRequest,
} from '../domain/entities/no-internet-purchase';
import { NoInternetPurchaseRepository } from '../domain/repositories/no-internet-purchase.repository';

@Injectable({ providedIn: 'root' })
export class RequestOTPUseCase
  implements BaseUsecase<NoInternetPurchaseOTPRequest, Observable<void>>
{
  readonly #repository = inject(NoInternetPurchaseRepository);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);

  execute(args: NoInternetPurchaseOTPRequest): Observable<void> {
    try {
      Guard.againstInvalidNumbers(
        +args.phoneNumber,
        'RequestOTPUseCase::phoneNumber::invalid',
        'Verifica el número de teléfono'
      );

      Guard.againstInvalidNumbers(
        args.loanId,
        'RequestOTPUseCase::loanId::invalid',
        'Verifica el ID del préstamo'
      );

      if (args.phoneNumber.length !== 10) {
        throw new RuntimeMerchantError(
          'El número de teléfono debe tener 10 dígitos',
          'RequestOTPUseCase::phoneNumber::invalidLength'
        );
      }

      return this.#repository.requestOTP(args).pipe(
        take(1),

        tap(() => {
          this.#notifier.success({
            title: 'Código enviado',
            message:
              'El cliente recibirá un mensaje de texto con el código de autorización',
          });
        }),

        catchError(err => {
          if (
            err instanceof HttpErrorResponse &&
            (err.error?.code === NO_INTERNET_PURCHASE_ERRORS.ocl0001 ||
              err.error?.code === NO_INTERNET_PURCHASE_ERRORS.ocl0002 ||
              err.error?.code === NO_INTERNET_PURCHASE_ERRORS.ocl0003)
          ) {
            const key = err.error.code as NoInternetPurchaseError;

            throw new RuntimeMerchantError(
              NO_INTERNET_PURCHASE_ERROR_MESSAGES[key],
              'RequestOTPUseCase::ApzOcl::' + key
            );
          }

          if (
            err instanceof HttpErrorResponse &&
            (err.error?.code === 'APZ4412' || err.error?.error === 'KYC_CODE')
          ) {
            throw new RuntimeMerchantError(
              'Esta modalidad de pago no está disponible para este cliente',
              'RequestOTPUseCase::ApzOcl::kycCode'
            );
          }

          if (
            err instanceof HttpErrorResponse &&
            (err.error?.code === 'APZ4403' ||
              err.error?.error === 'INSUFFICIENT_CREDIT')
          ) {
            throw new RuntimeMerchantError(
              'El cliente no cuenta con el crédito suficiente para realizar la compra',
              'RequestOTPUseCase::ApzOcl::insufficientCredit'
            );
          }

          return this.#errorHandler.handle<never>(err);
        })
      );
    } catch (error) {
      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
