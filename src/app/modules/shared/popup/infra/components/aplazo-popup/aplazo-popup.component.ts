import { Component, inject, signal } from '@angular/core';
import { DialogRef } from '@ngneat/dialog';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'aplazo-popup',
  standalone: true,
  imports: [FormsModule],
  template: `
    <div
      class="w-full h-full flex items-center justify-center bg-black bg-opacity-60">
      <div
        class="bg-white rounded-lg shadow-lg p-8 min-w-[320px] max-w-xl relative">
        <button
          type="button"
          class="absolute top-2 right-2 text-gray-400 hover:text-gray-700 text-2xl font-bold focus:outline-none"
          aria-label="Cerrar"
          (click)="close()">
          &times;
        </button>

        <!-- Popup Content -->
        <div [innerHTML]="safeHtml()"></div>

        <!-- Permanent Dismiss Checkbox -->
        <div class="mt-6 pt-4 border-t border-gray-200">
          <div class="flex items-center">
            <input
              type="checkbox"
              id="permanent-dismiss"
              [(ngModel)]="permanentDismiss"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              aria-describedby="permanent-dismiss-description" />
            <label
              for="permanent-dismiss"
              class="ml-2 block text-sm text-gray-700 cursor-pointer">
              No mostrar de nuevo
            </label>
          </div>
          <p
            id="permanent-dismiss-description"
            class="mt-1 text-xs text-gray-500">
            Si marcas esta opción, no volverás a ver este mensaje.
          </p>
        </div>
      </div>
    </div>
  `,
})
export class AplazoPopupComponent {
  private dialogRef = inject(DialogRef);
  private sanitizer = inject(DomSanitizer);

  safeHtml = signal<SafeHtml>(
    this.sanitizer.bypassSecurityTrustHtml(
      this.dialogRef.data?.htmlContent || '<p>No content</p>'
    )
  );

  // ✅ NEW: Signal for permanent dismiss checkbox state
  permanentDismiss = signal<boolean>(false);

  close() {
    console.log(
      '🚀 [AplazoPopupComponent] Closing popup with permanent dismiss:',
      this.permanentDismiss()
    );

    // Close dialog with permanent dismiss information
    this.dialogRef.close({
      permanentDismiss: this.permanentDismiss(),
    });
  }
}
