import { Inject, Injectable } from '@angular/core';

import { RedirectionService } from '@aplazo/merchant/shared';
import { Observable, map, take } from 'rxjs';
import { StoreService } from '../application/services/store.service';
import { POS_APP_ROUTES, RouteConfig } from '../domain/config/app-routes.core';
import { CanActivate } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class HasBranchSelectedGuard implements CanActivate {
  constructor(
    private storeService: StoreService,
    private redirectionService: RedirectionService,
    @Inject(POS_APP_ROUTES)
    private appRoutes: RouteConfig
  ) {}

  canActivate(): Observable<boolean> {
    return this.storeService.selectedBranch$.pipe(
      take(1),
      map(selectedBranch => {
        if (!selectedBranch) {
          this.redirectionService.internalNavigation([
            this.appRoutes.securedSelectBranch,
          ]);

          return false;
        }

        return true;
      })
    );
  }
}
