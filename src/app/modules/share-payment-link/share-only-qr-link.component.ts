import { Async<PERSON>ip<PERSON>, NgIf } from '@angular/common';
import { AfterViewInit, Component, OnD<PERSON>roy, inject } from '@angular/core';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { DialogRef } from '@ngneat/dialog';
import { Subject, takeUntil } from 'rxjs';
import { Branch } from '../../core/domain/entities';
import { Order } from '../../core/domain/order.interface';
import { GetQrImageService } from './application/services/qr-image.service';

@Component({
  standalone: true,
  selector: 'aplazo-share-only-qr-link',
  imports: [AplazoCardComponent, AplazoButtonComponent, NgIf, AsyncPipe],
  template: `
    <aplz-ui-card size="md">
      <header
        class="text-center"
        *ngIf="!data.showAlternativeHeader; else alternativeHeader">
        <h4 class="text-lg">Escanea el código para pagar.</h4>

        <p class="text-dark-secondary font-light text-base">
          Orden
          {{ data.loan?.loanId }}
        </p>
      </header>

      <figure *ngIf="qrImage$ | async as imgData" class="mt-8">
        <img [src]="imgData.src" class="max-w-full text-center mx-auto mb-4" />
      </figure>

      <div class="flex justify-end mt-8">
        <button aplzButton aplzAppearance="stroked" size="md" (click)="close()">
          Cerrar
        </button>
      </div>
    </aplz-ui-card>

    <ng-template #alternativeHeader>
      <header class="text-center">
        <h4 class="text-lg">Indica el siguiente ID de compra al cliente</h4>
        <h5 class="font-medium text-lg my-4">
          {{ data.loan?.loanId }}
        </h5>

        <p class="text-dark-secondary font-light text-base">Tambien puedes</p>

        <h4 class="text-lg my-4">Escanear el siguiente código para pagar.</h4>
      </header>
    </ng-template>
  `,
})
export class ShareOnlyQrLinkComponent implements AfterViewInit, OnDestroy {
  readonly #qrImageService = inject(GetQrImageService);
  readonly #dialogRef: DialogRef<{
    branch: Branch;
    loan: Order | null;
    showAlternativeHeader?: boolean;
  }> = inject(DialogRef);
  readonly #destroy$ = new Subject<void>();

  get data() {
    return this.#dialogRef.data;
  }

  qrImage$ = this.#qrImageService
    .getLastLoanQrImage$()
    .pipe(takeUntil(this.#destroy$));

  close(): void {
    this.#dialogRef.close();
  }

  ngAfterViewInit(): void {
    this.#qrImageService.refreshImage();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }
}
