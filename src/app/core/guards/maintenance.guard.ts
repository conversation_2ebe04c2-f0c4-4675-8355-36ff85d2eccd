import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';
import { StatsigService } from '@statsig/angular-bindings';
import { ROUTE_CONFIG } from '../domain/config/app-routes.core';

export const isUnderMaintenanceGuard: CanActivateFn = (_, state) => {
  const service = inject(StatsigService);
  const router = inject(Router);

  const isMaintenanceEnabled = service.checkGate(
    'b2b_front_maintenance_screen_enabled'
  );

  const isMaintenanceScreen = state.url.includes(ROUTE_CONFIG.unavailable);

  if (isMaintenanceEnabled && !isMaintenanceScreen) {
    return router.parseUrl(`/${ROUTE_CONFIG.unavailable}`);
  }

  if (!isMaintenanceEnabled && isMaintenanceScreen) {
    return router.parseUrl(`/${ROUTE_CONFIG.authentication}`);
  }

  return true;
};
