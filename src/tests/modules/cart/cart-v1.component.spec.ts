import { Async<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import {
  discardPeriodicTasks,
  fakeAsync,
  flush,
  TestBed,
  tick,
} from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { ObservabilityService } from '@aplazo/front-observability';
import { RedirectionService } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideRedirecterTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { AplazoDynamicPipe } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoDropdownComponents } from '@aplazo/shared-ui/dropdown';
import { AplazoIconComponent } from '@aplazo/shared-ui/icon';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';
import { DialogService } from '@ngneat/dialog';
import { StatsigService } from '@statsig/angular-bindings';
import { NgxMaskDirective, provideEnvironmentNgxMask } from 'ngx-mask';
import { provideI18NTesting } from 'src/tests/i18n.local';
import {
  POS_ENVIRONMENT_CORE,
  posEnvironmentCore,
} from '../../../app-core/domain/environments';
import { AnalyticsService } from '../../../app/core/application/services/analytics.service';
import { StoreService } from '../../../app/core/application/services/store.service';
import {
  POS_APP_ROUTES,
  ROUTE_CONFIG,
} from '../../../app/core/domain/config/app-routes.core';
import { Branch } from '../../../app/core/domain/entities';
import { IProductRequestUIDto } from '../../../app/core/domain/product-request';
import { EventService } from '../../../app/core/domain/repositories/events-service';
import { PosOfflineRepository } from '../../../app/core/domain/repositories/pos-offline.repository';
import { FinishOrderUseCase } from '../../../app/modules/cart-v1/application/finish-order.usecase';
import { CartV1Component } from '../../../app/modules/cart-v1/cart-v1.component';
import { ShowPaymentLinkDialogService } from '../../../app/modules/share-payment-link/application/services/show-payment-link-dialog.service';
import { ShowOnlyLoanIdDialogUsecase } from '../../../app/modules/share-payment-link/application/show-only-loan-id-dialog.usecase';
import { ShowOnlyQrDialogUsecase } from '../../../app/modules/share-payment-link/application/show-only-qr-dialog.usecase';
import { LocalPosOfflineRepository } from '../../core/services/impl/local-pos-offline.repository';
import { LocalStore } from '../../core/services/impl/local-store.service';
import { LocalSharePaymentLinkService } from '../share-payment-link/local-share-payment-link.service';
import { LocalShowOnlyLoanId } from '../share-payment-link/local-show-only-loan-id-dialog.usecase';
import { LocalShowQrDialog } from '../share-payment-link/local-show-qr-dialog.usecase';

const setup = (args?: {
  flagResolution?: boolean;
  snapshotRoutePath?: string;
}) => {
  const defaults = {
    flagResolution: false,
    snapshotRoutePath: ROUTE_CONFIG.securedCart,
  };

  const finalConfig = {
    flagResolution: args?.flagResolution ?? defaults.flagResolution,
    snapshotRoutePath: args?.snapshotRoutePath ?? defaults.snapshotRoutePath,
  };

  TestBed.configureTestingModule({
    imports: [
      AplazoSimpleTableComponents,
      AplazoIconComponent,
      AplazoButtonComponent,
      AplazoDropdownComponents,
      AplazoTooltipDirective,
      AplazoCardComponent,
      NgIf,
      NgFor,
      NgClass,
      AsyncPipe,
      AplazoDynamicPipe,
      NgxMaskDirective,
    ],
    providers: [
      provideUseCaseErrorHandlerTesting(),
      FinishOrderUseCase,
      DialogService,
      provideEnvironmentNgxMask(),
      {
        provide: StatsigService,
        useValue: {
          checkGate: () => {
            return finalConfig.flagResolution;
          },
        },
      },
      {
        provide: StoreService,
        useClass: LocalStore,
      },
      provideLoaderTesting(),
      provideRedirecterTesting(),
      provideNotifierTesting(),
      {
        provide: PosOfflineRepository,
        useClass: LocalPosOfflineRepository,
      },
      {
        provide: ShowPaymentLinkDialogService,
        useClass: LocalSharePaymentLinkService,
      },
      {
        provide: ShowOnlyQrDialogUsecase,
        useClass: LocalShowQrDialog,
      },
      {
        provide: ShowOnlyLoanIdDialogUsecase,
        useClass: LocalShowOnlyLoanId,
      },
      {
        provide: EventService,
        useValue: jasmine.createSpyObj('EventService', ['addProduct']),
      },
      {
        provide: AnalyticsService,
        useValue: jasmine.createSpyObj('AnalyticsService', ['track']),
      },
      {
        provide: POS_ENVIRONMENT_CORE,
        useValue: posEnvironmentCore,
      },
      {
        provide: POS_APP_ROUTES,
        useValue: ROUTE_CONFIG,
      },
      {
        provide: ObservabilityService,
        useValue: jasmine.createSpyObj('ObservabilityService', [
          'sendCustomLog',
        ]),
      },
      {
        provide: ActivatedRoute,
        useValue: {
          snapshot: {
            routeConfig: {
              path: finalConfig.snapshotRoutePath,
            },
          },
        },
      },
      provideI18NTesting('cart'),
    ],
  });

  const fixture = TestBed.createComponent(CartV1Component);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  const store = TestBed.inject(StoreService);
  const finishUsecase = TestBed.inject(FinishOrderUseCase); //as SpyObj<FinishOrderUseCase>;
  const redirecter = TestBed.inject(RedirectionService);
  const dialog = TestBed.inject(DialogService);
  const flags = TestBed.inject(StatsigService);

  return {
    component,
    fixture,
    store,
    finishUsecase,
    redirecter,
    dialog,
    flags,
  };
};

describe('CartV1Component', () => {
  const testProduct: IProductRequestUIDto = {
    productId: '12343',
    quantity: 1,
    name: 'test',
    price: '200',
    sku: 'test',
  };

  it('should create', () => {
    const { component } = setup();

    expect(component).toBeTruthy();
  });

  it('should return zero as default for totalPrice', () => {
    const { component } = setup();

    const totalPrice = spyOnProperty(
      component,
      'totalPrice',
      'get'
    ).and.callThrough();

    expect(component.totalPrice).toBe(0);
    expect(totalPrice).toHaveBeenCalledTimes(1);
  });

  it('should call open method when addProductsToCart is executed', async () => {
    const { component, dialog, fixture } = setup();

    const dialogSpy = spyOn(dialog, 'open').and.callThrough();
    component.addProductsToCart();

    fixture.detectChanges();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
  });

  it('should call open method when editProduct is executed', async () => {
    const { component, dialog, fixture } = setup();
    const dialogSpy = spyOn(dialog, 'open').and.callThrough();
    component.editProduct(testProduct);

    fixture.detectChanges();

    expect(dialogSpy).toHaveBeenCalledTimes(1);
  });

  it('should call internalNavigation when goToOrdersRoute is executed', () => {
    const { component, redirecter } = setup();
    const redirectExternalSpy = spyOn(redirecter, 'internalNavigation');
    component.goToOrdersRoute();

    expect(redirectExternalSpy).toHaveBeenCalledTimes(1);
  });

  it('should call finishOrderUseCase', fakeAsync(() => {
    const { component, finishUsecase, fixture, store } = setup();

    const branch: Branch = {
      id: 1,
      name: 'test 1',
      banned: false,
      shareLinkFlags: {
        isQrAllowed: true,
        isSMSAllowed: true,
        isWhatsappAllowed: true,
      },
    };
    store.setSelectedBranch(branch);
    const finishSpy = spyOn(finishUsecase, 'execute').and.callThrough();
    component.ngOnInit();
    component.isReadyToFinishOrder = true;

    tick();
    flush();

    component.setProducts([testProduct, testProduct]);

    component.finishOrder();

    fixture.detectChanges();
    tick();
    flush();

    expect(finishSpy).toHaveBeenCalledTimes(1);

    discardPeriodicTasks();
  }));
});
