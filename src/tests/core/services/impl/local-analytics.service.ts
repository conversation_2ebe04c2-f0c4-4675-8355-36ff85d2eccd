import { GtmMerchantEventDescription } from '@aplazo/front-analytics/tag-manager';
import {
  AnalyticsService,
  GTMMerchantEventName,
} from '../../../../app/core/application/services/analytics.service';

export class LocalAnalyticsService implements AnalyticsService {
  track(
    eventName: GTMMerchantEventName,
    data?: Partial<GtmMerchantEventDescription>
  ): void {
    console.log('LocalAnalyticsService.trackEvent', eventName, data);
  }
}
