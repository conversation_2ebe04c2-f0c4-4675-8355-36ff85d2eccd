import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { of, Observable } from 'rxjs';
import {
  ShowPopupUseCase,
  ShowPopupResult,
} from 'src/app/modules/shared/popup/application/usecases/show-popup.usecase';
import {
  PopupRepository,
  PopupMeta,
} from 'src/app/modules/shared/popup/infra/repositories/popup.repository';
import { AplazoPopupDialogService } from 'src/app/modules/shared/popup/infra/components/aplazo-popup/aplazo-popup-dialog.service';
import { PopupStorageService } from 'src/app/modules/shared/popup/application/services/popup-storage.service';
import { StoreService } from 'src/app/core/application/services/store.service';

describe('ShowPopupUseCase', () => {
  const mockBranch = { id: 123, name: 'Test Branch' };
  const mockPopups: PopupMeta[] = [
    {
      popupId: 'popup1',
      priority: 1,
      startAt: '2024-01-01',
      endAt: '2024-12-31',
    },
    {
      popupId: 'popup2',
      priority: 5,
      startAt: '2024-01-01',
      endAt: '2024-12-31',
    },
    {
      popupId: 'popup3',
      priority: 3,
      startAt: '2024-01-01',
      endAt: '2024-12-31',
    },
  ];

  function setupTestBed({
    selectedBranch$ = of(mockBranch),
  }: {
    selectedBranch$?: Observable<{ id: number; name: string } | null>;
  } = {}) {
    const popupRepoSpy = jasmine.createSpyObj('PopupRepository', [
      'getAvailablePopups',
      'getPopupHtml',
    ]);
    const dialogServiceSpy = jasmine.createSpyObj('AplazoPopupDialogService', [
      'openPopup',
    ]);
    const storageServiceSpy = jasmine.createSpyObj('PopupStorageService', [
      'wasPopupShownToday',
      'markPopupAsShown',
      'isPopupPermanentlyDismissed', // ✅ NEW METHOD FOR PERMANENT DISMISS
    ]);
    const storeServiceSpy = jasmine.createSpyObj('StoreService', [], {
      selectedBranch$,
    });

    TestBed.configureTestingModule({
      providers: [
        ShowPopupUseCase,
        { provide: PopupRepository, useValue: popupRepoSpy },
        { provide: AplazoPopupDialogService, useValue: dialogServiceSpy },
        { provide: PopupStorageService, useValue: storageServiceSpy },
        { provide: StoreService, useValue: storeServiceSpy },
      ],
    });

    return {
      useCase: TestBed.inject(ShowPopupUseCase),
      popupRepo: TestBed.inject(
        PopupRepository
      ) as jasmine.SpyObj<PopupRepository>,
      dialogService: TestBed.inject(
        AplazoPopupDialogService
      ) as jasmine.SpyObj<AplazoPopupDialogService>,
      storageService: TestBed.inject(
        PopupStorageService
      ) as jasmine.SpyObj<PopupStorageService>,
      storeService: TestBed.inject(
        StoreService
      ) as jasmine.SpyObj<StoreService>,
    };
  }

  it('should return success when popup is available and shown', fakeAsync(() => {
    const { useCase, popupRepo, storageService, dialogService } =
      setupTestBed();
    const today = new Date().toISOString().split('T')[0];
    popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
    popupRepo.getPopupHtml.and.returnValue(of('<h2>Test Popup</h2>'));
    storageService.wasPopupShownToday.and.returnValue(false);
    let result: ShowPopupResult | undefined;
    useCase.execute().subscribe(r => (result = r));
    tick();
    expect(result!.success).toBe(true);
    expect(result!.popupShown).toEqual(mockPopups[1]); // Highest priority
    expect(storageService.markPopupAsShown).toHaveBeenCalledWith(
      'popup2',
      '123',
      today
    );
    expect(dialogService.openPopup).toHaveBeenCalledWith('<h2>Test Popup</h2>');
  }));

  it('should return failure when no branch is selected', fakeAsync(() => {
    const { useCase } = setupTestBed({ selectedBranch$: of(null) });
    let result: ShowPopupResult | undefined;
    useCase.execute().subscribe(r => (result = r));
    tick();
    expect(result!.success).toBe(false);
    expect(result!.reason).toBe('No branch selected');
  }));

  it('should return failure when no popups are available', fakeAsync(() => {
    const { useCase, popupRepo } = setupTestBed();
    popupRepo.getAvailablePopups.and.returnValue(of([]));
    let result: ShowPopupResult | undefined;
    useCase.execute().subscribe(r => (result = r));
    tick();
    expect(result!.success).toBe(false);
    expect(result!.reason).toBe('No popups available');
  }));

  it('should return failure when all popups already shown today', fakeAsync(() => {
    const { useCase, popupRepo, storageService } = setupTestBed();
    popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
    storageService.wasPopupShownToday.and.returnValue(true);
    let result: ShowPopupResult | undefined;
    useCase.execute().subscribe(r => (result = r));
    tick();
    expect(result!.success).toBe(false);
    expect(result!.reason).toBe('All popups already shown today');
  }));

  it('should filter out already shown popups and show highest priority remaining', fakeAsync(() => {
    const { useCase, popupRepo, storageService, dialogService } =
      setupTestBed();
    const today = new Date().toISOString().split('T')[0];
    popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
    popupRepo.getPopupHtml.and.returnValue(of('<h2>Test Popup</h2>'));
    storageService.wasPopupShownToday.and.callFake((popupId: string) => {
      return popupId === 'popup2';
    });
    let result: ShowPopupResult | undefined;
    useCase.execute().subscribe(r => (result = r));
    tick();
    expect(result!.success).toBe(true);
    expect(result!.popupShown).toEqual(mockPopups[2]); // popup3 has highest priority among remaining
    expect(storageService.markPopupAsShown).toHaveBeenCalledWith(
      'popup3',
      '123',
      today
    );
    expect(dialogService.openPopup).toHaveBeenCalledWith('<h2>Test Popup</h2>');
  }));

  it('should return true when popups are available and not shown today', fakeAsync(() => {
    const { useCase, popupRepo, storageService } = setupTestBed();
    popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
    storageService.wasPopupShownToday.and.returnValue(false);
    let hasPopups: boolean | undefined;
    useCase.hasAvailablePopups().subscribe(r => (hasPopups = r));
    tick();
    expect(hasPopups).toBe(true);
  }));

  it('should return false when no popups are available', fakeAsync(() => {
    const { useCase, popupRepo } = setupTestBed();
    popupRepo.getAvailablePopups.and.returnValue(of([]));
    let hasPopups: boolean | undefined;
    useCase.hasAvailablePopups().subscribe(r => (hasPopups = r));
    tick();
    expect(hasPopups).toBe(false);
  }));

  it('should return false when all popups already shown today', fakeAsync(() => {
    const { useCase, popupRepo, storageService } = setupTestBed();
    popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
    storageService.wasPopupShownToday.and.returnValue(true);
    let hasPopups: boolean | undefined;
    useCase.hasAvailablePopups().subscribe(r => (hasPopups = r));
    tick();
    expect(hasPopups).toBe(false);
  }));

  it('should return false when no branch is selected', fakeAsync(() => {
    const { useCase } = setupTestBed({ selectedBranch$: of(null) });
    let hasPopups: boolean | undefined;
    useCase.hasAvailablePopups().subscribe(r => (hasPopups = r));
    tick();
    expect(hasPopups).toBe(false);
  }));

  // ✅ NEW TESTS FOR PERMANENT DISMISS FUNCTIONALITY
  describe('Permanent Dismiss Functionality', () => {
    it('should not show popup when permanently dismissed', fakeAsync(() => {
      const { useCase, popupRepo, storageService } = setupTestBed();

      popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
      storageService.wasPopupShownToday.and.returnValue(false);
      storageService.isPopupPermanentlyDismissed.and.returnValue(true); // ✅ Permanently dismissed

      let result: ShowPopupResult | undefined;
      useCase.execute().subscribe(r => (result = r));
      tick();

      expect(result!.success).toBe(false);
      expect(result!.reason).toBe('No unshown popups available');
    }));

    it('should show popup when not permanently dismissed and not shown today', fakeAsync(() => {
      const { useCase, popupRepo, storageService, dialogService } =
        setupTestBed();

      popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
      popupRepo.getPopupHtml.and.returnValue(of('<h2>Test Popup</h2>'));
      storageService.wasPopupShownToday.and.returnValue(false);
      storageService.isPopupPermanentlyDismissed.and.returnValue(false); // ✅ Not permanently dismissed

      let result: ShowPopupResult | undefined;
      useCase.execute().subscribe(r => (result = r));
      tick();

      expect(result!.success).toBe(true);
      expect(result!.popupShown).toEqual(mockPopups[1]); // Highest priority popup
    }));

    it('should filter out permanently dismissed popups from available list', fakeAsync(() => {
      const { useCase, popupRepo, storageService, dialogService } =
        setupTestBed();

      popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
      popupRepo.getPopupHtml.and.returnValue(of('<h2>Test Popup</h2>'));
      storageService.wasPopupShownToday.and.returnValue(false);

      // Mock: popup2 (highest priority) is permanently dismissed, popup3 should be shown
      storageService.isPopupPermanentlyDismissed.and.callFake(
        (popupId: string) => {
          return popupId === 'popup2'; // Only popup2 is permanently dismissed
        }
      );

      let result: ShowPopupResult | undefined;
      useCase.execute().subscribe(r => (result = r));
      tick();

      expect(result!.success).toBe(true);
      expect(result!.popupShown).toEqual(mockPopups[2]); // popup3 (next highest priority)
    }));

    it('should return false in hasAvailablePopups when all popups are permanently dismissed', fakeAsync(() => {
      const { useCase, popupRepo, storageService } = setupTestBed();

      popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
      storageService.wasPopupShownToday.and.returnValue(false);
      storageService.isPopupPermanentlyDismissed.and.returnValue(true); // All permanently dismissed

      let hasPopups: boolean | undefined;
      useCase.hasAvailablePopups().subscribe(r => (hasPopups = r));
      tick();

      expect(hasPopups).toBe(false);
    }));

    it('should return true in hasAvailablePopups when some popups are not permanently dismissed', fakeAsync(() => {
      const { useCase, popupRepo, storageService } = setupTestBed();

      popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
      storageService.wasPopupShownToday.and.returnValue(false);

      // Mock: only popup1 is not permanently dismissed
      storageService.isPopupPermanentlyDismissed.and.callFake(
        (popupId: string) => {
          return popupId !== 'popup1'; // Only popup1 is available
        }
      );

      let hasPopups: boolean | undefined;
      useCase.hasAvailablePopups().subscribe(r => (hasPopups = r));
      tick();

      expect(hasPopups).toBe(true);
    }));

    it('should check permanent dismiss status for each popup', fakeAsync(() => {
      const { useCase, popupRepo, storageService } = setupTestBed();

      popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
      storageService.wasPopupShownToday.and.returnValue(false);
      storageService.isPopupPermanentlyDismissed.and.returnValue(false);

      let result: ShowPopupResult | undefined;
      useCase.execute().subscribe(r => (result = r));
      tick();

      // Should check permanent dismiss status for each popup
      expect(storageService.isPopupPermanentlyDismissed).toHaveBeenCalledWith(
        'popup1',
        '123'
      );
      expect(storageService.isPopupPermanentlyDismissed).toHaveBeenCalledWith(
        'popup2',
        '123'
      );
      expect(storageService.isPopupPermanentlyDismissed).toHaveBeenCalledWith(
        'popup3',
        '123'
      );
    }));
  });
});
