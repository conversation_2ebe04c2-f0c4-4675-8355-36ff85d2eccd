import { <PERSON><PERSON><PERSON><PERSON><PERSON>, I18nPluralPipe, <PERSON><PERSON><PERSON>, NgIf } from '@angular/common';
import { AfterViewInit, Component, inject, OnDestroy } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { I18NService } from '@aplazo/i18n';
import { LoaderService } from '@aplazo/merchant/shared';
import {
  AplazoMatchMediaService,
  ValidDynamicPipesNames,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormFieldDirectives,
  AplazoSearchInputComponent,
  AplazoSelectComponents,
  searchControlFactory,
} from '@aplazo/shared-ui/forms';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponents,
  AplazoConfirmDialogComponent,
  AplazoStatCardComponent,
} from '@aplazo/shared-ui/merchant';
import { iconArrowPath, iconChevronDown } from '@aplazo/ui-icons';
import { DialogService } from '@ngneat/dialog';
import { StatsigModule } from '@statsig/angular-bindings';
import {
  BehaviorSubject,
  combineLatest,
  combineLatestWith,
  filter,
  lastValueFrom,
  map,
  MonoTypeOperatorFunction,
  Observable,
  of,
  pipe,
  shareReplay,
  startWith,
  Subject,
  switchMap,
  take,
  takeUntil,
  tap,
  timer,
} from 'rxjs';
import { AnalyticsService } from '../../../../core/application/services/analytics.service';
import { StoreService } from '../../../../core/application/services/store.service';
import { Order } from '../../../../core/domain/order.interface';
import { EventService } from '../../../../core/domain/repositories/events-service';
import { VALID_ORDER_STATUS } from '../../../../core/domain/valid-order-status';
import { HandleOrderGenerationUseCase } from '../../../cart-v1/application/handle-order-generation.usecase';
import { ChallengesBannerComponent } from '../../../challenges/challenges-banner.component';
import { CancelOrderUseCase } from '../../../orders-v1/application/usecases/cancel-order.usecase';
import { CheckStatusOrderUsecase } from '../../../orders-v1/application/usecases/check-status-order.usecase';
import { GetRefundInfoUsecase } from '../../../orders-v1/application/usecases/get-refund-info.usecase';
import { GetReportUsecase } from '../../../orders-v1/application/usecases/get-report.usecase';
import { RefundInfoComponent } from '../../../orders-v1/refund-info.component';
import { ShowPaymentLinkDialogService } from '../../../share-payment-link/application/services/show-payment-link-dialog.service';
import { PosUiBranchFfDirective } from '../../../shared/directives/pos-ui-branch-ff.directive';
import {
  IOrdersTableInjectedTextUI,
  OrdersTableComponent,
} from '../../../shared/organisms/orders-table/orders-table.component';
import { PreloanCartButtonComponent } from '../../../shared/preloan-cart-button.component';
import { GetOrdersWithSSEService } from '../services/get-orders-with-sse.service';

interface IOrdersStatsInjectedTextUI {
  totalAmount: string;
  totalOrders: string;
  avgAmount: string;

  [key: string]: string;
}

interface ICounterI18n {
  empty: string;
  singular: string;
  plural: string;
}

interface IStatsUI {
  statCardTitle: string;
  value: string;
  isDarkMode: boolean;
  pipeName?: ValidDynamicPipesNames;
  helpTooltip?: string;
  comparison?: {
    label: string;
    value: string;
    isDarkMode: boolean;
    pipeName?: ValidDynamicPipesNames;
  };
}

const getPrice = (orders: Order[]) =>
  orders.reduce((acc, curr) => acc + curr.price, 0);
const getOrders = (orders: Order[]) => orders.length;
const getAvg = (orders: Order[]) => {
  if (orders.length > 0) {
    return (
      orders.reduce((acc, curr) => acc + Math.abs(curr.price), 0) /
      orders.length
    );
  }

  return 0;
};

const STATS_CONTENT = {
  totalAmount: getPrice,
  totalOrders: getOrders,
  avgAmount: getAvg,
} as const;

const ORDER_TYPES = {
  Todos: '',
  Aprobados: VALID_ORDER_STATUS.active,
  Pendientes: VALID_ORDER_STATUS.created,
} as const;

type OrderTypeLabel = keyof typeof ORDER_TYPES;

@Component({
  standalone: true,
  selector: 'app-orders-v2',
  templateUrl: './orders-v2.component.html',
  imports: [
    NgIf,
    NgFor,
    AsyncPipe,
    I18nPluralPipe,
    AplazoButtonComponent,
    AplazoCardComponent,
    AplazoStatCardComponent,
    AplazoIconComponent,
    AplazoCommonMessageComponents,
    AplazoFormFieldDirectives,
    AplazoSelectComponents,
    AplazoSearchInputComponent,
    ReactiveFormsModule,
    PosUiBranchFfDirective,
    OrdersTableComponent,
    ChallengesBannerComponent,
    PreloanCartButtonComponent,
    StatsigModule,
  ],
})
export class OrdersV2Component implements OnDestroy, AfterViewInit {
  readonly #sseOrders = inject(GetOrdersWithSSEService);
  readonly #merchanStore = inject(StoreService);
  readonly #loader = inject(LoaderService);
  readonly #showPaymentLink = inject(ShowPaymentLinkDialogService);
  readonly #dialog = inject(DialogService);
  readonly #cancelOrder = inject(CancelOrderUseCase);
  readonly #getReport = inject(GetReportUsecase);
  readonly #verifyOrderStatusUseCase = inject(CheckStatusOrderUsecase);
  readonly #getRefundInfoUseCase = inject(GetRefundInfoUsecase);
  readonly #i18n = inject(I18NService);
  readonly #registryIconService = inject(AplazoIconRegistryService);
  readonly #matchMedia = inject(AplazoMatchMediaService);
  readonly #newOrderHandler = inject(HandleOrderGenerationUseCase);
  readonly #eventService: EventService = inject(EventService);
  readonly #analyticsService = inject(AnalyticsService);

  readonly #scope = 'orders';
  readonly minLength = 3;

  readonly #destroy$ = new Subject<void>();

  readonly screenSize$ = this.#matchMedia.screenSize$();

  readonly emptyMessageTextTemplate$ = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
    button: {
      label: string;
    };
  }>({
    key: 'emptyMessage',
    scope: this.#scope,
  });

  readonly counterLabelTextTemplate$ = this.#i18n
    .getTranslateObjectByKey<ICounterI18n>({
      key: 'counterLabel',
      scope: this.#scope,
    })
    .pipe(
      map(text => ({
        '=0': text.empty,
        '=1': '# ' + text.singular,
        other: '# ' + text.plural,
      }))
    );

  readonly filterTextTemplate$: Observable<{
    placeholder: string;
    tooltip?: string;
  }> = this.#i18n.getTranslateObjectByKey<{
    placeholder: string;
    tooltip?: string;
  }>({
    key: 'orderFilter',
    scope: this.#scope,
  });
  readonly ordersTableInjectedText$: Observable<IOrdersTableInjectedTextUI> =
    this.#i18n.getTranslateObjectByKey<IOrdersTableInjectedTextUI>({
      key: 'table',
      scope: this.#scope,
    });
  readonly emptySearchInjectedText$: Observable<{
    title: string;
    description: string;
  }> = this.#i18n.getTranslateObjectByKey<{
    title: string;
    description: string;
  }>({
    key: 'emptySearch',
    scope: this.#scope,
  });
  readonly ordersStatsInjectedText$: Observable<IOrdersStatsInjectedTextUI> =
    this.#i18n.getTranslateObjectByKey<IOrdersStatsInjectedTextUI>({
      key: 'stats',
      scope: this.#scope,
    });
  readonly searchbarInjectedText$: Observable<{
    placeholder: string;
    minlengthError: string;
    requiredError: string;
  }> = this.#i18n.getTranslateObjectByKey<{
    placeholder: string;
    minlengthError: string;
    requiredError: string;
  }>({
    key: 'searchbar',
    scope: this.#scope,
    params: {
      minlengthError: { minLength: this.minLength },
      requiredError: { minLength: this.minLength },
    },
  });
  readonly newOrderLabelText$ = this.#i18n.getTranslateObjectByKey<{
    buttonLabel: string;
  }>({
    key: 'newOrder',
    scope: this.#scope,
  });

  readonly searchControl = new FormControl<string | null>('');
  readonly #searchUtilitiesFactory = searchControlFactory({
    minLength: this.minLength,
    debouncedTime: 450,
  });
  readonly orderTypes = Object.keys(ORDER_TYPES) as OrderTypeLabel[];
  orderTypeControl = new FormControl<OrderTypeLabel>(this.orderTypes[0], {
    nonNullable: true,
  });
  readonly currentOrderType$: Observable<OrderTypeLabel> =
    this.orderTypeControl.valueChanges.pipe(
      startWith(this.orderTypeControl.value ?? this.orderTypes[0]),
      takeUntil(this.#destroy$)
    );
  readonly searchValue$ = this.searchControl.valueChanges.pipe(
    startWith(this.searchControl.value),
    this.#searchUtilitiesFactory.debouncedValue(),
    this.#filterSearchStream(),
    takeUntil(this.#destroy$)
  );

  readonly update$ = new Subject<void>();
  readonly #isRefreshing$ = new BehaviorSubject<boolean>(false);
  readonly isLoadingReport$ = this.#loader.isLoading$.pipe(
    combineLatestWith(this.#isRefreshing$),
    map(([isLoading, isRefreshing]) => isLoading || isRefreshing)
  );
  readonly integrationType$ = this.#merchanStore.integrationType$;
  readonly branchId$ = this.#merchanStore.selectedBranch$.pipe(
    map(branch => branch?.id || null)
  );

  readonly orders$ = this.#sseOrders.orders$.pipe(
    combineLatestWith(
      this.searchValue$,
      this.currentOrderType$,
      this.integrationType$
    ),
    map(([orders, searchValue, orderType, integrationType]) => {
      if (searchValue) {
        return orders
          .filter(o => o.status.includes(ORDER_TYPES[orderType]))
          .filter(
            o =>
              String(o.loanId).includes(searchValue) ||
              String(o.sellsAgentId).includes(searchValue)
          )
          .map(o => ({
            ...o,
            isWalmartOrder: integrationType === 'WM_APLAZO',
          }));
      }

      return orders
        .filter(o => o.status.includes(ORDER_TYPES[orderType]))
        .map(o => ({
          ...o,
          isWalmartOrder: integrationType === 'WM_APLAZO',
        }));
    }),
    takeUntil(this.#destroy$),
    shareReplay(1)
  );

  readonly ordersStatsContent$: Observable<IStatsUI[]> = combineLatest({
    orders: this.orders$,
    text: this.ordersStatsInjectedText$,
    branchId: this.branchId$,
  }).pipe(
    filter(({ orders }) => Array.isArray(orders)),

    map(({ orders, text, branchId }) => {
      const filteredOrders = orders
        .filter(branch => branch.branchId === branchId)
        .filter(item => item.status.toLowerCase() === 'activo');

      return Object.entries(STATS_CONTENT).map(([key, fn]) => {
        return {
          isDarkMode: true,
          statCardTitle: text[key],
          value: `${fn(filteredOrders)}`,
          pipeName: (key === 'totalOrders'
            ? 'decimal'
            : 'currency') as ValidDynamicPipesNames,
        };
      });
    }),
    takeUntil(this.#destroy$)
  );

  readonly vm$ = combineLatest({
    orders: this.orders$,
    stats: this.ordersStatsContent$,
    screenSize: this.screenSize$,
    emptyMessage: this.emptyMessageTextTemplate$,
    counterLabel: this.counterLabelTextTemplate$,
    filterText: this.filterTextTemplate$,
    emptySearch: this.emptySearchInjectedText$,
    searchbar: this.searchbarInjectedText$,
    orderTypes: this.orderTypes,
    currentOrderType: this.currentOrderType$,
    isLoadingReport: this.isLoadingReport$,
    integrationType: this.integrationType$,
    branchId: this.branchId$,
    ordersTableInjectedText: this.ordersTableInjectedText$,
    newOrderLabelText: this.newOrderLabelText$,
  }).pipe(takeUntil(this.#destroy$));

  constructor() {
    this.#registryIconService.registerIcons([iconArrowPath, iconChevronDown]);
  }

  async refreshOrdersList(): Promise<void> {
    this.#analyticsService.track('buttonClick', {
      buttonName: 'refreshOrdersList',
    });

    this.#sseOrders.refresh();

    const id = this.#loader.show();

    await lastValueFrom(timer(850).pipe(take(1)));

    this.#loader.hide(id);
  }

  cancelOrder(order: Order): void {
    this.#i18n
      .getTranslateObjectByKey<{
        cancelDialog: {
          title: string;
          message: string;
        };
      }>({
        key: 'cancelDialog',
        scope: this.#scope,
        params: { dynamicMessage: { message: `Orden: ${order.loanId}` } },
      })
      .pipe(
        switchMap(
          data =>
            this.#dialog.open(AplazoConfirmDialogComponent, {
              data,
              maxWidth: '320px',
            }).afterClosed$
        ),
        switchMap(dialogResult => {
          if (dialogResult?.confirmation) {
            this.#eventService
              .cancelOrder({
                order_id: order.loanId,
              })
              .subscribe();
            return this.#cancelOrder.execute(order).pipe(
              tap(() => {
                this.#analyticsService.track('buttonClick', {
                  buttonName: 'cancelLoan',
                  loanId: order.loanId,
                });
              })
            );
          }

          return of(null);
        }),
        take(1)
      )
      .subscribe();
  }

  sharePaymentLink(order: Order): void {
    this.#analyticsService.track('buttonClick', {
      buttonName: 'shareCheckoutLink',
      loanId: order.loanId,
    });
    this.#showPaymentLink.execute(order);
  }

  showRefundInfo(order: Order): void {
    this.#getRefundInfoUseCase
      .execute(String(order.loanId))
      .pipe(
        take(1),
        switchMap(
          data =>
            this.#dialog.open(RefundInfoComponent, {
              data,
            }).afterClosed$
        ),
        take(1)
      )
      .subscribe();
  }

  checkOrderStatus(order: Order) {
    this.#verifyOrderStatusUseCase.execute(order).subscribe();
  }

  newOrder(): void {
    this.#merchanStore.selectedBranch$.pipe(take(1)).subscribe(branch => {
      this.#eventService
        .newOrder({
          storeFrontId: branch?.id || 0,
        })
        .subscribe();
    });

    this.#newOrderHandler.execute().pipe(take(1)).subscribe();

    this.#analyticsService.track('buttonClick', {
      buttonName: 'todayNewOrder',
    });
  }

  generateNewReport() {
    this.#getReport.execute();
  }

  ngAfterViewInit(): void {
    this.#sseOrders.refresh();
  }

  ngOnDestroy(): void {
    this.#sseOrders.dispose();
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  #filterSearchStream(): MonoTypeOperatorFunction<string | null> {
    return pipe(
      filter(
        searchValue =>
          !searchValue ||
          searchValue.length === 0 ||
          searchValue.length >= this.minLength
      )
    );
  }
}
