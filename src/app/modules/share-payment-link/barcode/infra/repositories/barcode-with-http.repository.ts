import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { POS_ENVIRONMENT_CORE } from 'src/app-core/domain/environments';
import { BarCodeRepository } from '../../domain/barcode.repository';

@Injectable()
export class BarCodeWithHttpRepository implements BarCodeRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(POS_ENVIRONMENT_CORE);

  payLoan(args: { customerId: number; loanId: number }): Observable<void> {
    return this.#http.post<void>(
      `${this.#environment.bifrostUrl}/api/v1/loan-scan`,
      {
        customerId: args.customerId,
        loanId: args.loanId,
      }
    );
  }
}
