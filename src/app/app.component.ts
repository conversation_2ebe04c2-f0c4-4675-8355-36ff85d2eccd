import { Async<PERSON>ipe } from '@angular/common';
import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit, inject } from '@angular/core';
import { NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { TagManagerService } from '@aplazo/front-analytics/tag-manager';
import {
  LoaderService,
  MerchantChatAttributes,
  WebchatService,
} from '@aplazo/merchant/shared';
import { AplazoPillLoaderComponent } from '@aplazo/shared-ui/loader';
import { DialogService } from '@ngneat/dialog';
import {
  Subject,
  delayWhen,
  filter,
  map,
  switchMap,
  takeUntil,
  withLatestFrom,
  take,
} from 'rxjs';
import packageJson from '../../package.json';
import { StoreService } from './core/application/services/store.service';
import { MerchantsService } from './core/services/merchants.service';
import { PopupTriggerService } from './modules/shared/popup/application/services/popup-trigger.service';
import { PopupDemoHtmlComponent } from './modules/shared/popup/components/popup-demo-html.component';
import { PopupDemoComponent } from './modules/shared/popup/components/popup-demo.component';

@Component({
  standalone: true,
  selector: 'app-root',
  template: `
    <router-outlet></router-outlet>

    <aplz-ui-pill-loader
      [loading]="(isLoading$ | async) === true"
      [messages]="(loaderMessage$ | async)!"></aplz-ui-pill-loader>

    <!-- Popup Demo Widget (only in development) -->
    <app-popup-demo></app-popup-demo>
  `,
  imports: [
    RouterOutlet,
    AplazoPillLoaderComponent,
    AsyncPipe,
    PopupDemoComponent,
  ],
})
export class AppComponent implements OnInit, OnDestroy {
  readonly #router: Router = inject(Router);
  readonly #storeService: StoreService = inject(StoreService);
  readonly #loaderService: LoaderService = inject(LoaderService);
  readonly #tagService = inject(TagManagerService);
  readonly #webchatService: WebchatService<MerchantChatAttributes> =
    inject(WebchatService);
  readonly #version = packageJson.version;
  readonly #merchantService = inject(MerchantsService);
  readonly #popupTriggerService = inject(PopupTriggerService);
  readonly #dialogService = inject(DialogService);

  readonly isLoading$ = this.#loaderService.isLoading$;
  readonly loaderMessage$ = this.#loaderService.loaderMessage$;

  readonly #destroy$: Subject<void> = new Subject<void>();

  ngOnInit() {
    console.info('Init POSUI version --> ', this.#version);
    console.log('🎯 [AppComponent] Starting popup system initialization');

    // Initialize popup demo HTML listener
    this.initializePopupDemoListener();

    // Clean up old popup records on app initialization
    this.#popupTriggerService.cleanupOldRecords();
    console.log('🎯 [AppComponent] Cleanup completed');

    // For testing: Clear today's popup records to ensure popups can be shown
    const today = new Date().toISOString().split('T')[0];
    const popupStorage = this.#popupTriggerService['popupStorage'];
    if (
      popupStorage &&
      typeof popupStorage.clearRecordsForDate === 'function'
    ) {
      popupStorage.clearRecordsForDate(today);
      console.log('🎯 [AppComponent] Cleared popup records for today:', today);
    }

    // Global popup trigger system: Initialize popup checks for selected branch (only once)
    this.initializePopupTriggers();
    console.log('🎯 [AppComponent] Popup triggers initialized');

    this.#router.events
      .pipe(
        filter(e => e instanceof NavigationEnd),
        withLatestFrom(
          this.#storeService.merchantName$,
          this.#storeService.merchantConfig$,
          this.#storeService.selectedBranch$
        ),
        takeUntil(this.#destroy$)
      )
      .subscribe(([, merchantName, merchantConfig, selectedBranch]) => {
        this.#tagService.trackEvent({
          event: 'pageView',
          merchantId: merchantConfig?.merchantId || 0,
          merchantName: merchantName || '',
          branchId: selectedBranch?.id || 0,
          branchName: selectedBranch?.name || '',
        });
      });

    this.#router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntil(this.#destroy$)
      )
      .subscribe(() => {
        const isMainViews =
          document.location.pathname.includes('cart') ||
          document.location.pathname.includes('orders');

        if (!isMainViews) {
          this.#webchatService.hide();
          return;
        }

        this.#webchatService.show();

        // Global popup trigger system: Avoid redundant popup triggers after navigation
        // Popup triggers are already handled in initializePopupTriggers and after login
      });

    this.#storeService.token$
      .pipe(
        filter(Boolean),
        delayWhen(() =>
          this.#webchatService.chatInitialized$.pipe(filter(Boolean))
        ),
        switchMap(() => this.#merchantService.getMerchantDetails()),
        withLatestFrom(
          this.#storeService.selectedBranch$,
          this.#storeService.getMerchantId$(),
          this.#storeService.merchantName$,
          this.#storeService.integrationType$
        ),
        map(
          ([
            merchantDetails,
            branch,
            merchantId,
            merchantName,
            integrationType,
          ]) => {
            const attributes = {
              sharedEmails: merchantDetails.email
                ? [merchantDetails.email]
                : [],
              sharedPhones: [],
            };

            const customAttributes = {
              merchantIdNum: merchantId || 0,
              merchantNameStr: merchantName || '',
              merchantintegrationStr: integrationType || '',
              storefrontNameStr: branch?.name || '',
              storefrontIdNum: branch?.id || 0,
              correoStr: merchantDetails.email || '',
              statusStr: merchantDetails.status || '',
              merchantchannelStr: merchantDetails.channel || '',
              merchantsegmentStr: merchantDetails.segment || '',
            };

            return {
              attributes,
              customAttributes,
            };
          }
        ),
        takeUntil(this.#destroy$)
      )
      .subscribe(({ attributes, customAttributes }) => {
        this.#webchatService.setCustomerChatAttributes({
          attributes,
          customAttributes,
        });
        console.log(
          '🎯 [AppComponent] After login setup completed, checking for popups...'
        );

        // Global popup trigger system: Check for popups after successful login
        this.#storeService.selectedBranch$
          .pipe(
            filter(
              (branch): branch is { id: string | number } & typeof branch =>
                !!branch && branch.id !== undefined && branch.id !== null
            ),
            take(1) // Take only the first valid branch to avoid multiple triggers
          )
          .subscribe(branch => {
            console.log(
              '🎯 [AppComponent] Triggering popup after login for branch:',
              branch.id
            );
            this.#popupTriggerService
              .triggerAfterLogin(branch.id.toString())
              .subscribe({
                next: result => {
                  if (result) {
                    console.log(
                      '🎯 [AppComponent] Popup triggered after login for branch:',
                      branch.id
                    );
                  } else {
                    console.log(
                      '🎯 [AppComponent] No popup triggered after login for branch:',
                      branch.id
                    );
                  }
                },
                error: err => {
                  console.error(
                    `🎯 [AppComponent] Error triggering popup for branch ${branch.id}:`,
                    err
                  );
                },
              });
          });
      });
  }

  /**
   * Initializes popup triggers for the selected branch on app initialization.
   */
  private initializePopupTriggers(): void {
    console.log('🎯 [AppComponent] initializePopupTriggers: Starting...');

    this.#storeService.selectedBranch$
      .pipe(
        filter(
          (branch): branch is { id: string | number } & typeof branch =>
            !!branch && branch.id !== undefined && branch.id !== null
        ),
        takeUntil(this.#destroy$)
      )
      .subscribe(branch => {
        console.log(
          '🎯 [AppComponent] initializePopupTriggers: Branch selected:',
          branch.id
        );
        this.triggerPopupForBranch(branch.id, 'app initialization');
      });
  }

  /**
   * Triggers a popup for the given branch ID if needed, logging the context of the trigger.
   * @param branchId The ID of the branch to check for popups.
   * @param context The context in which the popup is being triggered (e.g., 'app initialization' or 'navigation').
   */
  private triggerPopupForBranch(
    branchId: string | number,
    context: string
  ): void {
    console.log(
      `🎯 [AppComponent] triggerPopupForBranch: Triggering for branch ${branchId} in context: ${context}`
    );

    this.#popupTriggerService
      .triggerPopupIfNeeded({ branchId: branchId.toString() })
      .subscribe({
        next: result => {
          if (result) {
            console.log(
              `🎯 [AppComponent] triggerPopupForBranch: Popup triggered after ${context} for branch:`,
              branchId
            );
          } else {
            console.log(
              `🎯 [AppComponent] triggerPopupForBranch: No popup triggered for branch ${branchId} in context: ${context}. If an API error occurred (e.g., 401 Unauthorized), mock data should be used as fallback.`
            );
          }
        },
        error: err => {
          console.error(
            `🎯 [AppComponent] triggerPopupForBranch: Error triggering popup for branch ${branchId}:`,
            err
          );
        },
      });
  }

  ngOnDestroy(): void {
    // Clean up popup demo event listener
    window.removeEventListener('popup-demo-html', this.#popupDemoHandler);

    this.#destroy$.next();
    this.#destroy$.complete();
  }

  /**
   * Event handler for popup demo HTML events.
   * Opens a dialog with custom HTML content from the demo widget.
   */
  readonly #popupDemoHandler = (event: Event) => {
    const customEvent = event as CustomEvent<any>;
    const detail =
      typeof customEvent.detail === 'string'
        ? { html: customEvent.detail }
        : customEvent.detail;
    console.log('🎭 [AppComponent] Received popup demo HTML event:', detail);

    this.#dialogService.open(PopupDemoHtmlComponent, {
      data: { htmlContent: detail.html },
      minHeight: detail.height ? detail.height + 'px' : '300px',
      minWidth: detail.width ? detail.width + 'px' : '500px',
      maxWidth: '98vw',
      width: detail.width ? detail.width + 'px' : '900px',
      closeButton: true,
    });
  };

  /**
   * Initializes the popup demo HTML event listener.
   * This allows the demo widget to show custom HTML in popups.
   */
  private initializePopupDemoListener(): void {
    console.log('🎭 [AppComponent] Initializing popup demo HTML listener');
    window.addEventListener(
      'popup-demo-html',
      this.#popupDemoHandler as EventListener
    );
  }
}
