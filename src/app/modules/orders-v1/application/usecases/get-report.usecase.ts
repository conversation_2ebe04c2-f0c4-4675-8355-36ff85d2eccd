import { Injectable } from '@angular/core';
import {
  BrowserDownloaderService,
  LoaderService,
  NotifierService,
} from '@aplazo/merchant/shared';
import {
  EMPTY,
  catchError,
  defer,
  delay,
  finalize,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { PosOfflineRepository } from 'src/app/core/domain/repositories/pos-offline.repository';

@Injectable({ providedIn: 'root' })
export class GetReportUsecase {
  constructor(
    private loader: LoaderService,
    private notifier: NotifierService,
    private repository: PosOfflineRepository,
    private downloader: BrowserDownloaderService
  ) {}

  public execute() {
    const idLoader = this.loader.show();

    this.repository
      .requestReport()
      .pipe(
        take(1),

        delay(450),

        switchMap(response =>
          defer(() =>
            this.downloader.executeByBlob(response, 'reporte_del_dia', 'xlsx')
          )
        ),

        tap(() => {
          this.notifier.success({
            title: 'Reporte generado',
            message:
              'El reporte se ha generado correctamente y la descarga comenzara pronto.',
          });
        }),

        catchError((e: any) => {
          console.error(e);

          this.notifier.warning({
            title: 'Ups, algo salió mal',
            message: 'No se pudo generar el reporte',
          });

          return EMPTY;
        }),

        finalize(() => {
          this.loader.hide(idLoader);
        })
      )
      .subscribe();
  }
}
