import { Component, inject } from '@angular/core';
import { RedirectionService } from '@aplazo/merchant/shared';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconCheckCircle } from '@aplazo/ui-icons';
import {
  POS_ENVIRONMENT_CORE,
  PosEnvironmentCoreType,
} from '../../../app-core/domain/environments';

@Component({
  standalone: true,
  imports: [AplazoIconComponent, AplazoButtonComponent],
  selector: 'aplazo-success-page',
  template: `
    <main
      class="h-full min-h-[600px] lg:h-screen flex flex-col items-center justify-center">
      <div class="w-full max-w-lg text-center">
        <picture class="block w-full h-24 text-special-success">
          <aplz-ui-icon name="check-circle" size="full"></aplz-ui-icon>
        </picture>

        <h1 class="text-lg lg:text-xl text-dark-secondary my-12 py-8">
          ¡Gracias! Tu compra fue procesada.
        </h1>
        <button
          class="mx-auto"
          aplzButton
          aplzAppearance="stroked"
          aplzColor="dark"
          size="lg"
          (click)="goToLangingPage()">
          Volver
        </button>
      </div>
    </main>
  `,
})
export class SuccessPageComponent {
  readonly #redirecter: RedirectionService = inject(RedirectionService);
  readonly #environment: PosEnvironmentCoreType = inject(POS_ENVIRONMENT_CORE);
  readonly #iconRegistry = inject(AplazoIconRegistryService);

  constructor() {
    this.#iconRegistry.registerIcons([iconCheckCircle]);
  }

  goToLangingPage() {
    if (this.#environment.landingpage) {
      this.#redirecter.externalNavigation(this.#environment.landingpage);
    }
  }
}
