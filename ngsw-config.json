{"$schema": "./node_modules/@angular/service-worker/config/schema.json", "index": "/index.html", "assetGroups": [{"name": "app", "installMode": "prefetch", "resources": {"files": ["/favicon.ico", "/index.html", "/manifest.webmanifest", "/*.css", "/*.js"]}}, {"name": "assets", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["/assets/**", "/*.(svg|cur|jpg|jpeg|png|apng|webp|avif|gif|ico|mp4|webm)", "/**/*.(svg|cur|jpg|jpeg|png|apng|webp|avif|gif|ico|mp4|webm)", "/*.(otf|ttf|woff|woff2)", "/**/*.(otf|ttf|woff|woff2)", "/*.(json)", "/assets/i18n/**"]}}], "dataGroups": [{"name": "app-configs", "urls": ["*/api/merchant_configs", "*/api/v1/branches/**"], "cacheConfig": {"strategy": "freshness", "maxSize": 0, "maxAge": "0u", "timeout": "30s"}}, {"name": "api-performance", "urls": ["*/api/user_agent/"], "cacheConfig": {"strategy": "performance", "maxSize": 100, "maxAge": "1h"}}, {"name": "i18n-translations", "urls": ["*/i18n/**"], "cacheConfig": {"strategy": "performance", "maxSize": 20, "maxAge": "1h"}}, {"name": "external-assets", "urls": ["https://cdn.aplazo.mx/**", "https://aplazoassets.s3*/**"], "cacheConfig": {"strategy": "performance", "maxSize": 100, "maxAge": "1h"}}, {"name": "third-party-services", "urls": ["https://www.google-analytics.com/**", "https://analytics.google.com/**", "https://ssl.google-analytics.com/**", "https://prodregistryv2.org/v1/**", "https://*.featureassets.org/**", "https://*.beyondwickedmapping.org/**", "https://aplazo.api.kustomerapp.com/**"], "cacheConfig": {"strategy": "freshness", "maxSize": 0, "maxAge": "0u", "timeout": "10s"}}, {"name": "datadog-browser-intake", "urls": ["https://browser-intake-datadoghq.com/**"], "cacheConfig": {"strategy": "freshness", "maxSize": 0, "maxAge": "0u", "timeout": "30s"}}, {"name": "tag-manager", "urls": ["https://www.googletagmanager.com/**"], "cacheConfig": {"strategy": "performance", "maxSize": 10, "maxAge": "300s", "timeout": "30s"}}]}