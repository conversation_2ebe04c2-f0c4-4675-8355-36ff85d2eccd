import { Component, DebugElement } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { FinishWithBarcodeUsecase } from 'src/app/modules/share-payment-link/barcode/application/finish-with-barcode.usecase';
import { BarcodeComponent } from 'src/app/modules/share-payment-link/barcode/infra/components/barcode.component';
import { provideI18NTesting } from 'src/tests/i18n.local';

@Component({
  standalone: true,
  imports: [BarcodeComponent],
  template: `<app-barcode [data]="data"></app-barcode>`,
})
class TestHostComponent {
  data = {
    order: null,
    branch: null,
  };
}

describe('BarcodeComponent', () => {
  let fixture: ComponentFixture<TestHostComponent>;
  let barCodeComponent: DebugElement;
  let usecase: jasmine.SpyObj<FinishWithBarcodeUsecase>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [BarcodeComponent],
      providers: [
        provideI18NTesting('barcode-payment'),
        {
          provide: FinishWithBarcodeUsecase,
          useValue: jasmine.createSpyObj('FinishWithBarcodeUsecase', [
            'execute',
          ]),
        },
      ],
    });

    fixture = TestBed.createComponent(TestHostComponent);
    fixture.detectChanges();

    usecase = TestBed.inject(
      FinishWithBarcodeUsecase
    ) as jasmine.SpyObj<FinishWithBarcodeUsecase>;

    barCodeComponent = fixture.debugElement.query(
      By.directive(BarcodeComponent)
    );
  });

  it('should create', () => {
    expect(barCodeComponent).toBeTruthy();
  });

  it('should show the title', () => {
    const title = barCodeComponent.query(By.css('h1'));

    expect(title.nativeElement.textContent?.trim()).toBe(
      'Solicita al cliente mostrar el código de barras'
    );
  });

  it('shouls show an input with the label "Código del cliente"', () => {
    const label = barCodeComponent.query(
      By.css('label.aplazo-form-field__label')
    );
    const input = barCodeComponent.query(By.css('input[aplzforminput]'));

    expect(label.nativeElement.textContent?.trim()).toBe(
      'Código del cliente  *'
    );
    expect(input).toBeDefined();
  });
});
