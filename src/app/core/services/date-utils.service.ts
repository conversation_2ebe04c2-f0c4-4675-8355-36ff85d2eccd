import { Injectable } from '@angular/core';
import { Guard, RuntimeMerchantError } from '@aplazo/merchant/shared';
import {
  endOfDay,
  format,
  isWithinInterval,
  parseISO,
  startOfDay,
  sub,
} from 'date-fns';
import { es } from 'date-fns/locale';

const config = {
  locale: es,
};

@Injectable({ providedIn: 'root' })
export class DateUtilsService {
  today = () => new Date();

  endOfDay = (date: Date): Date => endOfDay(date);

  startOfDay = (date: Date): Date => startOfDay(date);

  parseFromISO = (date: string): Date | null => {
    const parsed = parseISO(date);

    try {
      Guard.againstInvalidDate({
        date: parsed,
        errorMessage: 'La fecha proporcionada no es válida.',
        origin: 'DateUtilsService::parseIsoDate',
      });

      return parsed;
    } catch (error) {
      if (error instanceof RuntimeMerchantError) {
        console.warn(error.code);
      } else {
        console.error(error);
      }

      return null;
    }
  };

  /**
   * A simple validation to check if the date is today or yesterday
   *
   * @param {string} date - Is a string that represents a date in ISO format
   *
   * @returns {boolean} - true if the date is today or yesterday, false otherwise
   *
   * @example
   * dateUtils.isTodayOrYesterday('2021-09-01T23:59:59.999Z') // false
   */
  isTodayOrYesterday(date: string): boolean {
    const parsed = this.parseFromISO(date);

    if (!parsed) {
      return false;
    }

    const todayAtTheEnd = endOfDay(this.today());
    const yesterday = this.startOfDay(sub(todayAtTheEnd, { days: 1 }));

    const dateRangeToValidate = {
      start: yesterday,
      end: todayAtTheEnd,
    };

    return isWithinInterval(parsed, dateRangeToValidate);
  }

  getMonthYear(date: Date): string {
    return format(date, 'MMMM yyyy', config);
  }
}
