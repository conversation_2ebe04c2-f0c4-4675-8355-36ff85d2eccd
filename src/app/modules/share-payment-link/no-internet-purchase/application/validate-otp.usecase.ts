import { HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { TransactionBehaviorService } from '@aplazo/front-analytics/kount';
import {
  BaseUsecase,
  Guard,
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { catchError, Observable, take, tap, throwError } from 'rxjs';
import {
  NO_INTERNET_PURCHASE_ERROR_MESSAGES,
  NO_INTERNET_PURCHASE_ERRORS,
  NoInternetPurchaseError,
  NoInternetPurchaseValidateOTPRequest,
} from '../domain/entities/no-internet-purchase';
import { NoInternetPurchaseRepository } from '../domain/repositories/no-internet-purchase.repository';

@Injectable()
export class ValidateOTPUseCase
  implements
    BaseUsecase<NoInternetPurchaseValidateOTPRequest, Observable<void>>
{
  readonly #repository = inject(NoInternetPurchaseRepository);
  readonly #notifier = inject(NotifierService);
  readonly #errorHandler = inject(UseCaseErrorHandler);
  readonly #kount = inject(TransactionBehaviorService);

  execute(args: NoInternetPurchaseValidateOTPRequest): Observable<void> {
    try {
      const request: NoInternetPurchaseValidateOTPRequest =
        {} as NoInternetPurchaseValidateOTPRequest;

      Guard.againstInvalidNumbers(
        +args.phoneNumber,
        'ValidateOTPUseCase::phoneNumber::invalid',
        'Verifica el número de teléfono'
      );

      Guard.againstInvalidNumbers(
        args.loanId,
        'ValidateOTPUseCase::loanId::invalid',
        'Verifica el ID del préstamo'
      );

      request.loanId = args.loanId;

      if (!Guard.againstEmptyValue(args, 'code').succeeded) {
        throw new RuntimeMerchantError(
          'El código de autorización no puede estar vacío',
          'ValidateOTPUseCase::otp::empty'
        );
      }

      request.code = args.code;

      if (args.phoneNumber.length !== 10) {
        throw new RuntimeMerchantError(
          'El número de teléfono debe tener 10 dígitos',
          'ValidateOTPUseCase::phoneNumber::invalidLength'
        );
      }

      request.phoneNumber = args.phoneNumber;

      if (!args.sessionId) {
        request.sessionId = this.#kount.sessionId;
      } else {
        request.sessionId = args.sessionId;
      }

      return this.#repository.validateOTP(request).pipe(
        tap(() => {
          this.#notifier.success({
            title: 'Orden completada',
            message:
              'En breve podra ver la actualización en la sección de órdenes',
          });
        }),

        catchError(err => {
          if (
            err instanceof HttpErrorResponse &&
            Object.values(NO_INTERNET_PURCHASE_ERRORS).includes(
              (err.error?.code as NoInternetPurchaseError) ?? ''
            )
          ) {
            const key = err.error.code as NoInternetPurchaseError;

            throw new RuntimeMerchantError(
              NO_INTERNET_PURCHASE_ERROR_MESSAGES[key],
              'ValidateOTPUseCase::ApzOcl::' + key
            );
          }

          if (
            err instanceof HttpErrorResponse &&
            (err.error?.code === 'APZ4403' ||
              err.error?.error === 'INSUFFICIENT_CREDIT')
          ) {
            throw new RuntimeMerchantError(
              'El cliente no cuenta con el crédito suficiente para realizar la compra',
              'ValidateOTPUseCase::ApzOcl::insufficientCredit'
            );
          }

          return this.#errorHandler.handle<never>(err);
        }),

        take(1)
      );
    } catch (error) {
      return this.#errorHandler.handle(
        error,
        throwError(() => error)
      );
    }
  }
}
