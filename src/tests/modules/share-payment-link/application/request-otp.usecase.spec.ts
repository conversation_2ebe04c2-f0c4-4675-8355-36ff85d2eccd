import { HttpErrorResponse } from '@angular/common/http';
import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import {
  NotifierService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  provideNotifierTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { of, throwError } from 'rxjs';
import { RequestOTPUseCase } from '../../../../app/modules/share-payment-link/no-internet-purchase/application/request-otp.usecase';
import { NoInternetPurchaseRepository } from '../../../../app/modules/share-payment-link/no-internet-purchase/domain/repositories/no-internet-purchase.repository';

describe('RequestOtpUsecase', () => {
  let usecase: RequestOTPUseCase;
  let repository: jasmine.SpyObj<NoInternetPurchaseRepository>;
  let successNotifier: jasmine.Spy;
  let errorHandler: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        RequestOTPUseCase,
        {
          provide: NoInternetPurchaseRepository,
          useValue: jasmine.createSpyObj('NoInternetPurchaseRepository', [
            'requestOTP',
          ]),
        },
        provideNotifierTesting(),
        provideUseCaseErrorHandlerTesting(),
      ],
    });

    usecase = TestBed.inject(RequestOTPUseCase);
    repository = TestBed.inject(
      NoInternetPurchaseRepository
    ) as jasmine.SpyObj<NoInternetPurchaseRepository>;

    successNotifier = spyOn(
      TestBed.inject(NotifierService),
      'success'
    ).and.callThrough();
    errorHandler = spyOn(
      TestBed.inject(UseCaseErrorHandler),
      'handle'
    ).and.callThrough();
  });

  it('should be created', () => {
    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(RequestOTPUseCase);
  });

  it('should throw an error if the phone number is invalid', fakeAsync(() => {
    let result: any;

    usecase
      .execute({
        phoneNumber: 'a123',
        loanId: 1,
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe('Guard::againstInvalidNumbers');
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.requestOTP).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error if the loan id is invalid', fakeAsync(() => {
    let result: any;

    usecase
      .execute({
        phoneNumber: '1234567890',
        // @ts-expect-error: testing invalid loan id
        loanId: 'a',
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe('Guard::againstInvalidNumbers');
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.requestOTP).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error if the phone number length is invalid', fakeAsync(() => {
    let result: any;

    usecase
      .execute({
        phoneNumber: '123456789',
        loanId: 1,
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe('RequestOTPUseCase::phoneNumber::invalidLength');
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.requestOTP).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
  }));

  it('should throw RuntimeMerchantError when repository fails with controlled error APZ_OCL_0001', fakeAsync(() => {
    repository.requestOTP.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 400,
            error: {
              code: 'APZ_OCL_0001',
            },
          })
      )
    );

    let result: any;

    usecase
      .execute({
        phoneNumber: '1234567890',
        loanId: 1,
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe('RequestOTPUseCase::ApzOcl::APZ_OCL_0001');
    expect(repository.requestOTP).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
  }));

  it('should throw RuntimeMerchantError when repository fails with controlled error APZ_OCL_0002', fakeAsync(() => {
    repository.requestOTP.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 400,
            error: {
              code: 'APZ_OCL_0002',
            },
          })
      )
    );

    let result: any;

    usecase
      .execute({
        phoneNumber: '1234567890',
        loanId: 1,
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe('RequestOTPUseCase::ApzOcl::APZ_OCL_0002');
    expect(repository.requestOTP).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
  }));

  it('should throw RuntimeMerchantError when repository fails with controlled error APZ_OCL_0003', fakeAsync(() => {
    repository.requestOTP.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 400,
            error: {
              code: 'APZ_OCL_0003',
            },
          })
      )
    );

    let result: any;

    usecase
      .execute({
        phoneNumber: '1234567890',
        loanId: 1,
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(RuntimeMerchantError);
    expect(result.code).toBe('RequestOTPUseCase::ApzOcl::APZ_OCL_0003');
    expect(repository.requestOTP).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(0);
    expect(successNotifier).toHaveBeenCalledTimes(0);
  }));

  it('should throw an error when repository fails with an unknown error', fakeAsync(() => {
    repository.requestOTP.and.returnValue(
      throwError(
        () =>
          new HttpErrorResponse({
            status: 500,
            statusText: 'Internal Server Error',
          })
      )
    );

    let result: any;

    usecase
      .execute({
        phoneNumber: '1234567890',
        loanId: 1,
      })
      .subscribe({
        next: fail,
        error: err => {
          result = err;
        },
      });

    tick();

    expect(result).toBeInstanceOf(HttpErrorResponse);
    expect(errorHandler).toHaveBeenCalledTimes(1);
    expect(repository.requestOTP).toHaveBeenCalledTimes(1);
    expect(successNotifier).toHaveBeenCalledTimes(0);
  }));

  it('should notify success when repository succeeds', fakeAsync(() => {
    repository.requestOTP.and.returnValue(of(void 0));

    let result: any;

    usecase
      .execute({
        phoneNumber: '1234567890',
        loanId: 1,
      })
      .subscribe({
        next: res => {
          result = res;
        },
        error: fail,
      });

    tick();

    expect(result).toBeUndefined();
    expect(successNotifier).toHaveBeenCalledTimes(1);
    expect(repository.requestOTP).toHaveBeenCalledTimes(1);
    expect(errorHandler).toHaveBeenCalledTimes(0);
  }));
});
