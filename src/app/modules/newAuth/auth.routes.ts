import { Route } from '@angular/router';
import { ROUTE_CONFIG } from '../../core/domain/config/app-routes.core';
import { allowNavigationWhenOnline } from '../../core/guards/disallow-navigation-when-offline.guard';
import { provideLogin } from './login/infra/config/providers';
import { LoginComponent } from './login/login.component';

const authRoutes: Route[] = [
  {
    path: '',
    redirectTo: ROUTE_CONFIG.login,
    pathMatch: 'full',
  },
  {
    path: ROUTE_CONFIG.login,
    canActivate: [allowNavigationWhenOnline],
    canDeactivate: [allowNavigationWhenOnline],
    providers: [provideLogin()],
    component: LoginComponent,
  },
];

export default authRoutes;
