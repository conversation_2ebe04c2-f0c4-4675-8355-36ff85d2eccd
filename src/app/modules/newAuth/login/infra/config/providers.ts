import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { LoginCentralizedUseCase } from '../../application/login-centralized.usecase';
import { LoginCentralizedRepository } from '../../domain/login-centralized.repository';
import { LoginRepository } from '../../domain/login.repository';
import { LoginCentralizedService } from '../../login-centralized.service';
import { LoginService } from '../../login.service';

export function provideLogin(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: LoginRepository,
      useClass: LoginService,
    },
    {
      provide: LoginCentralizedRepository,
      useClass: LoginCentralizedService,
    },
    LoginCentralizedUseCase,
  ]);
}
