import { AsyncPipe, I18nPluralPipe } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { fakeAsync, flush, TestBed, tick } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import {
  provideJwtDecoderTesting,
  provideLoaderTesting,
  provideNotifierTesting,
} from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoFormFieldDirectives,
  AplazoSearchInputComponent,
  AplazoSelectComponents,
} from '@aplazo/shared-ui/forms';
import { AplazoIconComponent } from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponents,
  AplazoStatCardComponent,
} from '@aplazo/shared-ui/merchant';
import { DialogService } from '@ngneat/dialog';
import { StatsigModule, StatsigService } from '@statsig/angular-bindings';
import { of, Subject } from 'rxjs';
import { OrdersWithSocketService } from 'src/app/core/services/orders-with-socket.service';
import { GetActiveBannerUsecase } from 'src/app/modules/dynamic-banners/application/get-active-banner.usecase';
import { DynamicBannerComponent } from 'src/app/modules/dynamic-banners/infra/components/dynamic-banner.component';
import { PreloanCartButtonComponent } from 'src/app/modules/shared/preloan-cart-button.component';
import { provideI18NTesting } from 'src/tests/i18n.local';
import { providePosEnvironmentCore } from '../../../app-core/domain/environments';
import { AnalyticsService } from '../../../app/core/application/services/analytics.service';
import { StoreService } from '../../../app/core/application/services/store.service';
import { Order } from '../../../app/core/domain/order.interface';
import { EventService } from '../../../app/core/domain/repositories/events-service';
import { HandleOrderGenerationUseCase } from '../../../app/modules/cart-v1/application/handle-order-generation.usecase';
import { CancelOrderUseCase } from '../../../app/modules/orders-v1/application/usecases/cancel-order.usecase';
import { CheckStatusOrderUsecase } from '../../../app/modules/orders-v1/application/usecases/check-status-order.usecase';
import { GetRefundInfoUsecase } from '../../../app/modules/orders-v1/application/usecases/get-refund-info.usecase';
import { GetReportUsecase } from '../../../app/modules/orders-v1/application/usecases/get-report.usecase';
import { OrdersV1Component } from '../../../app/modules/orders-v1/orders-v1.component';
import { ShowPaymentLinkDialogService } from '../../../app/modules/share-payment-link/application/services/show-payment-link-dialog.service';
import { PosUiBranchFfDirective } from '../../../app/modules/shared/directives/pos-ui-branch-ff.directive';
import { OrdersTableComponent } from '../../../app/modules/shared/organisms/orders-table/orders-table.component';
import { ordersDummy } from '../../../mocks/order.dummy';
import { LocalAnalyticsService } from '../../core/services/impl/local-analytics.service';
import { LocalEventsService } from '../../core/services/impl/local-event.service';
import { LocalSharePaymentLinkService } from '../share-payment-link/local-share-payment-link.service';
import { NotifierService } from '@aplazo/merchant/shared';

const defaultOrders: Order[] = [
  {
    id: 28754,
    createdAt: '2024-05-24T13:49:26.60761Z',
    updatedAt: '2024-05-24T13:49:50.027838Z',
    DeletedAt: null,
    date: '2024-05-24 13:49:26.605574755 +0000 UTC m=+318636.678757560',
    status: 'Activo',
    price: 100,
    url: 'https://checkout-offline.aplazo.net/main/cbff8d6b-b956-4faa-a6f0-a1cd05b2bd0b',
    loanId: 109724,
    merchantId: 199,
    products: [],
    branchId: 145,
    sellsAgentId: 0,
  },
  {
    id: 28755,
    createdAt: '2024-05-24T13:51:14.734048Z',
    updatedAt: '2024-05-24T13:51:39.447193Z',
    DeletedAt: null,
    date: '2024-05-24 13:51:14.73200515 +0000 UTC m=+318744.805187961',
    status: 'Activo',
    price: 100,
    url: 'https://checkout-offline.aplazo.net/main/678197c3-f6e5-4537-8973-da79a02599f9',
    loanId: 109725,
    merchantId: 199,
    products: [],
    branchId: 145,
    sellsAgentId: 0,
  },
  {
    id: 28756,
    createdAt: '2024-05-24T13:54:24.875129Z',
    updatedAt: '2024-05-24T13:54:50.470053Z',
    DeletedAt: null,
    date: '2024-05-24 13:54:24.872756635 +0000 UTC m=+318934.945939444',
    status: 'Activo',
    price: 100,
    url: 'https://checkout-offline.aplazo.net/main/bb5d1be9-9df4-4216-ab91-c41103ccb6b5',
    loanId: 109726,
    merchantId: 199,
    products: [],
    branchId: 145,
    sellsAgentId: 0,
  },
  {
    id: 28767,
    createdAt: '2024-05-24T16:25:32.958567556Z',
    updatedAt: '2024-05-24T16:25:32.958567556Z',
    DeletedAt: null,
    date: '2024-05-24 16:25:32.956588751 +0000 UTC m=+328003.029771562',
    status: 'created',
    price: 100,
    url: 'https://checkout-offline.aplazo.net/main/7b1a0135-0d2d-4723-81b5-af3b241b48ef',
    loanId: 109753,
    merchantId: 199,
    products: [
      {
        id: 30827,
        createdAt: '2024-05-24T16:25:32.960931408Z',
        updatedAt: '2024-05-24T16:25:32.960931408Z',
        DeletedAt: null,
        quantity: 1,
        description: 'POS OFFLine',
        imageUrl:
          'https://aplazoassets.s3-us-west-2.amazonaws.com/aplazo-logo-png-colores.png',
        price: 100,
        title: 'Concrete',
        ExternalId: 'uottf',
        merchantPosOrderId: 28767,
      },
    ],
    branchId: 36,
    sellsAgentId: 0,
  },
];

const setup = (args?: { orders?: Order[]; store?: Partial<StoreService> }) => {
  const defaultArgs = {
    orders: defaultOrders,
    store: {
      integrationType$: of('POSUI'),
      selectedBranch$: of({
        id: 36,
        createdAt: '2021-04-07T15:22:48.256326Z',
        updatedAt: '2025-02-24T22:10:39.088741Z',
        DeletedAt: null,
        name: 'Toluca 10',
        banned: false,
        merchantConfigId: 12,
        shareLinkFlags: {
          id: 32,
          createdAt: '2022-01-17T18:48:15.09599Z',
          updatedAt: '2025-02-24T22:10:39.088741Z',
          DeletedAt: null,
          isQrAllowed: true,
          isWhatsappAllowed: true,
          isSMSAllowed: true,
          branchId: 36,
        },
        branchFeatureFlags: {
          id: 2,
          createdAt: '0001-01-01T00:00:00Z',
          updatedAt: '2025-02-24T22:10:39.088741Z',
          DeletedAt: null,
          branchId: 36,
          isSellAgentTrackEnable: false,
          isSupportChatEnable: true,
          isOrderRefundEnable: true,
          isTodayReportEnable: false,
        },
      }),
      getMerchantId$: () => of(199),
      hasBranchShareLinkEnabled$: of(true),
    },
  };

  const config = {
    orders: args?.orders ?? defaultArgs.orders,
    store: args?.store ?? defaultArgs.store,
  };

  TestBed.configureTestingModule({
    imports: [
      AsyncPipe,
      I18nPluralPipe,
      AplazoButtonComponent,
      AplazoCardComponent,
      AplazoStatCardComponent,
      AplazoIconComponent,
      AplazoCommonMessageComponents,
      AplazoFormFieldDirectives,
      AplazoSelectComponents,
      AplazoSearchInputComponent,
      ReactiveFormsModule,
      OrdersTableComponent,
      PreloanCartButtonComponent,
      PosUiBranchFfDirective,
      DynamicBannerComponent,
      HttpClientTestingModule,
      StatsigModule,
    ],
    providers: [
      OrdersWithSocketService,
      DialogService,
      {
        provide: NotifierService,
        useValue: {
          open: jasmine.createSpy('open'),
          warning: jasmine.createSpy('warning'),
        },
      },
      {
        provide: OrdersWithSocketService,
        useValue: {
          orders$: of(config.orders),
          refreshOrdersList: () => void 0,
          paymentFailure$: new Subject<any>(), // Add this line
        },
      },
      providePosEnvironmentCore(),
      provideLoaderTesting(),
      provideJwtDecoderTesting(),
      provideI18NTesting('orders'),
      provideNotifierTesting(),
      {
        provide: ShowPaymentLinkDialogService,
        useClass: LocalSharePaymentLinkService,
      },
      {
        provide: StoreService,
        useValue: config.store,
      },
      {
        provide: CancelOrderUseCase,
        useValue: () => of(void 0),
      },
      {
        provide: AnalyticsService,
        useClass: LocalAnalyticsService,
      },
      {
        provide: EventService,
        useClass: LocalEventsService,
      },
      {
        provide: GetReportUsecase,
        useValue: {
          execute: () => of(void 0),
        },
      },
      {
        provide: GetRefundInfoUsecase,
        useValue: {
          execute: () => of(null),
        },
      },
      {
        provide: HandleOrderGenerationUseCase,
        useValue: {
          execute: () => of(config.orders[0]),
        },
      },
      {
        provide: CheckStatusOrderUsecase,
        useValue: {
          execute: () => of({ content: config.orders[0] }),
        },
      },
      {
        provide: GetActiveBannerUsecase,
        useValue: {
          execute: () => of(null),
        },
      },
      {
        provide: StatsigService,
        useValue: {
          checkGate: () => of(false),
        },
      },
    ],
  });

  const fixture = TestBed.createComponent(OrdersV1Component);
  const component = fixture.componentInstance;

  const notifier = TestBed.inject(NotifierService);

  const notifierSpy = spyOn(notifier, 'warning').and.callThrough();

  const ordersService = TestBed.inject(OrdersWithSocketService);

  fixture.detectChanges();

  return {
    fixture,
    component,
    notifierSpy,
    ordersService,
  };
};

describe('OrdersV1Component', () => {
  it('should create', () => {
    const { component } = setup();

    expect(component).toBeTruthy();
  });

  xit('should update orders when orderTypeControl is changed', fakeAsync(() => {
    // const { component, fixture } = setup();
    // tick(1000);
    // let result: any;
    // component.orders$.subscribe(o => {
    //   result = o;
    // });
    // component.orderTypeControl.setValue('Todos');
    // fixture.detectChanges();
    // tick(1000);
  }));

  it('should call refresh when search is executed', () => {
    // const search$ = spyOn(component.searchValue$, 'next');
    // component.search('sss');
    // expect(search$).toHaveBeenCalledTimes(1);
    // expect(socketServiceMock.refresh).toHaveBeenCalledTimes(1);
  });

  it('should call internalNavigation when goToNewOrderRoute is executed', () => {
    // component.newOrder();
    // expect(redirectionServiceMock.internalNavigation).toHaveBeenCalledWith([
    //   posAppRoutes.aplazoRoot,
    //   posAppRoutes.aplazoLayout,
    //   posAppRoutes.securedCart,
    // ]);
  });

  it('should call open method when cancelOrder is executed', async () => {
    const { component, fixture } = setup();
    component.cancelOrder(ordersDummy[0]);

    fixture.detectChanges();
  });

  it('should call open method when sharePaymentLink is executed', async () => {
    const { component, fixture } = setup();
    component.sharePaymentLink(ordersDummy[0]);

    fixture.detectChanges();
  });

  it('should display a warning notification on payment error', fakeAsync(() => {
    const { notifierSpy, ordersService } = setup();

    const error = {
      loanId: 12345,
      cashierMessage: 'Payment failed due to insufficient funds.',
      branchId: 36,
    };

    (ordersService.paymentFailure$ as Subject<any>).next(error);
    tick(450); // Advance time for auditTime(300)

    expect(notifierSpy).toHaveBeenCalledWith({
      title: 'Alerta',
      message: `[Orden #${error.loanId}] ${error.cashierMessage}`,
    });
    flush();
  }));

  it('should not display a warning notification if paymentFailure$ emits null or an error without cashierMessage', fakeAsync(() => {
    const { notifierSpy, ordersService } = setup();

    (ordersService.paymentFailure$ as Subject<any>).next(null);
    tick(300);
    expect(notifierSpy).not.toHaveBeenCalled();

    (ordersService.paymentFailure$ as Subject<any>).next({ loanId: 67890 });
    tick(300);
    expect(notifierSpy).not.toHaveBeenCalled();
    flush();
  }));

  it('should only display warning once for identical consecutive payment errors', fakeAsync(() => {
    const { notifierSpy, ordersService } = setup();

    const error = {
      loanId: 12345,
      cashierMessage: 'Payment failed due to insufficient funds.',
      createdAt: new Date(),
      branchId: 36, // Match the branch ID from setup function
    };

    (ordersService.paymentFailure$ as Subject<any>).next(error);
    tick(300);
    expect(notifierSpy).toHaveBeenCalledTimes(1);

    // Emit the same error again within the auditTime window
    (ordersService.paymentFailure$ as Subject<any>).next(error);
    tick(300);
    expect(notifierSpy).toHaveBeenCalledTimes(1); // Should still be 1 due to distinctUntilChanged

    // Emit a different error
    const differentError = {
      loanId: 54321,
      cashierMessage: 'Another payment issue.',
      createdAt: new Date(),
      branchId: 36, // Match the branch ID from setup function
    };
    (ordersService.paymentFailure$ as Subject<any>).next(differentError);
    tick(300);
    expect(notifierSpy).toHaveBeenCalledTimes(2); // Should be 2 now
  }));

  it('should display warning for different consecutive payment errors', fakeAsync(() => {
    const { notifierSpy, ordersService } = setup();

    const error1 = {
      loanId: 111,
      cashierMessage: 'Error 1',
      createdAt: new Date(),
      branchId: 36, // Match the branch ID from setup function
    };
    const error2 = {
      loanId: 222,
      cashierMessage: 'Error 2',
      createdAt: new Date(),
      branchId: 36, // Match the branch ID from setup function
    };

    (ordersService.paymentFailure$ as Subject<any>).next(error1);
    tick(300);
    expect(notifierSpy).toHaveBeenCalledTimes(1);

    (ordersService.paymentFailure$ as Subject<any>).next(error2);
    tick(300);
    expect(notifierSpy).toHaveBeenCalledTimes(2);
  }));
});
