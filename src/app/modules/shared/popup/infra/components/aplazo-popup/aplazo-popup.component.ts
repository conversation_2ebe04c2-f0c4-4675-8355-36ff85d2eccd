import { Component, inject, signal } from '@angular/core';
import { DialogRef } from '@ngneat/dialog';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';

@Component({
  selector: 'aplazo-popup',
  standalone: true,
  template: `
    <div
      class="w-full h-full flex items-center justify-center bg-black bg-opacity-60">
      <div
        class="bg-white rounded-lg shadow-lg p-8 min-w-[320px] max-w-xl relative">
        <button
          type="button"
          class="absolute top-2 right-2 text-gray-400 hover:text-gray-700 text-2xl font-bold focus:outline-none"
          aria-label="Cerrar"
          (click)="close()">
          &times;
        </button>
        <div [innerHTML]="safeHtml()"></div>
      </div>
    </div>
  `,
})
export class AplazoPopupComponent {
  private dialogRef = inject(DialogRef);
  private sanitizer = inject(DomSanitizer);

  safeHtml = signal<SafeHtml>(
    this.sanitizer.bypassSecurityTrustHtml(
      this.dialogRef.data?.htmlContent || '<p>No content</p>'
    )
  );

  close() {
    this.dialogRef.close();
  }
}
