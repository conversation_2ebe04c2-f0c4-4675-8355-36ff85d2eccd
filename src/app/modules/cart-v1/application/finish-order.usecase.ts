import { Injectable, inject } from '@angular/core';
import { ObservabilityService } from '@aplazo/front-observability';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
  RedirectionService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { StatsigService } from '@statsig/angular-bindings';
import { nanoid } from 'nanoid';
import {
  EMPTY,
  MonoTypeOperatorFunction,
  Observable,
  OperatorFunction,
  catchError,
  defer,
  finalize,
  map,
  of,
  pipe,
  switchMap,
  take,
  tap,
  withLatestFrom,
} from 'rxjs';
import { POS_ENVIRONMENT_CORE } from '../../../../app-core/domain/environments';
import { StoreService } from '../../../core/application/services/store.service';
import { LoanRequest } from '../../../core/domain/cart';
import { POS_APP_ROUTES } from '../../../core/domain/config/app-routes.core';
import { Branch } from '../../../core/domain/entities';
import { IntegrationType } from '../../../core/domain/integration-type';
import { Order } from '../../../core/domain/order.interface';
import {
  IProductRequestUIDto,
  ProductRequest,
} from '../../../core/domain/product-request';
import { PosOfflineRepository } from '../../../core/domain/repositories/pos-offline.repository';
import { ShowPaymentLinkDialogService } from '../../share-payment-link/application/services/show-payment-link-dialog.service';
import { ShowOnlyLoanIdDialogUsecase } from '../../share-payment-link/application/show-only-loan-id-dialog.usecase';
import { ShowOnlyQrDialogUsecase } from '../../share-payment-link/application/show-only-qr-dialog.usecase';

export interface CreateOrderRequestDto {
  products: IProductRequestUIDto[];
  totalPrice: string;
  sellsAgentId: string;
  preloanCode?: string;
}

export const noSelectedBranchError =
  'Error en creación de orden: no hay sucursal seleccionada';

export const maxLoanAmountByIntegrationTypeError =
  'Error en creación de orden: El monto máximo permitido es $6,000.00 MXN';

@Injectable({ providedIn: 'root' })
export class FinishOrderUseCase
  implements BaseUsecase<CreateOrderRequestDto, Observable<Order>>
{
  readonly #storeService = inject(StoreService);
  readonly #loader = inject(LoaderService);
  readonly #notifier = inject(NotifierService);
  readonly #posRepository = inject(PosOfflineRepository);
  readonly #showPaymentLink = inject(ShowPaymentLinkDialogService);
  readonly #showQrPaymentLink = inject(ShowOnlyQrDialogUsecase);
  readonly #showLoanId = inject(ShowOnlyLoanIdDialogUsecase);
  readonly #statsigService = inject(StatsigService);
  readonly #redirecter = inject(RedirectionService);
  readonly #observability = inject(ObservabilityService);
  readonly #usecaseError = inject(UseCaseErrorHandler);
  readonly #appRoutes = inject(POS_APP_ROUTES);
  readonly #environment = inject(POS_ENVIRONMENT_CORE);

  readonly #maxLoanAmountForRestrictedIntegrationType = 6000;

  get maxLoanAmountForRestrictedIntegrationType(): number {
    return this.#maxLoanAmountForRestrictedIntegrationType;
  }

  execute(orderRequest: CreateOrderRequestDto): Observable<Order> {
    const idLoader = this.#loader.show();

    return this.#storeService.selectedBranch$.pipe(
      take(1),

      this.validateEmptyBranch(),

      this.setMinimunActiveShareLinkMethods(),

      this.validateMaxLoanAmountByIntegrationType('WM_APLAZO', orderRequest),

      this.createLoanRequestDto(orderRequest),

      switchMap(order => this.#posRepository.createLoan(order).pipe(take(1))),

      catchError((error: unknown) => {
        if (error instanceof RuntimeMerchantError) {
          this.#observability.sendCustomLog({
            message: `controled runtime >> ${error.code}::_::${
              error.origin ? error.origin : error.message
            }`,
            status: 'warn',
          });
        } else {
          const message =
            typeof error !== 'string'
              ? (error as any)?.error?.message ??
                (error as any)?.error?.error ??
                (error as any)?.message ??
                'Estamos trabajando para resolver el error. Por favor, reintente más tarde o contacte a soporte.'
              : error;

          this.#observability.sendCustomLog({
            message: `uncontroled runtime >> ${message}`,
            status: 'error',
          });
        }
        return this.#usecaseError.handle(error, EMPTY);
      }),

      this.sideEffects(idLoader)
    );
  }

  private validateEmptyBranch(): OperatorFunction<Branch | null, Branch> {
    return pipe(
      map(branchOrNull => {
        if (!branchOrNull) {
          throw new RuntimeMerchantError(
            noSelectedBranchError,
            'FinishOrder::EmptyBranch',
            'finish-order-usecase-error'
          );
        }

        return branchOrNull;
      })
    );
  }

  private setMinimunActiveShareLinkMethods(): MonoTypeOperatorFunction<Branch> {
    return pipe(
      map(branch => {
        if (!branch.shareLinkFlags) {
          const branchWithShareLinkFlags = {
            ...branch,
            shareLinkFlags: {
              isQrAllowed: true,
              isSMSAllowed: false,
              isWhatsappAllowed: false,
            },
          };

          this.#storeService.setSelectedBranch(branchWithShareLinkFlags);

          return branchWithShareLinkFlags;
        }

        return branch;
      })
    );
  }

  private validateMaxLoanAmountByIntegrationType(
    restrictedIntegrationType: IntegrationType,
    order: CreateOrderRequestDto
  ): MonoTypeOperatorFunction<Branch> {
    return pipe(
      withLatestFrom(this.#storeService.integrationType$),

      map(([branch, integrationType]) => {
        if (
          integrationType === restrictedIntegrationType &&
          +order.totalPrice > this.#maxLoanAmountForRestrictedIntegrationType
        ) {
          throw new RuntimeMerchantError(
            maxLoanAmountByIntegrationTypeError,
            'FinishOrder::MaxLoanAmountByIntegrationType',
            'finish-order-usecase-error'
          );
        }

        return branch;
      })
    );
  }

  private createLoanRequestDto(
    order: CreateOrderRequestDto
  ): OperatorFunction<Branch, LoanRequest> {
    return pipe(
      map(branch => {
        const hostname = this.#environment.hostname;
        const productsRequest = order.products.map(item =>
          ProductRequest.create({
            sku: item.sku,
            name: item.name,
            price: item.price.toString(),
            quantity: item.quantity,
          })
        );

        return LoanRequest.create({
          hostname,
          cartId: nanoid(),
          branchId: branch.id,
          products: productsRequest,
          totalPrice: order.totalPrice,
          sellsAgentId: +order.sellsAgentId,
        });
      })
    );
  }

  private sideEffects(idLoader: string): MonoTypeOperatorFunction<Order> {
    return pipe(
      tap(() => {
        this.#redirecter.internalNavigation([
          this.#appRoutes.aplazoRoot,
          this.#appRoutes.aplazoLayout,
          this.#appRoutes.securedOrders,
        ]);
      }),

      tap(() => {
        this.#notifier.success({
          title: 'Orden generada correctamente',
        });
      }),

      switchMap(createdLoan => {
        const isQrFlagEnabled = this.#statsigService.checkGate(
          'b2b_front_posui_finish_with_qr'
        );
        const isOnlyLoanIdEnabled = this.#statsigService.checkGate(
          'b2b_front_posui_finish_with_only_phone'
        );

        if (isQrFlagEnabled) {
          return defer(() =>
            this.#showQrPaymentLink.execute(createdLoan, isOnlyLoanIdEnabled)
          ).pipe(map(() => createdLoan));
        }

        if (isOnlyLoanIdEnabled) {
          return defer(() => this.#showLoanId.execute(createdLoan)).pipe(
            map(() => createdLoan)
          );
        }

        this.#showPaymentLink.execute(createdLoan);
        return of(createdLoan);
      }),

      take(1),

      finalize(() => {
        this.#loader.hide(idLoader);
      })
    );
  }
}
