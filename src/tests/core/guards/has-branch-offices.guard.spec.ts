import { TestBed } from '@angular/core/testing';
import { RouterStateSnapshot, UrlTree } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { lastValueFrom, of } from 'rxjs';
import { StoreService } from '../../../app/core/application/services/store.service';
import {
  POS_APP_ROUTES,
  ROUTE_CONFIG,
  RouteConfig,
} from '../../../app/core/domain/config/app-routes.core';
import { MerchantConfig } from '../../../app/core/domain/merchant-config';
import { HasBranchOfficesGuard } from '../../../app/core/guards/has-branch-offices.guard';
import { MerchantsService } from '../../../app/core/services/merchants.service';
import { merchantConfigDummy } from '../../../mocks/merchant-config.dummy';
import { LocalStore } from '../services/impl/local-store.service';

describe('HasBranchOfficesGuard', () => {
  let guard: HasBranchOfficesGuard;
  let store: StoreService;
  let service: jasmine.SpyObj<MerchantsService>;
  let routes: RouteConfig;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      providers: [
        {
          provide: StoreService,
          useClass: LocalStore,
        },
        {
          provide: POS_APP_ROUTES,
          useValue: ROUTE_CONFIG,
        },
        {
          provide: MerchantsService,
          useValue: jasmine.createSpyObj('MerchantsService', [
            'getMerchantConfig',
          ]),
        },
      ],
    });

    guard = TestBed.inject(HasBranchOfficesGuard);

    store = TestBed.inject(StoreService);
    service = TestBed.inject(
      MerchantsService
    ) as jasmine.SpyObj<MerchantsService>;
    routes = TestBed.inject(POS_APP_ROUTES);
  });

  it('should be created', () => {
    expect(guard).toBeTruthy();
  });

  it('should call merchantsService.getMerchantConfig when current route includes securedSelectBranch', async () => {
    service.getMerchantConfig.and.returnValue(of({} as any));

    const fakeCurrentRouteState = {
      url: `/home/<USER>
    } as RouterStateSnapshot;

    await lastValueFrom(guard.canActivate(null, fakeCurrentRouteState));

    expect(service.getMerchantConfig).toHaveBeenCalledTimes(1);
  });

  it('should allows navigation when merchantsService.getMerchantConfig return a list of branches with items', async () => {
    service.getMerchantConfig.and.returnValue(
      of({
        ...merchantConfigDummy,
        createdAt: merchantConfigDummy.createdAt,
        updatedAt: merchantConfigDummy.updatedAt,
      })
    );
    const fakeCurrentRouteState = {
      url: `/home/<USER>
    } as RouterStateSnapshot;

    const resolution = await lastValueFrom(
      guard.canActivate(null, fakeCurrentRouteState)
    );

    expect(resolution).toBeTrue();
  });

  it('should deny navigation when merchantsService.getMerchantConfig return an empty list of branches', async () => {
    service.getMerchantConfig.and.returnValue(
      of({
        ...merchantConfigDummy,
        createdAt: merchantConfigDummy.createdAt,
        updatedAt: merchantConfigDummy.updatedAt,
        branches: [],
      })
    );
    const fakeCurrentRouteState = {
      url: `/home/<USER>
    } as RouterStateSnapshot;

    const resolution = await lastValueFrom(
      guard.canActivate(null, fakeCurrentRouteState)
    );

    expect(resolution).toBeInstanceOf(UrlTree);
    expect(resolution.toString()).toBe(`/${routes.securedNewBranch}`);
  });

  it('should not call merchantsService.getMerchantConfig when current route not includes securedSelectBranch', async () => {
    const fakeCurrentRouteState = {
      url: '/home/<USER>/branch/selection/route',
    } as RouterStateSnapshot;

    await lastValueFrom(guard.canActivate(null, fakeCurrentRouteState));

    expect(service.getMerchantConfig).toHaveBeenCalledTimes(0);
  });

  it('should allows navigation when hasBranches$ is true and current route not includes securedSelectBranch', async () => {
    store.setMerchantConfig(merchantConfigDummy as unknown as MerchantConfig);
    const fakeCurrentRouteState = {
      url: '/home/<USER>/branch/selection/route',
    } as RouterStateSnapshot;

    const resolution = await lastValueFrom(
      guard.canActivate(null, fakeCurrentRouteState)
    );

    expect(resolution).toBeTrue();
  });

  it('should deny navigation when hasBranches$ is false and current route not includes securedSelectBranch', async () => {
    const fakeCurrentRouteState = {
      url: '/home/<USER>/branch/selection/route',
    } as RouterStateSnapshot;

    const resolution = await lastValueFrom(
      guard.canActivate(null, fakeCurrentRouteState)
    );

    expect(resolution).toBeInstanceOf(UrlTree);
    expect(resolution.toString()).toBe(`/${routes.securedNewBranch}`);
  });
});
