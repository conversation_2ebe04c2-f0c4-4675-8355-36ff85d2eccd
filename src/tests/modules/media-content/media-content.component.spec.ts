import { AsyncPipe } from '@angular/common';
import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { NotifierService } from '@aplazo/merchant/shared';
import { provideNotifierTesting } from '@aplazo/merchant/shared-testing';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import {
  AplazoCommonMessageComponent,
  AplazoCommonMessageComponents,
} from '@aplazo/shared-ui/merchant';
import {
  AplazoTabGroupComponent,
  AplazoTabsComponents,
} from '@aplazo/shared-ui/tabs';
import { AplazoYoutubeDialogComponent } from '@aplazo/shared-ui/youtube';
import { iconPlayCircle } from '@aplazo/ui-icons';
import { DialogService } from '@ngneat/dialog';
import { StatsigService } from '@statsig/angular-bindings';
import { firstValueFrom, of } from 'rxjs';
import { AnalyticsService } from '../../../app/core/application/services/analytics.service';
import { StoreService } from '../../../app/core/application/services/store.service';
import {
  IMediaContentUIDto,
  ITrainingMediaContentUIDto,
  MappedMediaContentTags,
} from '../../../app/core/domain/notification';
import { NotificationsHandlerService } from '../../../app/core/services/notifications-handler.service';
import { AplazoMediaCardThumbnailComponent } from '../../../app/modules/media-content/media-content-card.component';
import { MediaContentComponent } from '../../../app/modules/media-content/media-content.component';

const setup = (content?: ITrainingMediaContentUIDto | null) => {
  TestBed.configureTestingModule({
    imports: [
      AplazoCardComponent,
      AplazoIconComponent,
      AplazoButtonComponent,
      AplazoCommonMessageComponents,
      AplazoTabsComponents,
      AplazoMediaCardThumbnailComponent,
      AsyncPipe,
    ],
    providers: [
      provideNotifierTesting(),
      AplazoIconRegistryService,
      {
        provide: NotificationsHandlerService,
        useValue: {
          getTrainingContent$: () => {
            if (content === null) {
              return of({
                tags: [],
                content: [],
              });
            }
            return of(content);
          },
        },
      },
      {
        provide: DialogService,
        useValue: {
          open: (params: any) => {
            return {} as any;
          },
        },
      },
      {
        provide: AnalyticsService,
        useValue: {
          track: () => {
            return void 0;
          },
        },
      },
      {
        provide: StatsigService,
        useValue: {
          logEvent: () => {
            return void 0;
          },
        },
      },
      {
        provide: StoreService,
        useValue: {
          getMerchantId$: () => {
            return of(199);
          },
          selectedBranch$: of(36),
        },
      },
    ],
  });

  const iconRegistry = TestBed.inject(AplazoIconRegistryService);
  const registerIconsSpy = spyOn(iconRegistry, 'registerIcons');
  const dialogService = TestBed.inject(DialogService);
  const notifier = TestBed.inject(NotifierService);
  const openDialogSpy = spyOn(dialogService, 'open');
  const warningSpy = spyOn(notifier, 'warning').and.callThrough();
  const analyticsService = TestBed.inject(AnalyticsService);
  const trackSpy = spyOn(analyticsService, 'track');
  const statsigService = TestBed.inject(StatsigService);
  const logEventSpy = spyOn(statsigService, 'logEvent');

  const fixture = TestBed.createComponent(MediaContentComponent);
  const component = fixture.componentInstance;

  fixture.detectChanges();

  return {
    fixture,
    component,
    registerIconsSpy,
    openDialogSpy,
    warningSpy,
    trackSpy,
    logEventSpy,
  };
};

describe('MediaContentComponent', () => {
  const dummyContent = {
    id: 1,
    title: 'test',
    description: 'test',
    isValidVideoUrl: true,
    urlVideo: 'http://a.com',
    urlThumbnail: 'http://b.com',
    tags: ['CUSTOMER'] as unknown as MappedMediaContentTags[],
  };

  it('should create', () => {
    const { component } = setup();

    expect(component).toBeTruthy();
  });

  it('should register the play-circle icon on creation', () => {
    const { registerIconsSpy } = setup();
    expect(registerIconsSpy).toHaveBeenCalledWith([iconPlayCircle]);
  });

  describe('Initial State and Rendering', () => {
    it('should show empty message when content is null', async () => {
      const { fixture, component } = setup(null);

      const emptyMessage = fixture.debugElement.query(
        By.directive(AplazoCommonMessageComponent)
      );

      expect(emptyMessage).toBeDefined();
      expect(await firstValueFrom(component.hasContent$)).toBe(false);
    });

    it('should show empty message when there is no content', async () => {
      const { fixture, component } = setup({ tags: [], content: [] });

      const emptyMessage = fixture.debugElement.query(
        By.directive(AplazoCommonMessageComponent)
      );

      expect(emptyMessage).toBeDefined();

      const description = emptyMessage.query(
        By.css('.aplazo-common-message__description')
      );

      expect(description.nativeElement.textContent?.trim()).toBe(
        'Aún no tenemos contenido multimedia, espéralo muy pronto'
      );
      expect(await firstValueFrom(component.hasContent$)).toBe(false);
    });

    it('should show empty message when content.tags is empty', async () => {
      const { fixture, component } = setup({
        tags: [],
        content: [dummyContent],
      });

      const emptyMessage = fixture.debugElement.query(
        By.directive(AplazoCommonMessageComponent)
      );

      expect(emptyMessage).toBeDefined();
      expect(await firstValueFrom(component.hasContent$)).toBe(false);
    });

    it('should show empty message when content.content is empty', async () => {
      const { fixture, component } = setup({
        tags: ['CUSTOMER' as any],
        content: [],
      });

      const emptyMessage = fixture.debugElement.query(
        By.directive(AplazoCommonMessageComponent)
      );

      expect(emptyMessage).toBeDefined();
      expect(await firstValueFrom(component.hasContent$)).toBe(false);
    });
  });

  it('should show content when there is content', async () => {
    const expectedResponse = [
      {
        id: 142,
        commType: 'TRAINING',
        title: 'FAQ - TRAINING 04/03/2024',
        description:
          'TEST SECURITY TRAINING IN APLAZOVERSITY - REQUIRED TO ALL',
        status: 'A',
        platform: 'P',
        merchants: '',
        branchList: [],
        lastUpdate: '2025-03-06T23:20:15.402400Z',
        applyAll: true,
        complement: {
          urlVideo: 'https://youtu.be/LVVssVlPBMc',
          urlThumbnail:
            'https://cdn.aplazo.mx/landing-pages/highlight/vc-highlight.webp',
          tags: ['CUSTOMER', 'SALES', 'FAQ'],
        },
      },
    ];

    const { fixture, component } = setup({
      tags: ['FAQ', 'CUSTOMER', 'SALES'] as any,
      content: expectedResponse.map(i => {
        return {
          id: i.id,
          title: i.title,
          description: i.description,
          isValidVideoUrl: true,
          urlVideo: i.complement.urlVideo,
          urlThumbnail: i.complement.urlThumbnail,
          tags: i.complement.tags as MappedMediaContentTags[],
        };
      }),
    });

    const thumbnail = fixture.debugElement.query(
      By.directive(AplazoMediaCardThumbnailComponent)
    );
    const tabGroup = fixture.debugElement.query(
      By.directive(AplazoTabGroupComponent)
    );

    expect(thumbnail).toBeDefined();
    expect(tabGroup).toBeDefined();
    expect(await firstValueFrom(component.hasContent$)).toBe(true);
  });

  describe('Tab Functionality', () => {
    const contentWithTabs = {
      tags: ['FAQ', 'CUSTOMER', 'SALES'] as unknown as MappedMediaContentTags[],
      content: [
        {
          id: 1,
          title: 'FAQ 1',
          description: 'test',
          isValidVideoUrl: true,
          urlVideo: 'http://a.com',
          urlThumbnail: 'http://b.com',
          tags: ['FAQ'] as unknown as MappedMediaContentTags[],
        },
        {
          id: 2,
          title: 'Customer 1',
          description: 'test',
          isValidVideoUrl: false,
          urlVideo: 'http://a.com',
          urlThumbnail: 'http://b.com',
          tags: ['CUSTOMER'] as unknown as MappedMediaContentTags[],
        },
        {
          id: 3,
          title: 'Customer 2',
          description: 'test',
          isValidVideoUrl: true,
          urlVideo: 'http://a.com',
          urlThumbnail: 'http://b.com',
          tags: ['CUSTOMER'] as unknown as MappedMediaContentTags[],
        },
      ],
    } as ITrainingMediaContentUIDto;

    it('should generate the correct number of tab options from tags', async () => {
      const { component } = setup(contentWithTabs);
      const tabOptions = await firstValueFrom(component.tabOptions$);
      expect(tabOptions.length).toBe(3);
      expect(tabOptions[0].label).toBe('FAQ');
      expect(tabOptions[1].label).toBe('CUSTOMER');
      expect(tabOptions[2].label).toBe('SALES');
    });

    it('should display items for the first tab by default', fakeAsync(() => {
      const { component, fixture } = setup(contentWithTabs);
      let items: IMediaContentUIDto[] = [];
      component.items$.subscribe(i => (items = i));
      fixture.detectChanges();
      tick();

      expect(items.length).toBe(1);
      expect(items[0].title).toBe('FAQ 1');

      fixture.detectChanges();

      const mediaCards = fixture.debugElement.queryAll(
        By.directive(AplazoMediaCardThumbnailComponent)
      );
      expect(mediaCards.length).toBe(1);
    }));

    it('should update displayed items when a different tab is selected', fakeAsync(() => {
      const { component, fixture } = setup(contentWithTabs);
      let items: IMediaContentUIDto[] = [];
      component.items$.subscribe(i => (items = i));
      component.changeTab({
        index: 1,
      });
      fixture.detectChanges();
      tick();

      expect(items.length).toBe(2);
      expect(items[0].title).toBe('Customer 1');
      expect(items[1].title).toBe('Customer 2');
      fixture.detectChanges();

      const mediaCards = fixture.debugElement.queryAll(
        By.directive(AplazoMediaCardThumbnailComponent)
      );
      expect(mediaCards.length).toBe(2);
    }));

    it('should show no items if a tab with no associated content is selected', fakeAsync(() => {
      const { component, fixture } = setup(contentWithTabs);
      let items: IMediaContentUIDto[] = [];
      component.items$.subscribe(i => (items = i));
      component.changeTab({
        index: 2,
      }); // SALES tab has no content in `contentWithTabs`
      fixture.detectChanges();
      tick();

      expect(items.length).toBe(0);
      fixture.detectChanges();

      const mediaCards = fixture.debugElement.queryAll(
        By.directive(AplazoMediaCardThumbnailComponent)
      );
      expect(mediaCards.length).toBe(0);
    }));

    it('should handle an out-of-bounds tab index gracefully', fakeAsync(() => {
      const { component, fixture } = setup(contentWithTabs);
      let items: IMediaContentUIDto[] = [];
      component.items$.subscribe(i => (items = i));
      component.changeTab({
        index: 99,
      });
      fixture.detectChanges();
      tick();

      expect(items.length).toBe(0);
      fixture.detectChanges();

      const mediaCards = fixture.debugElement.queryAll(
        By.directive(AplazoMediaCardThumbnailComponent)
      );
      expect(mediaCards.length).toBe(0);
    }));
  });

  describe('Video Playback (`openVideo` method)', () => {
    const videoItem: IMediaContentUIDto = {
      id: 1,
      title: 'Test Video',
      description: 'A great video',
      isValidVideoUrl: true,
      urlVideo: 'https://www.youtube.com/watch?v=VIDEO_ID',
      urlThumbnail: 'http://b.com',
      tags: [],
    };

    it('should open the youtube dialog for a valid youtube.com/watch URL', () => {
      const { component, openDialogSpy } = setup();
      component.openVideo(videoItem);
      expect(openDialogSpy).toHaveBeenCalledWith(AplazoYoutubeDialogComponent, {
        data: {
          videoId: 'VIDEO_ID',
          title: videoItem.title,
          description: videoItem.description,
        },
        minWidth: '250px',
        maxHeight: '90vh',
        minHeight: '450px',
      });
    });

    it('should open the youtube dialog for a valid youtu.be URL', () => {
      const { component, openDialogSpy } = setup();
      const shortUrlItem = {
        ...videoItem,
        urlVideo: 'https://youtu.be/SHORT_ID',
      };
      component.openVideo(shortUrlItem);
      expect(openDialogSpy).toHaveBeenCalledWith(AplazoYoutubeDialogComponent, {
        data: {
          videoId: 'SHORT_ID',
          title: shortUrlItem.title,
          description: shortUrlItem.description,
        },
        minWidth: '250px',
        maxHeight: '90vh',
        minHeight: '450px',
      });
    });

    it('should throw an error if isValidVideoUrl is false', () => {
      const { component } = setup();
      const invalidItem = { ...videoItem, isValidVideoUrl: false };
      expect(() => component.openVideo(invalidItem)).toThrow(
        new Error('There is no enough data to open the video')
      );
    });

    it('should throw an error for a non-youtube URL', () => {
      const { component } = setup();
      const nonYoutubeItem = {
        ...videoItem,
        urlVideo: 'https://vimeo.com/12345',
      };
      expect(() => component.openVideo(nonYoutubeItem)).toThrow(
        new Error('There is no valid youtube video url')
      );
    });

    it('should show a warning notification if the youtube URL has no video ID', () => {
      const { component, warningSpy, openDialogSpy } = setup({
        tags: [],
        content: [
          {
            ...videoItem,
            urlVideo: 'https://www.youtube.com/watch?v=',
          },
        ],
      });

      component.openVideo({
        ...videoItem,
        urlVideo: 'https://www.youtube.com/watch?v=',
      });

      expect(warningSpy).toHaveBeenCalledWith({
        title: 'Video no encontrado',
      });
      expect(openDialogSpy).not.toHaveBeenCalled();
    });

    it('should not display a play button if isValidVideoUrl is false', fakeAsync(() => {
      const content = {
        tags: ['TEST'] as unknown as MappedMediaContentTags[],
        content: [{ ...videoItem, isValidVideoUrl: false }],
      } as ITrainingMediaContentUIDto;
      const { fixture } = setup(content);
      tick();
      fixture.detectChanges();
      const playButton = fixture.debugElement.query(
        By.css('button[aplzButton]')
      );
      expect(playButton).toBeNull();
    }));
  });

  describe('Analytics and Tracking', () => {
    const videoItem: IMediaContentUIDto = {
      id: 1,
      title: 'Test Video',
      description: 'A great video',
      isValidVideoUrl: true,
      urlVideo: 'https://www.youtube.com/watch?v=VIDEO_ID',
      urlThumbnail: 'http://b.com',
      tags: [],
    };

    it('should track analytics event when a video is opened', () => {
      const { component, trackSpy } = setup();
      component.openVideo(videoItem);
      expect(trackSpy).toHaveBeenCalledWith('buttonClick', {
        buttonName: 'aplazoVersityOpenVideo',
        timestamp: jasmine.any(Number),
        title: videoItem.title,
      });
    });

    it('should log statsig event when a video is opened', fakeAsync(() => {
      const { component, logEventSpy } = setup();
      component.openVideo(videoItem);
      tick();
      expect(logEventSpy).toHaveBeenCalledWith(
        'posui_aplazoversity_open_video',
        videoItem.title,
        {
          merchantId: '199',
          branchId: '36',
        }
      );
    }));
  });
});
