import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { POS_ENVIRONMENT_CORE } from '../../../../../../app-core/domain/environments';
import {
  NoInternetPurchaseOTPRequest,
  NoInternetPurchaseValidateOTPRequest,
} from '../../domain/entities/no-internet-purchase';
import { NoInternetPurchaseRepository } from '../../domain/repositories/no-internet-purchase.repository';

@Injectable({
  providedIn: 'root',
})
export class NoInternetPurchaseWithHttp
  implements NoInternetPurchaseRepository
{
  readonly #http = inject(HttpClient);
  readonly #environment = inject(POS_ENVIRONMENT_CORE);

  requestOTP(request: NoInternetPurchaseOTPRequest): Observable<void> {
    return this.#http.post<void>(
      `${this.#environment.bifrostUrl}/api/v1/no-internet-purchases`,
      request
    );
  }
  validateOTP(request: NoInternetPurchaseValidateOTPRequest): Observable<void> {
    return this.#http.post<void>(
      `${this.#environment.bifrostUrl}/api/v1/no-internet-purchases/validate-code`,
      request
    );
  }
}
