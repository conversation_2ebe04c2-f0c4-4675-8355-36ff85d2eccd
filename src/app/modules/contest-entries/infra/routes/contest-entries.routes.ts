import { inject } from '@angular/core';
import { Route } from '@angular/router';
import { ROUTE_CONFIG } from '../../../../core/domain/config/app-routes.core';
import { GetAllContestsRegions } from '../../application/usecases/retrieve-regions.usecase';
import { provideContest, withScopedLoader } from '../config/providers';
import { AplazoContestEntriesLayoutComponent } from '../layout/contest-entries.layout.component';
import { AplazoContestPositionComponent } from '../pages/contest-position/contest-position.component';
import { AplazoContestRegistrationComponent } from '../pages/contest-registration/contest-registration.component';

const contestRoutes: Route[] = [
  {
    path: '',
    component: AplazoContestEntriesLayoutComponent,
    providers: [provideContest(withScopedLoader())],
    children: [
      {
        path: '',
        pathMatch: 'full',
        redirectTo: ROUTE_CONFIG.contestRegistration,
      },
      {
        path: ROUTE_CONFIG.contestRegistration,
        component: AplazoContestRegistrationComponent,
        resolve: {
          regions: () => inject(GetAllContestsRegions).execute(),
        },
      },
      {
        path: ROUTE_CONFIG.contestPosition,
        component: AplazoContestPositionComponent,
      },
    ],
  },
];

export default contestRoutes;
