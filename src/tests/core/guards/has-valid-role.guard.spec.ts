import { TestBed } from '@angular/core/testing';
import { UrlTree } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';
import { StoreService } from '../../../app/core/application/services/store.service';
import { hasValidRoleGuard } from '../../../app/core/guards/has-valid-role.guard';

const setup = (role = '') => {
  TestBed.configureTestingModule({
    imports: [RouterTestingModule],
    providers: [
      {
        provide: StoreService,
        useValue: {
          userRole$: of(role),
          clearStore: () => {
            void 0;
          },
        },
      },
    ],
  });
};

describe('hasValidRoleGuard', () => {
  it("should return UrlTree instance if role isn't valid", async () => {
    setup('invalid-role');

    const result = await TestBed.runInInjectionContext(
      hasValidRoleGuard as () => Promise<boolean>
    );

    expect(result).toBeInstanceOf(UrlTree);
    expect(result.toString()).toBe('/');
  });

  it('should allow navigation if role is ROLE_MERCHANT', async () => {
    setup('ROLE_MERCHANT');

    const result = await TestBed.runInInjectionContext(
      hasValidRoleGuard as () => Promise<boolean>
    );

    expect(result)
      .withContext('Should allow navigation if role is ROLE_MERCHANT')
      .toBeTrue();
  });

  it('should allow navigation if role is ROLE_SELL_AGENT', async () => {
    setup('ROLE_SELL_AGENT');

    const result = await TestBed.runInInjectionContext(
      hasValidRoleGuard as () => Promise<boolean>
    );

    expect(result)
      .withContext('Should allow navigation if role is ROLE_SELL_AGENT')
      .toBeTrue();
  });

  it('should allow navigation if role is ROLE_MANAGER', async () => {
    setup('ROLE_MANAGER');

    const result = await TestBed.runInInjectionContext(
      hasValidRoleGuard as () => Promise<boolean>
    );

    expect(result)
      .withContext('Should allow navigation if role is ROLE_MANAGER')
      .toBeTrue();
  });
});
