import { AsyncPipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  inject,
  input,
  ViewEncapsulation,
} from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { switchMap } from 'rxjs';
import { GetQrImageService } from './application/services/qr-image.service';

@Component({
  standalone: true,
  selector: 'aplazo-qr-image',
  template: `
    @if (result$ | async; as result) {
      @if (result.loading) {
        <div class="p-8 w-full flex justify-center items-center">
          <div
            role="status"
            class="flex items-center justify-center h-60 aspect-square bg-dark-background rounded-lg animate-pulse">
            <svg
              class="w-10 h-10 text-dark-secondary"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="currentColor"
              viewBox="0 0 16 20">
              <path
                d="M5 5V.13a2.96 2.96 0 0 0-1.293.749L.879 3.707A2.98 2.98 0 0 0 .13 5H5Z" />
              <path
                d="M14.066 0H7v5a2 2 0 0 1-2 2H0v11a1.97 1.97 0 0 0 1.934 2h12.132A1.97 1.97 0 0 0 16 18V2a1.97 1.97 0 0 0-1.934-2ZM9 13a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2Zm4 .382a1 1 0 0 1-1.447.894L10 13v-2l1.553-1.276a1 1 0 0 1 1.447.894v2.764Z" />
            </svg>
            <span class="sr-only">Loading...</span>
          </div>
        </div>
      } @else if (result.succeded) {
        <h3 class="text-lg font-medium mt-4 mb-2 text-center">
          Escanea el código para pagar.
        </h3>
        <img [src]="result.src" class="max-w-full text-center mx-auto mb-4" />
      } @else if (!result.succeded) {
        <h3 class="text-lg font-medium mt-4 mb-2 text-center">
          No se pudo generar el código QR.
        </h3>
        <img [src]="result.src" class="max-w-full text-center mx-auto mb-4" />
      }
    }
  `,
  imports: [AsyncPipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class QrImageComponent {
  readonly #qrImageService = inject(GetQrImageService);

  readonly loanId = input<number | null>(null);

  readonly #loanId$ = toObservable(this.loanId);

  readonly result$ = this.#loanId$.pipe(
    switchMap(loanId => this.#qrImageService.getImageSrc$(loanId))
  );
}
