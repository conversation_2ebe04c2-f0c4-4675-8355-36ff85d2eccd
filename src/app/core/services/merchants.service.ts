import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable, catchError, map, of } from 'rxjs';

import {
  POS_ENVIRONMENT_CORE,
  PosEnvironmentCoreType,
} from '../../../app-core/domain/environments';
import { UpdateMerchantConfigDTO } from '../../core/domain/dtos';
import { MerchantConfig } from '../../core/domain/merchant-config';

@Injectable({
  providedIn: 'root',
})
export class MerchantsService {
  constructor(
    private http: HttpClient,
    @Inject(POS_ENVIRONMENT_CORE)
    private environment: PosEnvironmentCoreType
  ) {}

  getMerchantConfig(): Observable<MerchantConfig> {
    return this.http
      .get<any>(`${this.environment.posApiUrl}/api/merchant_configs`)
      .pipe(
        map(res => this.mapResponseFromContent<MerchantConfig>(res)),
        catchError(error => {
          console.log('err', error);
          return of({} as MerchantConfig);
        })
      );
  }

  getMerchantDetails(): Observable<{
    email?: string;
    status?: string;
    channel?: string;
    segment?: string;
    name: string;
  }> {
    return this.http
      .get<any>(
        `${this.environment.posApiUrl}/api/merchant_configs/merchant-details`
      )
      .pipe(
        map(res => this.mapResponseFromContent<{ name: string }>(res)),
        catchError(error => {
          console.log('err', error);
          return of({ name: '' } as { name: string });
        })
      );
  }

  updateMerchantBranch(
    data: UpdateMerchantConfigDTO
  ): Observable<MerchantConfig> {
    return this.http
      .put<{ content: MerchantConfig }>(
        `${this.environment.posApiUrl}/api/merchant_configs`,
        data
      )
      .pipe(map(response => response.content));
  }

  private mapResponseFromContent<T>(response: any): T {
    if (
      Object.prototype.hasOwnProperty.call(response, 'content') &&
      Object.prototype.hasOwnProperty.call(response, 'code')
    ) {
      return response.content as T;
    }

    return response as T;
  }
}
