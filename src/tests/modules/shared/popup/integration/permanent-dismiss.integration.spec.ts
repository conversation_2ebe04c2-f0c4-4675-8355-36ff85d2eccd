import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { of } from 'rxjs';
import { PopupStorageService } from 'src/app/modules/shared/popup/application/services/popup-storage.service';
import { ShowPopupUseCase } from 'src/app/modules/shared/popup/application/usecases/show-popup.usecase';
import { AplazoPopupDialogService } from 'src/app/modules/shared/popup/infra/components/aplazo-popup/aplazo-popup-dialog.service';
import { PopupRepository } from 'src/app/modules/shared/popup/infra/repositories/popup.repository';
import { StoreService } from 'src/app/core/application/services/store.service';

/**
 * ✅ INTEGRATION TESTS FOR PERMANENT DISMISS FUNCTIONALITY
 * Tests the complete flow from user interaction to storage persistence
 */
describe('Permanent Dismiss Integration', () => {
  let storageService: PopupStorageService;
  let showPopupUseCase: ShowPopupUseCase;
  let popupRepo: jasmine.SpyObj<PopupRepository>;
  let dialogService: jasmine.SpyObj<AplazoPopupDialogService>;
  let storeService: jasmine.SpyObj<StoreService>;

  const mockBranch = { id: 123, name: 'Test Branch' };
  const mockPopups = [
    {
      popupId: 'popup1',
      priority: 1,
      startAt: '2024-01-01',
      endAt: '2024-12-31',
    },
    {
      popupId: 'popup2',
      priority: 2,
      startAt: '2024-01-01',
      endAt: '2024-12-31',
    },
  ];

  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear();

    // Create spies for dependencies
    popupRepo = jasmine.createSpyObj('PopupRepository', [
      'getAvailablePopups',
      'getPopupHtml',
    ]);
    dialogService = jasmine.createSpyObj('AplazoPopupDialogService', [
      'openPopup',
    ]);
    storeService = jasmine.createSpyObj('StoreService', [], {
      selectedBranch$: of(mockBranch),
    });

    TestBed.configureTestingModule({
      providers: [
        PopupStorageService, // ✅ Real service, not mocked
        ShowPopupUseCase,
        { provide: PopupRepository, useValue: popupRepo },
        { provide: AplazoPopupDialogService, useValue: dialogService },
        { provide: StoreService, useValue: storeService },
      ],
    });

    storageService = TestBed.inject(PopupStorageService);
    showPopupUseCase = TestBed.inject(ShowPopupUseCase);
  });

  afterEach(() => {
    localStorage.clear();
  });

  it('should persist permanent dismiss preference and prevent future displays', fakeAsync(() => {
    // Setup: Mock available popups
    popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));
    popupRepo.getPopupHtml.and.returnValue(of('<h2>Test Popup</h2>'));

    // Step 1: First execution should show popup1 (highest priority)
    let firstResult: any;
    showPopupUseCase.execute().subscribe(result => (firstResult = result));
    tick();

    expect(firstResult.success).toBe(true);
    expect(firstResult.popupShown.popupId).toBe('popup1');

    // Step 2: User marks popup1 as permanently dismissed
    storageService.markPopupAsPermanentlyDismissed('popup1', '123');

    // Step 3: Verify permanent dismiss is persisted
    expect(storageService.isPopupPermanentlyDismissed('popup1', '123')).toBe(true);

    // Step 4: Second execution should show popup2 (popup1 is permanently dismissed)
    let secondResult: any;
    showPopupUseCase.execute().subscribe(result => (secondResult = result));
    tick();

    expect(secondResult.success).toBe(true);
    expect(secondResult.popupShown.popupId).toBe('popup2');

    // Step 5: Mark popup2 as permanently dismissed too
    storageService.markPopupAsPermanentlyDismissed('popup2', '123');

    // Step 6: Third execution should show no popups (all permanently dismissed)
    let thirdResult: any;
    showPopupUseCase.execute().subscribe(result => (thirdResult = result));
    tick();

    expect(thirdResult.success).toBe(false);
    expect(thirdResult.reason).toBe('No unshown popups available');
  }));

  it('should maintain permanent dismiss across browser sessions', fakeAsync(() => {
    // Step 1: Mark popup as permanently dismissed
    storageService.markPopupAsPermanentlyDismissed('popup1', '123');

    // Step 2: Simulate browser restart by creating new service instance
    const newStorageService = new PopupStorageService();

    // Step 3: Verify permanent dismiss persists
    expect(newStorageService.isPopupPermanentlyDismissed('popup1', '123')).toBe(true);
  }));

  it('should handle mixed dismiss states correctly', fakeAsync(() => {
    const today = new Date().toISOString().split('T')[0];

    // Setup: popup1 shown today but not permanently dismissed
    storageService.markPopupAsShown('popup1', '123', today);
    
    // Setup: popup2 permanently dismissed
    storageService.markPopupAsPermanentlyDismissed('popup2', '123');

    popupRepo.getAvailablePopups.and.returnValue(of(mockPopups));

    // Should not show any popup:
    // - popup1: shown today (daily limit)
    // - popup2: permanently dismissed
    let result: any;
    showPopupUseCase.execute().subscribe(r => (result = r));
    tick();

    expect(result.success).toBe(false);
    expect(result.reason).toBe('No unshown popups available');
  }));

  it('should allow popup to be shown again after clearing permanent dismiss', fakeAsync(() => {
    // Step 1: Mark popup as permanently dismissed
    storageService.markPopupAsPermanentlyDismissed('popup1', '123');
    expect(storageService.isPopupPermanentlyDismissed('popup1', '123')).toBe(true);

    // Step 2: Clear permanent dismiss (simulate user changing preference)
    storageService.clearPermanentDismiss('popup1', '123');
    expect(storageService.isPopupPermanentlyDismissed('popup1', '123')).toBe(false);

    // Step 3: Popup should be available again
    popupRepo.getAvailablePopups.and.returnValue(of([mockPopups[0]])); // Only popup1
    popupRepo.getPopupHtml.and.returnValue(of('<h2>Test Popup</h2>'));

    let result: any;
    showPopupUseCase.execute().subscribe(r => (result = r));
    tick();

    expect(result.success).toBe(true);
    expect(result.popupShown.popupId).toBe('popup1');
  }));

  it('should handle localStorage errors gracefully', fakeAsync(() => {
    // Mock localStorage to throw errors
    spyOn(localStorage, 'getItem').and.throwError('Storage quota exceeded');
    spyOn(localStorage, 'setItem').and.throwError('Storage quota exceeded');

    // Should not throw errors
    expect(() => {
      storageService.markPopupAsPermanentlyDismissed('popup1', '123');
    }).not.toThrow();

    expect(() => {
      storageService.isPopupPermanentlyDismissed('popup1', '123');
    }).not.toThrow();

    // Should return false when storage fails
    expect(storageService.isPopupPermanentlyDismissed('popup1', '123')).toBe(false);
  }));

  it('should validate popup and branch parameters', fakeAsync(() => {
    // Should handle empty/invalid parameters gracefully
    expect(() => {
      storageService.markPopupAsPermanentlyDismissed('', '123');
    }).not.toThrow();

    expect(() => {
      storageService.markPopupAsPermanentlyDismissed('popup1', '');
    }).not.toThrow();

    expect(storageService.isPopupPermanentlyDismissed('', '123')).toBe(false);
    expect(storageService.isPopupPermanentlyDismissed('popup1', '')).toBe(false);
  }));

  it('should maintain separate dismiss states per branch', fakeAsync(() => {
    // Mark popup as dismissed for branch 123
    storageService.markPopupAsPermanentlyDismissed('popup1', '123');
    
    // Should be dismissed for branch 123
    expect(storageService.isPopupPermanentlyDismissed('popup1', '123')).toBe(true);
    
    // Should NOT be dismissed for branch 456
    expect(storageService.isPopupPermanentlyDismissed('popup1', '456')).toBe(false);
  }));
});
