import { registerLocaleData } from '@angular/common';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import locale from '@angular/common/locales/es-MX';
import {
  APP_INITIALIZER,
  ApplicationConfig,
  ErrorHandler,
  importProvidersFrom,
  inject,
  isDevMode,
  LOCALE_ID,
} from '@angular/core';
import { provideNoopAnimations } from '@angular/platform-browser/animations';
import { provideRouter } from '@angular/router';
import { provideServiceWorker } from '@angular/service-worker';
import { ProvideKount } from '@aplazo/front-analytics/kount';
import {
  provideMerchantsGTM,
  TagManagerService,
} from '@aplazo/front-analytics/tag-manager';
import {
  ObservabilityService,
  provideObservability,
} from '@aplazo/front-observability';
import { provideI18N } from '@aplazo/i18n';
import {
  NotifierService,
  provideBrowserDownloader,
  provideBrowserUtils,
  provideConnectionStatus,
  provideCustomError<PERSON>andler,
  provideJwtDecoder,
  provideKustomerWebchat,
  provideLoader,
  provideNotifier,
  provideRedirecter,
  provideTemporal,
  provideUseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import { provideScriptRenderer } from '@aplazo/shared-ui/script-renderer';
import { LogsInitConfiguration } from '@datadog/browser-logs';
import { RumInitConfiguration } from '@datadog/browser-rum';
import { DialogService } from '@ngneat/dialog';
import { STATSIG_INIT_CONFIG, StatsigService } from '@statsig/angular-bindings';
import { StatsigInitConfig } from '@statsig/angular-bindings/statsig.module';
import { StatsigSessionReplayPlugin } from '@statsig/session-replay';
import { StatsigAutoCapturePlugin } from '@statsig/web-analytics';
import { provideNgxMask } from 'ngx-mask';
import { ToastNoAnimationModule } from 'ngx-toastr';
import {
  defer,
  lastValueFrom,
  map,
  of,
  switchMap,
  take,
  tap,
  withLatestFrom,
} from 'rxjs';
import packageJson from '../../package.json';
import {
  posEnvironmentCore,
  providePosEnvironmentCore,
} from '../app-core/domain/environments';
import { routes } from './app.routes';
import { RefreshLoginUsecase } from './core/application/refresh-login.usecase';
import { StoreService } from './core/application/services/store.service';
import { provideAnalyticsService } from './core/config/providers';
import {
  POS_APP_ROUTES,
  ROUTE_CONFIG,
} from './core/domain/config/app-routes.core';
import { EventService } from './core/domain/repositories/events-service';
import { NotificationsRepository } from './core/domain/repositories/notifications.repository';
import { PaymentsRepository } from './core/domain/repositories/payments.repository';
import { PosOfflineRepository } from './core/domain/repositories/pos-offline.repository';
import { authInterceptor } from './core/interceptors/auth.interceptor';
import { branchInterceptor } from './core/interceptors/branch.interceptor';
import { jwtInterceptor } from './core/interceptors/jwt.interceptor';
import { EventDataFoundationService } from './core/services/events-data-foundation.service';
import { NOtificationsService } from './core/services/notifications.service';
import { PaymentsService } from './core/services/payments.service';
import { PlatformService } from './core/services/platform';
import { PosOfflineService } from './core/services/pos-offline.service';
import { provideServiceWorkerUpdater } from './core/services/service-worker-updater.service';
import { GeneralStoreService } from './core/services/store.service';
import { TopErrorHandlerService } from './core/services/top-error-handler.service';
import { provideDynamicBanners } from './modules/dynamic-banners/infra/config/providers';
import { POPUP_PROVIDERS } from './modules/shared/popup/infra/config/providers';
import { ShowPaymentLinkDialogService } from './modules/share-payment-link/application/services/show-payment-link-dialog.service';
import { ShowOnlyLoanIdDialogUsecase } from './modules/share-payment-link/application/show-only-loan-id-dialog.usecase';
import { ShowOnlyQrDialogUsecase } from './modules/share-payment-link/application/show-only-qr-dialog.usecase';
import { provideBarcode } from './modules/share-payment-link/barcode/infra/config/providers';
import { provideQrImageService } from './modules/share-payment-link/infra/config/providers';
import { LoanIdWithNgneatDialogService } from './modules/share-payment-link/loan-id-with-ngneat-dialog.service';
import { provideNoInternetPurchase } from './modules/share-payment-link/no-internet-purchase/infra/config/providers';
import { PaymentLinkWithNgneatDialogService } from './modules/share-payment-link/payment-link-with-ngneat-dialog.service';
import { QrLinkWithNgneatDialogService } from './modules/share-payment-link/qr-link-with-ngneat-dialog.service';

registerLocaleData(locale);

export function initRootServices(): () => Promise<void> {
  const refreshUsecase = inject(RefreshLoginUsecase);
  const platformService = inject(PlatformService);
  const observabilityService = inject(ObservabilityService);
  const notifierService = inject(NotifierService);
  const tagService = inject(TagManagerService);
  const storeService = inject(StoreService);
  const featureFlagsService = inject(StatsigService);

  return async () => {
    if (platformService.TRIDENT) {
      notifierService.warning({
        title: 'Navegador no soportado',
        message:
          'Para disfrutar de una experiencia de navegación completa utilice un navegador de última generación como Chrome, Edge, Firefox, Opera, Brave, etc.',
      });
      console.warn(
        'The current browser is not supported, please use some other alternative like Chrome, Firefox, Brave, Safari, etc.'
      );

      observabilityService.sendCustomLog({
        message: 'UnsupportedBrowser::isMSIE' + platformService.TRIDENT,
        status: 'warn',
      });
    }

    await lastValueFrom(
      refreshUsecase.execute().pipe(
        withLatestFrom(storeService.selectedBranch$),
        switchMap(([user, branch]) => {
          if (user.isLoggedIn) {
            return defer(() =>
              featureFlagsService.updateUserAsync({
                ...featureFlagsService.getClient()?.getContext().user,
                custom: {
                  ...featureFlagsService.getClient()?.getContext().user.custom,
                  merchantId: String(user.merchantId),
                  branchId: String(branch?.id),
                },
                userID: String(user.merchantId),
                customIDs: {
                  branchID: String(branch?.id),
                  storefrontID: String(branch?.id),
                },
              })
            ).pipe(
              map(() => user),
              tap(() => {
                featureFlagsService.logEvent(
                  'posui_front_login_refreshed',
                  Date.now(),
                  {
                    merchantId: String(user.merchantId),
                    branchId: String(branch?.id),
                  }
                );
              })
            );
          }
          return of(user);
        }),
        tap(({ merchantId, isLoggedIn }) => {
          if (isLoggedIn) {
            tagService.trackEvent({
              event: 'loginRefreshed',
              merchantId: merchantId,
              merchantName: '',
              description: {
                genericInfo: 'loginRefreshed',
                startDate: '',
                endDate: '',
                status: '',
                searchTerm: '',
                pageNum: 0,
                pageSize: 0,
                loanId: 0,
                graphType: '',
                buttonName: '',
              },
            });
          }
        }),
        take(1)
      )
    );
  };
}

const datadogConfig: RumInitConfiguration = {
  applicationId: posEnvironmentCore.datadogApplicationId,
  clientToken: posEnvironmentCore.datadogClientToken,
  env: posEnvironmentCore.datadogEnv,
  service: posEnvironmentCore.datadogService,
  sessionSampleRate: posEnvironmentCore.production ? 8 : 0,
  version: packageJson.version,
  allowedTracingUrls: [
    /https:\/\/(api|pos|merchant-acs|merchantdash|mpromotions)\.aplazo\.(mx)/,
  ],
  traceSampleRate: 8,
};

const datadogLoggerConfig: LogsInitConfiguration = {
  clientToken: posEnvironmentCore.datadogClientToken,
  sessionSampleRate: posEnvironmentCore.datadogEnv === 'production' ? 8 : 0,
  version: packageJson.version,
};

const featureFlagsSettings: StatsigInitConfig<any> = {
  sdkKey: posEnvironmentCore.featureFlagsApiKey,
  user: {
    appVersion: packageJson.version,
    userAgent: navigator.userAgent,
    locale: 'es-MX',
    custom: {
      appName: 'posui',
    },
  },
  options: {
    environment: {
      tier: posEnvironmentCore.featureFlagsEnv,
    },
    plugins: [new StatsigAutoCapturePlugin(), new StatsigSessionReplayPlugin()],
  },
};

export const appConfig: ApplicationConfig = {
  providers: [
    provideScriptRenderer(),
    provideNoopAnimations(),
    provideHttpClient(
      withInterceptors([jwtInterceptor, branchInterceptor, authInterceptor])
    ),
    provideRouter(routes),
    provideI18N({
      remoteUrl: posEnvironmentCore.i18nUrl,
      fallbackLocalUrl: '/assets/i18n',
    }),
    importProvidersFrom(
      ToastNoAnimationModule.forRoot({
        closeButton: true,
        timeOut: 8000,
      })
    ),
    provideObservability({
      rumConfig: datadogConfig,
      loggerConfig: datadogLoggerConfig,
    }),
    provideNgxMask(),
    ProvideKount({
      clientId: posEnvironmentCore.kountClientId,
      environment: posEnvironmentCore.kountEnvironment,
    }),
    {
      provide: STATSIG_INIT_CONFIG,
      useValue: featureFlagsSettings,
    },
    {
      provide: LOCALE_ID,
      useValue: 'es-MX',
    },
    providePosEnvironmentCore(),
    {
      provide: POS_APP_ROUTES,
      useValue: ROUTE_CONFIG,
    },
    {
      provide: APP_INITIALIZER,
      useFactory: initRootServices,
      multi: true,
    },
    {
      provide: ErrorHandler,
      useClass: TopErrorHandlerService,
    },
    provideLoader(),
    provideNotifier(),
    provideRedirecter(),
    provideJwtDecoder(),
    provideBrowserDownloader(),
    provideMerchantsGTM({
      apiKey: posEnvironmentCore.gtmId,
    }),
    provideKustomerWebchat({
      apiKey: posEnvironmentCore.webchatApiKey,
      brandId: posEnvironmentCore.webchatBrandId,
    }),
    provideAnalyticsService(),
    provideQrImageService(),
    provideUseCaseErrorHandler(),
    provideNoInternetPurchase(),
    provideTemporal(),
    provideBarcode(),
    provideDynamicBanners(),
    DialogService,
    ...POPUP_PROVIDERS,
    {
      provide: StoreService,
      useClass: GeneralStoreService,
    },
    {
      provide: NotificationsRepository,
      useClass: NOtificationsService,
    },
    {
      provide: PosOfflineRepository,
      useClass: PosOfflineService,
    },
    {
      provide: ShowPaymentLinkDialogService,
      useClass: PaymentLinkWithNgneatDialogService,
    },
    {
      provide: ShowOnlyQrDialogUsecase,
      useClass: QrLinkWithNgneatDialogService,
    },
    {
      provide: ShowOnlyLoanIdDialogUsecase,
      useClass: LoanIdWithNgneatDialogService,
    },
    {
      provide: EventService,
      useClass: EventDataFoundationService,
    },
    {
      provide: PaymentsRepository,
      useClass: PaymentsService,
    },
    provideServiceWorker('ngsw-worker.js', {
      enabled: !isDevMode(),
      registrationStrategy: 'registerWhenStable:30000',
    }),
    provideConnectionStatus({
      notifierText: {
        title: 'Sin conexión a internet',
        message:
          'Algunas o todas las funcionalidades pueden verse afectadas. Verifique la conexión y vuelva a intentar',
      },
      position: 'toast-bottom-full-width',
    }),
    provideBrowserUtils(),
    provideCustomErrorHandler('chunkFile', 'offlineToastr'),
    provideServiceWorkerUpdater({
      pollInterval: 5 * 1000,
      dialogTitle: 'Una nueva versión está disponible',
      dialogMessage: '¿Quieres actualizar ahora?',
    }),
  ],
};
