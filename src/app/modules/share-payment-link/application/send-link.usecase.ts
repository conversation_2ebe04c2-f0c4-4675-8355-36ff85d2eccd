import { Injectable } from '@angular/core';
import {
  BaseUsecase,
  LoaderService,
  NotifierService,
} from '@aplazo/merchant/shared';
import { DialogRef } from '@ngneat/dialog';
import {
  EMPTY,
  MonoTypeOperatorFunction,
  Observable,
  catchError,
  finalize,
  pipe,
  take,
  tap,
} from 'rxjs';
import {
  NOTIFICATION_CHANNELS,
  NotificationChannel,
} from '../../../core/domain/dtos';
import {
  CommonPosOfflineResponse,
  PosOfflineRepository,
  SendLinkRequestDTO,
} from '../../../core/domain/repositories/pos-offline.repository';

@Injectable({ providedIn: 'root' })
export class SendLinkUsecase
  implements
    BaseUsecase<
      {
        loanId: number;
        phone: number;
        target: NotificationChannel;
        dialogRef?: DialogRef<any, any>;
      },
      void
    >
{
  readonly #executables: Map<
    NotificationChannel,
    (args: SendLinkRequestDTO) => Observable<CommonPosOfflineResponse<any>>
  > = new Map();

  constructor(
    private posOfflineService: PosOfflineRepository,
    private loader: LoaderService,
    private notifier: NotifierService
  ) {
    this.#executables.set(NOTIFICATION_CHANNELS.SMS, args =>
      this.posOfflineService.sendPaymentLink({
        loanId: args.loanId,
        phoneNumber: args.phoneNumber,
      })
    );
    this.#executables.set(NOTIFICATION_CHANNELS.WHATSAPP, args =>
      this.posOfflineService.sendWSPaymentLink({
        loanId: args.loanId,
        phoneNumber: args.phoneNumber,
      })
    );
  }

  execute(args: {
    loanId: number;
    phone: number;
    target: NotificationChannel;
    dialogRef?: DialogRef<any, any>;
  }): void {
    const loaderId = this.loader.show();
    const phoneWithInternationalCode = `52${args.phone}`;

    if (phoneWithInternationalCode.length !== 12) {
      this.loader.hide(loaderId);
      this.notifier.warning({
        title: 'Número de teléfono inválido',
        message: 'El número de teléfono debe tener 10 dígitos',
      });
      return;
    }

    const executable = this.#executables.get(args.target);

    if (!executable) {
      this.loader.hide(loaderId);
      this.notifier.warning({
        title: 'No se ha seleccionado un tipo de mensaje',
      });
      return;
    }

    executable({
      phoneNumber: phoneWithInternationalCode,
      loanId: args.loanId,
    })
      .pipe(this.#showSuccessOrComplete(loaderId, args.dialogRef))
      .subscribe();
  }

  #showSuccessOrComplete(
    loaderId: string,
    dialogRef?: DialogRef
  ): MonoTypeOperatorFunction<any> {
    return pipe(
      take(1),

      tap(() => {
        this.notifier.success({
          title: 'Mensaje enviado',
        });
      }),

      catchError(error => {
        console.error(error);

        this.notifier.warning({
          title: '¡Ups! Algo salió mal',
          message: 'No se pudo enviar el mensaje',
        });

        return EMPTY;
      }),

      finalize(() => {
        this.loader.hide(loaderId);
        dialogRef?.close();
      })
    );
  }
}
