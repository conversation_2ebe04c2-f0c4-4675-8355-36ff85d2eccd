import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { of } from 'rxjs';
import {
  PopupTriggerService,
  PopupTriggerConfig,
} from 'src/app/modules/shared/popup/application/services/popup-trigger.service';
import { PopupRepository } from 'src/app/modules/shared/popup/infra/repositories/popup.repository';
import { PopupStorageService } from 'src/app/modules/shared/popup/application/services/popup-storage.service';
import {
  ShowPopupUseCase,
  ShowPopupResult,
} from 'src/app/modules/shared/popup/application/usecases/show-popup.usecase';

describe('PopupTriggerService', () => {
  let service: PopupTriggerService;
  let popupRepository: jasmine.SpyObj<PopupRepository>;
  let popupStorage: jasmine.SpyObj<PopupStorageService>;
  let showPopupUseCase: jasmine.SpyObj<ShowPopupUseCase>;

  const mockPopups = [
    {
      popupId: 'POPUP-001',
      startAt: '2025-06-23:00:00:00UTC',
      endAt: '2025-07-23:00:00:00UTC',
      priority: 1,
    },
    {
      popupId: 'POPUP-002',
      startAt: '2025-06-23:00:00:00UTC',
      endAt: '2025-07-23:00:00:00UTC',
      priority: 2,
    },
  ];

  beforeEach(() => {
    const popupRepositorySpy = jasmine.createSpyObj('PopupRepository', [
      'getAvailablePopups',
    ]);
    const popupStorageSpy = jasmine.createSpyObj('PopupStorageService', [
      'wasPopupShownToday',
      'markPopupAsShown',
      'cleanupOldRecords',
      'isPopupPermanentlyDismissed',
    ]);
    const showPopupUseCaseSpy = jasmine.createSpyObj('ShowPopupUseCase', [
      'execute',
    ]);

    TestBed.configureTestingModule({
      providers: [
        PopupTriggerService,
        { provide: PopupRepository, useValue: popupRepositorySpy },
        { provide: PopupStorageService, useValue: popupStorageSpy },
        { provide: ShowPopupUseCase, useValue: showPopupUseCaseSpy },
      ],
    });

    service = TestBed.inject(PopupTriggerService);
    popupRepository = TestBed.inject(
      PopupRepository
    ) as jasmine.SpyObj<PopupRepository>;
    popupStorage = TestBed.inject(
      PopupStorageService
    ) as jasmine.SpyObj<PopupStorageService>;
    showPopupUseCase = TestBed.inject(
      ShowPopupUseCase
    ) as jasmine.SpyObj<ShowPopupUseCase>;
  });

  it('should return false when no popups are available', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of([]));

    let result = false;
    service.shouldShowPopup({ branchId: 'BRANCH-001' }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(false);
    expect(popupRepository.getAvailablePopups).toHaveBeenCalledWith(
      'BRANCH-001'
    );
  }));

  it('should return true when popup is available and not shown today', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(false);
    popupStorage.isPopupPermanentlyDismissed.and.returnValue(false);

    let result = false;
    service.shouldShowPopup({ branchId: 'BRANCH-001' }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(true);
    expect(popupStorage.wasPopupShownToday).toHaveBeenCalledWith(
      'POPUP-002',
      'BRANCH-001',
      jasmine.any(String)
    );
    expect(popupStorage.isPopupPermanentlyDismissed).toHaveBeenCalledWith(
      'POPUP-002',
      'BRANCH-001'
    );
  }));

  it('should return false when all popups have been shown today', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(true);
    popupStorage.isPopupPermanentlyDismissed.and.returnValue(false);

    let result = false;
    service.shouldShowPopup({ branchId: 'BRANCH-001' }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(false);
  }));

  it('should prioritize popups by priority (higher number = higher priority)', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.callFake((popupId: string) => {
      return popupId === 'POPUP-001';
    });
    popupStorage.isPopupPermanentlyDismissed.and.returnValue(false);

    let result = false;
    service.shouldShowPopup({ branchId: 'BRANCH-001' }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(true);
    expect(popupStorage.wasPopupShownToday).toHaveBeenCalledWith(
      'POPUP-002',
      'BRANCH-001',
      jasmine.any(String)
    );
    expect(popupStorage.isPopupPermanentlyDismissed).toHaveBeenCalledWith(
      'POPUP-002',
      'BRANCH-001'
    );
  }));

  it('should return false when popup is permanently dismissed', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(false);
    popupStorage.isPopupPermanentlyDismissed.and.returnValue(true);

    let result = false;
    service.shouldShowPopup({ branchId: 'BRANCH-001' }).subscribe(value => {
      result = value;
    });
    tick();

    expect(result).toBe(false);
    expect(popupStorage.isPopupPermanentlyDismissed).toHaveBeenCalledWith(
      'POPUP-002',
      'BRANCH-001'
    );
  }));

  it('should not trigger popup when shouldShowPopup returns false', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of([]));

    let result = false;
    service
      .triggerPopupIfNeeded({ branchId: 'BRANCH-001' })
      .subscribe(value => {
        result = value;
      });
    tick();

    expect(result).toBe(false);
    expect(showPopupUseCase.execute).not.toHaveBeenCalled();
  }));

  it('should trigger popup when shouldShowPopup returns true', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(false);
    popupStorage.isPopupPermanentlyDismissed.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(
      of({ success: true, popupShown: mockPopups[0] })
    );

    let result = false;
    service
      .triggerPopupIfNeeded({ branchId: 'BRANCH-001' })
      .subscribe(value => {
        result = value;
      });
    tick();

    expect(result).toBe(true);
    expect(showPopupUseCase.execute).toHaveBeenCalled();
  }));

  it('should not mark as shown when popup execution fails', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(false);
    popupStorage.isPopupPermanentlyDismissed.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(
      of({ success: false, reason: 'Error' })
    );

    let result = false;
    service
      .triggerPopupIfNeeded({ branchId: 'BRANCH-001' })
      .subscribe(value => {
        result = value;
      });
    tick();

    expect(result).toBe(false);
  }));

  it('should call triggerPopupIfNeeded with login configuration', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of([]));

    service.triggerAfterLogin('BRANCH-001').subscribe();
    tick();

    expect(popupRepository.getAvailablePopups).toHaveBeenCalledWith(
      'BRANCH-001'
    );
  }));

  it('should call triggerPopupIfNeeded with payment configuration', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of([]));

    service.triggerAfterPaymentSuccess('BRANCH-001').subscribe();
    tick();

    expect(popupRepository.getAvailablePopups).toHaveBeenCalledWith(
      'BRANCH-001'
    );
  }));

  it('should call popupStorage.cleanupOldRecords', () => {
    service.cleanupOldRecords();

    expect(popupStorage.cleanupOldRecords).toHaveBeenCalled();
  });
});
