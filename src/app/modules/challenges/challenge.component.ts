import { Component, Input } from '@angular/core';
import {
  <PERSON><PERSON><PERSON>cyPipe,
  DatePipe,
  NgIf,
  NgSwitch,
  NgSwitchCase,
} from '@angular/common';
import { CountdownDirective } from '../shared/directives/countdown.directive';
import { Challenge } from '../../core/domain/challenge';

@Component({
  standalone: true,
  imports: [
    CountdownDirective,
    DatePipe,
    CurrencyPipe,
    NgSwitchCase,
    NgSwitch,
    NgIf,
  ],
  selector: 'aplazo-challenge-component',
  templateUrl: './challenge.component.html',
})
export class ChallengeComponent {
  @Input() challenge: Challenge;

  isExpired(): boolean {
    return new Date(this.challenge.end).getTime() < new Date().getTime();
  }
}
