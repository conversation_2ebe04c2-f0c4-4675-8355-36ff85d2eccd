[{"id": 642, "createdAt": "2023-06-30T17:35:22.611963Z", "updatedAt": "2023-06-30T17:52:52.614134Z", "DeletedAt": null, "name": "002 Walmart 2", "banned": false, "merchantConfigId": 167, "shareLinkFlags": {"id": 576, "createdAt": "2023-06-30T17:35:22.622002Z", "updatedAt": "2023-06-30T17:52:52.614134Z", "DeletedAt": null, "isQrAllowed": true, "isWhatsappAllowed": true, "isSMSAllowed": true, "branchId": 642}, "branchFeatureFlags": {"id": 420, "createdAt": "2023-06-30T17:35:22.638662Z", "updatedAt": "2023-06-30T17:52:52.614134Z", "DeletedAt": null, "branchId": 642, "isSellAgentTrackEnable": false, "isSupportChatEnable": false, "isOrderRefundEnable": false, "isTodayReportEnable": false}}, {"id": 42, "createdAt": "2021-04-12T16:58:03.245323Z", "updatedAt": "2021-04-26T18:13:42.263067Z", "DeletedAt": null, "name": "Tienda Premiun", "banned": false, "merchantConfigId": 2, "shareLinkFlags": {"id": 5, "createdAt": "2021-08-24T13:00:06.792253Z", "updatedAt": "2021-08-24T13:00:06.792253Z", "DeletedAt": null, "isQrAllowed": true, "isWhatsappAllowed": true, "isSMSAllowed": true, "branchId": 42}, "branchFeatureFlags": {"id": 9, "createdAt": "2022-07-04T22:09:25.910065Z", "updatedAt": "0001-01-01T00:00:00Z", "DeletedAt": null, "branchId": 42, "isSellAgentTrackEnable": false, "isSupportChatEnable": false, "isOrderRefundEnable": false, "isTodayReportEnable": false}}, {"id": 43, "createdAt": "2021-04-26T18:13:42.267676Z", "updatedAt": "2021-04-26T18:13:42.267676Z", "DeletedAt": null, "name": "Sucursal Merida", "banned": false, "merchantConfigId": 2, "shareLinkFlags": {"id": 6, "createdAt": "2021-08-24T13:00:06.792253Z", "updatedAt": "2021-08-24T13:00:06.792253Z", "DeletedAt": null, "isQrAllowed": true, "isWhatsappAllowed": true, "isSMSAllowed": true, "branchId": 43}}, {"id": 47, "createdAt": "2021-07-05T18:16:56.160461Z", "updatedAt": "2021-07-05T18:16:56.160461Z", "DeletedAt": null, "name": "Tienda de Prueba", "banned": false, "merchantConfigId": 2, "shareLinkFlags": {"id": 7, "createdAt": "2021-08-24T13:00:06.792253Z", "updatedAt": "2021-08-24T13:00:06.792253Z", "DeletedAt": null, "isQrAllowed": true, "isWhatsappAllowed": true, "isSMSAllowed": true, "branchId": 47}}, {"id": 51, "createdAt": "2021-10-26T21:48:49.328693Z", "updatedAt": "2021-10-26T21:48:49.328693Z", "DeletedAt": null, "name": "Tienda De Demo", "banned": false, "merchantConfigId": 2, "shareLinkFlags": {"id": 11, "createdAt": "2021-10-26T21:48:49.347524Z", "updatedAt": "2021-10-26T21:48:49.347524Z", "DeletedAt": null, "isQrAllowed": true, "isWhatsappAllowed": true, "isSMSAllowed": true, "branchId": 51}}, {"id": 52, "createdAt": "2021-10-29T17:15:46.285226Z", "updatedAt": "2021-10-29T17:15:46.285226Z", "DeletedAt": null, "name": "tiEndA de PrueBa", "banned": false, "merchantConfigId": 2, "shareLinkFlags": {"id": 12, "createdAt": "2021-10-29T17:15:46.309445Z", "updatedAt": "2021-10-29T17:15:46.309445Z", "DeletedAt": null, "isQrAllowed": true, "isWhatsappAllowed": true, "isSMSAllowed": true, "branchId": 52}}]