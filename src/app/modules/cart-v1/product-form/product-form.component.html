<aplz-ui-card *ngIf="data?.injectedText">
  <h4 class="font-medium text-xl text-center my-6">
    {{ data.injectedText.title }}
  </h4>

  <form
    class="flex flex-col gap-y-4"
    [formGroup]="formGroup"
    (ngSubmit)="addProductToCart()">
    <aplz-ui-form-field>
      <aplz-ui-form-label>
        {{ data.injectedText.sku.label }}
      </aplz-ui-form-label>
      <input
        #skuInput
        aplzFormInput
        type="text"
        formControlName="sku"
        [placeholder]="data.injectedText.sku.placeholder || 'SKU'" />

      <p aplzFormError *ngIf="skuControl.hasError('required')">
        {{ data.injectedText.sku.errorRequired }}
      </p>
    </aplz-ui-form-field>

    <aplz-ui-form-field>
      <aplz-ui-form-label>
        {{ data.injectedText.product.label }}
      </aplz-ui-form-label>
      <input        
        type="text"
        aplzFormInput
        formControlName="name"
        [placeholder]="data.injectedText.product.placeholder || 'Nombre'" />

      <p aplzFormError *ngIf="productControl.hasError('required')">
        {{ data.injectedText.product.errorRequired }}
      </p>
      <p aplzFormError *ngIf="productControl.hasError('maxlength')">
        {{ data.injectedText.product.errorMaxLength }}
      </p>


    </aplz-ui-form-field>

    <div class="flex flex-wrap gap-4">
      <aplz-ui-form-field class="flex-grow flex-shrink md:flex-1">
        <aplz-ui-form-label>
          {{ data.injectedText.price.label }}
        </aplz-ui-form-label>
        <input
          type="text"
          aplzFormInput
          formControlName="price"
          [placeholder]="data.injectedText.price.placeholder || 'Precio'"
          mask="separator.2"
          thousandSeparator=","
          prefix="$"
          inputmode="decimal" />

        <p aplzFormError *ngIf="priceControl.hasError('required')">
          {{ data.injectedText.price.errorRequired || 'Campo requerido' }}
        </p>
        <p
          aplzFormError
          *ngIf="
            !priceControl.hasError('required') && priceControl.hasError('min')
          ">
          {{ data.injectedText.price.errorMin }}
        </p>
      </aplz-ui-form-field>

      <aplz-ui-form-field class="flex-grow flex-shrink md:flex-1">
        <aplz-ui-form-label>
          {{ data.injectedText.quantity.label }}
        </aplz-ui-form-label>
        <input
          type="text"
          aplzFormInput
          formControlName="quantity"
          [placeholder]="data.injectedText.quantity.placeholder"
          mask="separator.0"
          thousandSeparator=","
          inputmode="decimal" />

        <p aplzFormError *ngIf="quantityControl.hasError('required')">
          {{ data.injectedText.quantity.errorRequired }}
        </p>
        <p
          *ngIf="
            !quantityControl.hasError('required') &&
            quantityControl.hasError('min')
          ">
          {{ data.injectedText.quantity.errorMin }}
        </p>
      </aplz-ui-form-field>
    </div>

    <button
      aplzButton
      aplzAppearance="solid"
      aplzColor="dark"
      size="md"
      type="submit"
      [fullWidth]="true"
      [disabled]="!isValidForm">
      {{ data.injectedText.actions.confirm }}
    </button>
  </form>
</aplz-ui-card>
