import { Async<PERSON>ipe, NgIf } from '@angular/common';
import {
  discardPeriodicTasks,
  fakeAsync,
  flush,
  TestBed,
} from '@angular/core/testing';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { RedirectionService } from '@aplazo/merchant/shared';
import {
  provideNotifierTesting,
  provideRedirecterTesting,
} from '@aplazo/merchant/shared-testing';
import { AplazoMatchMediaService } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoDetailsComponents } from '@aplazo/shared-ui/details';
import { AplazoIconComponent } from '@aplazo/shared-ui/icon';
import { AplazoSidenavLinkComponent } from '@aplazo/shared-ui/sidenav';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';
import { DialogService } from '@ngneat/dialog';
import { StatsigModule, StatsigService } from '@statsig/angular-bindings';
import { lastValueFrom, of, take } from 'rxjs';
import { INotificationsWithNewOnesUIDto } from 'src/app/core/domain/notification';
import { NotificationsHandlerService } from 'src/app/core/services/notifications-handler.service';
import { provideI18NTesting } from 'src/tests/i18n.local';
import {
  POS_ENVIRONMENT_CORE,
  posEnvironmentCore,
} from '../../../app-core/domain/environments';
import { AnalyticsService } from '../../../app/core/application/services/analytics.service';
import { StoreService } from '../../../app/core/application/services/store.service';
import {
  POS_APP_ROUTES,
  ROUTE_CONFIG,
} from '../../../app/core/domain/config/app-routes.core';
import { MerchantsService } from '../../../app/core/services/merchants.service';
import { NotificationsStore } from '../../../app/core/services/notifications-store.service';
import { SecuredV1LogoutUsecase } from '../../../app/modules/secured-v1/secured-v1-logout.usecase';
import { SecuredV1Component } from '../../../app/modules/secured-v1/secured-v1.component';

const notis = [
  {
    id: 75,
    commType: 'INFO',
    title: 'Prueba notificacion ',
    description: 'This is a super notification test',
    status: 'A',
    platform: 'P',
    merchants: '199',
    lastUpdate: '2024-12-12T19:59:01.005722Z',
    applyAll: true,
    complement: {
      urlVideo: 'https://www.youtube.com/watch?v=1ulI-t9qT7U',
      urlThumbnail: 'string',
      tags: ['CUSTOMER'],
    },
  },
  {
    id: 76,
    commType: 'INFO',
    title: 'Prueba notificacion #2',
    description: 'This is a super notification test',
    status: 'A',
    platform: 'P',
    merchants: '199',
    lastUpdate: '2024-12-12T20:00:31.979923Z',
    applyAll: true,
    complement: {
      urlVideo: 'https://www.youtube.com/watch?v=1ulI-t9qT7U',
      urlThumbnail: 'string',
      tags: ['CUSTOMER'],
    },
  },
  {
    id: 77,
    commType: 'INFO',
    title: 'Prueba notificacion #3',
    description:
      'This is a super notification test to wait for push notificacion',
    status: 'A',
    platform: 'P',
    merchants: '199',
    lastUpdate: '2024-12-12T20:05:57.667430Z',
    applyAll: true,
    complement: {
      urlVideo: 'https://www.youtube.com/watch?v=1ulI-t9qT7U',
      urlThumbnail: 'string',
      tags: ['CUSTOMER'],
    },
  },
  {
    id: 74,
    commType: 'INFO',
    title: 'Prueba notificacion ',
    description: 'This is a super notification test',
    status: 'A',
    platform: 'P',
    merchants: '2239',
    lastUpdate: '2024-12-12T19:58:50.417649Z',
    applyAll: true,
    complement: {
      urlVideo: 'https://www.youtube.com/watch?v=1ulI-t9qT7U',
      urlThumbnail: 'string',
      tags: ['CUSTOMER'],
    },
  },
  {
    id: 78,
    commType: 'INFO',
    title: 'Prueba notificacion #4',
    description:
      'This is a super notification test to wait for push notificacion',
    status: 'A',
    platform: 'P',
    merchants: '199',
    lastUpdate: '2024-12-12T20:06:39.391647Z',
    applyAll: true,
    complement: {
      urlVideo: 'https://www.youtube.com/watch?v=1ulI-t9qT7U',
      urlThumbnail: 'string',
      tags: ['CUSTOMER'],
    },
  },
  {
    id: 79,
    commType: 'INFO',
    title: 'Prueba notificacion #4.5',
    description:
      'This is a super notification test to wait for push notificacion again',
    status: 'A',
    platform: 'P',
    merchants: '199',
    lastUpdate: '2024-12-12T20:20:08.186969Z',
    applyAll: true,
    complement: {
      urlVideo: 'https://www.youtube.com/watch?v=1ulI-t9qT7U',
      urlThumbnail: 'string',
      tags: ['CUSTOMER'],
    },
  },
  {
    id: 80,
    commType: 'INFO',
    title: 'Prueba notificacion #5',
    description: 'This is a super duper notification test ',
    status: 'A',
    platform: 'P',
    merchants: '199',
    lastUpdate: '2024-12-12T20:37:47.994863Z',
    applyAll: true,
    complement: {
      urlVideo: 'https://www.youtube.com/watch?v=1ulI-t9qT7U',
      urlThumbnail: 'string',
      tags: ['CUSTOMER'],
    },
  },
];

const routeMock = {
  firstChild: {
    data: of({ title: 'title' }),
  },
  snapshot: {
    firstChild: {
      data: { title: 'title' },
    },
  },
};

const defaultConfig = {
  store: {
    merchantId: 123,
    branch: null,
    role: 'ROLE_MERCHANT',
    url: 'https://aplazo.mx/test.webp',
  },
  notifications: {
    data: notis,
    counter: 3,
  },
  flags: false,
  dialog: undefined,
};

const setup = (args?: {
  store?: {
    merchantId: number;
    branch: string;
    role: string;
    url: string;
  };
  notifications?: {
    data: INotificationsWithNewOnesUIDto[];
    counter?: number;
  };
  flags?: boolean | null;
  dialog?: {
    confirmation: boolean;
  };
}) => {
  const config = {
    store: { ...defaultConfig.store, ...(args?.store ?? {}) },
    notifications: {
      ...defaultConfig.notifications,
      ...(args?.notifications ?? {}),
    },
    flags: args?.flags != null ? Boolean(args.flags) : defaultConfig.flags,
    dialog: args?.dialog ?? defaultConfig.dialog,
  };

  TestBed.configureTestingModule({
    imports: [
      RouterModule,
      AsyncPipe,
      NgIf,
      AplazoIconComponent,
      AplazoButtonComponent,
      AplazoTooltipDirective,
      AplazoSidenavLinkComponent,
      AplazoDetailsComponents,
      RouterTestingModule,
      StatsigModule,
    ],
    providers: [
      provideRedirecterTesting(),
      provideNotifierTesting(),
      AplazoMatchMediaService,
      {
        provide: ActivatedRoute,
        useValue: routeMock,
      },
      {
        provide: POS_ENVIRONMENT_CORE,
        useValue: posEnvironmentCore,
      },
      {
        provide: POS_APP_ROUTES,
        useValue: ROUTE_CONFIG,
      },
      {
        provide: DialogService,
        useValue: {
          open: () => ({ afterClosed$: of(config.dialog) }),
        },
      },
      {
        provide: StoreService,
        useValue: {
          getMerchantId$: () => of(config.store.merchantId),
          selectedBranchName$: of(config.store.branch),
          userRole$: of(config.store.role),
          merchantLogoUrl$: of(config.store.url),
          merchantName$: of('test name'),
        },
      },
      {
        provide: SecuredV1LogoutUsecase,
        useValue: jasmine.createSpyObj('SecuredV1LogoutUsecase', ['execute']),
      },
      {
        provide: MerchantsService,
        useValue: {
          getMerchantDetails: () => of({ name: 'test name' }),
        },
      },
      {
        provide: NotificationsStore,
        useValue: {
          notifications$: of(config.notifications.data),
          hasNewNotifications$: () => of(config.notifications.data.length > 0),
          newNotificationsCounter$: () => of(config.notifications.counter),
          setNotifications: () => {
            void 0;
          },
        },
      },
      {
        provide: NotificationsHandlerService,
        useValue: {
          getNotifications$: () => of(config.notifications.data),
        },
      },
      {
        provide: AnalyticsService,
        useValue: jasmine.createSpyObj('AnalyticsService', ['track']),
      },
      {
        provide: StatsigService,
        useValue: (() => {
          const mockStatsigClient = jasmine.createSpyObj('StatsigClient', [
            '$on',
          ]);
          mockStatsigClient.$on.and.returnValue(undefined);

          const mockStatsigService = jasmine.createSpyObj('StatsigService', [
            'checkGate',
            'getExperiment',
            'getFeatureConfig',
            'getClient',
            '$on',
            'initialize',
          ]);

          mockStatsigService.getClient.and.returnValue(mockStatsigClient);
          mockStatsigService.$on.and.returnValue(of(undefined));

          mockStatsigService.checkGate.and.returnValue(config.flags);

          return mockStatsigService;
        })(),
      },
      provideI18NTesting('principal-layout'),
    ],
  });

  const fixture = TestBed.createComponent(SecuredV1Component);
  const component = fixture.componentInstance;
  const redirecter = TestBed.inject(RedirectionService);
  const analytics = TestBed.inject(
    AnalyticsService
  ) as jasmine.SpyObj<AnalyticsService>;
  const logoutUsecase = TestBed.inject(
    SecuredV1LogoutUsecase
  ) as jasmine.SpyObj<SecuredV1LogoutUsecase>;
  const statsigService = TestBed.inject(
    StatsigService
  ) as jasmine.SpyObj<StatsigService>;

  fixture.detectChanges();

  const openPdfSpy = spyOn(redirecter, 'openPdf').and.callThrough();
  const openInternalLinkSpy = spyOn(
    redirecter,
    'internalNavigation'
  ).and.callThrough();
  const openExternalLinkSpy = spyOn(
    redirecter,
    'externalNavigation'
  ).and.callThrough();
  const openDialogSpy = spyOn(TestBed.inject(DialogService), 'open');

  return {
    fixture,
    component,
    openPdfSpy,
    openInternalLinkSpy,
    openExternalLinkSpy,
    analytics,
    logoutUsecase,
    openDialogSpy,
    statsigService,
  };
};

describe('SecuredV1Component', () => {
  it('should create', () => {
    const { component } = setup();

    expect(component).toBeTruthy();
  });

  it('should get the banner flag disabled by default', async () => {
    const { component } = setup();

    const bannerFlag = await lastValueFrom(component.bannerFlag$.pipe(take(1)));

    expect(bannerFlag.enabled).toBeFalse();
  });

  it('should call the openPdf method when openDocument is executed', () => {
    const { component, openPdfSpy } = setup();

    component.openDocument('test.pdf');

    expect(openPdfSpy).toHaveBeenCalledTimes(1);
    expect(openPdfSpy).toHaveBeenCalledWith('test.pdf');
  });

  it('should call the externalNavigation method when goToMerchantLandingRoute is executed', () => {
    const { component, openExternalLinkSpy } = setup();

    component.goToMerchantLandingRoute();

    expect(openExternalLinkSpy).toHaveBeenCalledTimes(1);
    expect(openExternalLinkSpy).toHaveBeenCalledWith(
      `${posEnvironmentCore.landingpage}para-comercios`,
      '_blank'
    );
  });

  it('should call the externalNavigation method when goToFAQLandingRoute is executed', () => {
    const { component, openExternalLinkSpy } = setup();

    component.goToFAQLandingRoute();

    expect(openExternalLinkSpy).toHaveBeenCalledTimes(1);
    expect(openExternalLinkSpy).toHaveBeenCalledWith(
      `${posEnvironmentCore.landingpage}preguntas-frecuentes`,
      '_blank'
    );
  });

  it('should call the externalNavigation method when goToMerchantWhatsapp is executed', () => {
    const { component, openExternalLinkSpy } = setup();

    component.goToMerchantWhatsapp();

    expect(openExternalLinkSpy).toHaveBeenCalledTimes(1);
    expect(openExternalLinkSpy).toHaveBeenCalledWith(
      'https://api.whatsapp.com/send/?phone=525570058799',
      '_blank'
    );
  });

  it('should call the internalNavigation method when goToNotificationsRoute is executed', () => {
    const { component, openInternalLinkSpy } = setup();

    component.goToNotificationsRoute();

    expect(openInternalLinkSpy).toHaveBeenCalledTimes(1);
    expect(openInternalLinkSpy).toHaveBeenCalledWith([
      ROUTE_CONFIG.aplazoRoot,
      ROUTE_CONFIG.aplazoLayout,
      ROUTE_CONFIG.aplazoNotifications,
    ]);
  });

  it('should call the internalNavigation method when logoClicked is executed', () => {
    const { component, openInternalLinkSpy } = setup();

    component.logoClicked();

    expect(openInternalLinkSpy).toHaveBeenCalledTimes(1);
    expect(openInternalLinkSpy).toHaveBeenCalledWith('/');
  });

  it('should set error image when onLoadError is executed', fakeAsync(() => {
    const { component } = setup();

    let image: any;

    component.img$.subscribe({
      next: img => {
        image = img;
      },
      error: fail,
    });

    flush();

    expect(image).toBe('https://aplazo.mx/test.webp');

    component.onLoadError();

    flush();

    expect(image).toBe(
      'https://aplazoassets.s3.us-west-2.amazonaws.com/merchant-dash-assets/settings.svg'
    );

    // there is a timer suscribed within ngOnInit that needs to be cleared
    discardPeriodicTasks();
  }));

  it('should call the logout usecase when logout is executed and dialog returns confirmation equals true', () => {
    const { component, openDialogSpy, logoutUsecase } = setup();

    openDialogSpy.and.returnValue({
      afterClosed$: of({ confirmation: true }),
    } as any);

    component.logout();

    expect(openDialogSpy).toHaveBeenCalledTimes(1);
    expect(logoutUsecase.execute).toHaveBeenCalledTimes(1);
  });

  it('should not call the logout usecase when logout is executed and dialog returns confirmation equals false', () => {
    const { component, openDialogSpy, logoutUsecase } = setup();

    openDialogSpy.and.returnValue({
      afterClosed$: of({ confirmation: false }),
    } as any);

    component.logout();

    expect(openDialogSpy).toHaveBeenCalledTimes(1);
    expect(logoutUsecase.execute).toHaveBeenCalledTimes(0);
  });

  it('should not call the logout usecase when logout is executed and dialog returns undefined', () => {
    const { component, openDialogSpy, logoutUsecase } = setup();

    openDialogSpy.and.returnValue({
      afterClosed$: of(undefined),
    } as any);

    component.logout();

    expect(openDialogSpy).toHaveBeenCalledTimes(1);
    expect(logoutUsecase.execute).toHaveBeenCalledTimes(0);
  });

  it('should call track method from AnalyticsService when trackSidebarClick is executed', () => {
    const { component, analytics } = setup();

    const timestamp = new Date().getTime();
    component.trackSidebarClick('test', timestamp);

    expect(analytics.track).toHaveBeenCalledTimes(1);
    expect(analytics.track).toHaveBeenCalledWith('buttonClick', {
      buttonName: 'test',
      timestamp: timestamp as any,
      url: '/',
    });
  });
});
