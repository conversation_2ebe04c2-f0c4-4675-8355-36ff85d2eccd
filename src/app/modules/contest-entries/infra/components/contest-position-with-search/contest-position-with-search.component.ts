import { AsyncPipe } from '@angular/common';
import { Component, inject, OnDestroy } from '@angular/core';
import {
  AbstractControl,
  AsyncValidatorFn,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { LoaderService, RuntimeMerchantError } from '@aplazo/merchant/shared';
import {
  AplazoLettersNumbersDirective,
  AplazoTrimSpacesDirective,
} from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import {
  AplazoIconComponent,
  AplazoIconRegistryService,
} from '@aplazo/shared-ui/icon';
import { iconArrowLeft, iconSearch, iconXMark } from '@aplazo/ui-icons';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  distinctUntilChanged,
  first,
  lastValueFrom,
  map,
  Observable,
  of,
  startWith,
  take,
} from 'rxjs';
import { AnalyticsService } from '../../../../../core/application/services/analytics.service';
import { GetOneParticipantWithDetailsUseCase } from '../../../application/usecases/get-one-with-details.usecase';
import { ContestStoreService } from '../../services/contest-store.service';

@Component({
  standalone: true,
  selector: 'app-contest-position-with-search',
  templateUrl: './contest-position-with-search.component.html',
  imports: [
    AsyncPipe,
    ReactiveFormsModule,
    AplazoButtonComponent,
    AplazoIconComponent,
    AplazoLettersNumbersDirective,
    AplazoTrimSpacesDirective,
  ],
})
export class AplazoContestPositionWithSearchComponent implements OnDestroy {
  readonly #iconRegister = inject(AplazoIconRegistryService);
  readonly #getOneUseCase = inject(GetOneParticipantWithDetailsUseCase);
  readonly #store = inject(ContestStoreService);
  readonly #loader = inject(LoaderService);
  readonly #analytics = inject(AnalyticsService);

  readonly participantErrorKey = 'participantNotFound';

  readonly #asyncError$ = new BehaviorSubject<
    Record<string, ValidationErrors | null>
  >({});

  readonly hasDetails$ = this.#store.hasDetails$;
  readonly isLoading$ = this.#loader.isLoading$;

  readonly vm$ = combineLatest({
    hasDetails: this.hasDetails$,
    isLoading: this.isLoading$,
  });

  minLength = 3;

  readonly search = new FormControl<string>('', {
    nonNullable: true,
    validators: [Validators.required, Validators.minLength(this.minLength)],

    asyncValidators: [this.#validateAfterTrigger()],
  });
  readonly form = new FormGroup({
    search: this.search,
  });

  readonly search$ = this.search.valueChanges.pipe(
    startWith(this.search.value),
    distinctUntilChanged(),
    map(val => String(val == null ? '' : val).trim())
  );

  readonly isActiveSearch$ = this.search.valueChanges.pipe(
    map(val => (val?.length ?? 0) >= this.minLength)
  );

  constructor() {
    this.#iconRegister.registerIcons([iconSearch, iconXMark, iconArrowLeft]);
  }

  async retrieveParticipantInfo(): Promise<void> {
    this.form.markAllAsTouched();

    const hasSearch =
      this.search.value != null && this.search.value.length >= this.minLength;

    if (this.form.valid && hasSearch) {
      try {
        this.#analytics.track('search', {
          category: 'premios_contest',
          timestamp: new Date().getTime(),
          label: 'searchPremiosParticipant',
          searchTerm: this.search.value,
        });
        const result = await lastValueFrom(
          this.#getOneUseCase.execute(this.search.value).pipe(
            catchError(err => {
              const isControlledError = err instanceof RuntimeMerchantError;
              const errors = this.#asyncError$.getValue();

              if (
                isControlledError &&
                err.code === 'GetOneWithDetailsUseCase::participantNotFound'
              ) {
                errors[this.search.value] = {
                  [this.participantErrorKey]: err.message,
                };

                this.#asyncError$.next(errors);
              }

              this.search.updateValueAndValidity();

              return of(null);
            }),
            take(1)
          )
        );

        if (result) {
          this.#store.setNewRanking(result);
        }
      } catch (error) {
        console.warn(error);
      }
    }
  }

  clear(): void {
    this.form.reset();
    this.#store.clearRanking();
  }

  ngOnDestroy(): void {
    this.clear();
  }

  #validateAfterTrigger(): AsyncValidatorFn {
    return (control: AbstractControl): Observable<ValidationErrors | null> => {
      const currentCode = control.value;

      return this.#asyncError$.pipe(
        first(),
        map(stateError => {
          const errors = stateError[currentCode];

          if (errors) {
            return errors;
          }

          return null;
        })
      );
    };
  }
}
