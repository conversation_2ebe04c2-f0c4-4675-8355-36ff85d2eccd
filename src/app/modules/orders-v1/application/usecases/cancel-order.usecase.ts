import { Injectable } from '@angular/core';
import { LoaderService, NotifierService } from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, take, tap } from 'rxjs';
import { Order } from '../../../../core/domain/order.interface';
import { PosOfflineRepository } from '../../../../core/domain/repositories/pos-offline.repository';

@Injectable({ providedIn: 'root' })
export class CancelOrderUseCase {
  constructor(
    private offlineService: PosOfflineRepository,
    private loader: LoaderService,
    private notifier: NotifierService
  ) {}

  execute(order: Order): Observable<void> {
    const idLoader = this.loader.show();

    return this.offlineService.deleteOrder(order.loanId).pipe(
      take(1),
      tap(() => {
        this.notifier.success({
          title: 'Orden cancelada',
        });
      }),

      catchError((error: any) => {
        console.error(error);

        this.notifier.error({
          title: 'Algo salio mal!',
          message: 'No se pudo cancelar la orden',
        });

        throw error;
      }),

      finalize(() => {
        this.loader.hide(idLoader);
      })
    );
  }
}
