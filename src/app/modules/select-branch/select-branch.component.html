<ng-container *ngIf="filteredBranches$ | async as branches">
  <ng-container *ngIf="injectedText$ | async as injectedText">
    <div class="flex flex-col justify-between w-full min-h-screen">
      <header
        class="sticky top-0 left-0 flex flex-col items-center max-w-xl mx-auto pt-8 px-10 pb-4 text-center bg-light">
        <aplz-ui-logo size="md"></aplz-ui-logo>

        <h4 class="mt-8 text-2xl font-medium">
          {{ injectedText.title }}
        </h4>
        <p class="py-4 max-w-sm text-dark-secondary text-md font-light">
          {{ injectedText.legend }}
        </p>

        <aplz-ui-form-field [hideRequiredMarker]="true">
          <input
            aplzFormInput
            type="search"
            class="search-outline"
            [formControl]="branchNameCtrl"
            [placeholder]="injectedText.searchbar.placeholder"
            autocomplete="off" />

          <button
            aplzButton
            aplzInputPrefix
            type="button"
            size="xs"
            aplzAppearance="basic">
            <aplz-ui-icon name="search" size="sm" class="px-2"></aplz-ui-icon>
          </button>
        </aplz-ui-form-field>
      </header>

      <main class="px-10 pb-36 w-full max-w-lg h-full mx-auto overflow-y-auto">
        <div class="min-h-screen">
          <div class="w-full text-sm font-medium text-gray-900 bg-light">
            <button
              class="block w-full text-left font-light text-md px-4 py-2 my-1 cursor-pointer hover:bg-dark-background hover:text-accent-hover focus:outline-none focus:text-accent-pressed"
              [class.bg-dark-background]="
                !!currentSelection && currentSelection.id === branch.id
              "
              *ngFor="let branch of branches"
              (click)="setSelectedBranch(branch)">
              {{ branch.name }}
            </button>
          </div>
        </div>
      </main>

      <footer
        class="sticky bottom-0 left-0 flex items-center justify-center w-full min-h-[90px] px-8 py-4 bg-light shadow-dark shadow-xl z-20">
        <button
          aplzButton
          aplzAppearance="solid"
          size="md"
          aplzColor="dark"
          data-test="branch-selection-button-select-branch"
          [disabled]="!currentSelection"
          (click)="selectBranch()">
          {{ injectedText.selectButton }}
        </button>
      </footer>
    </div>
  </ng-container>
</ng-container>
