import { As<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgI<PERSON> } from '@angular/common';
import { Component, Input, Output, inject } from '@angular/core';
import { AplazoDynamicPipe, AplazoMatchMediaService } from '@aplazo/shared-ui';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { AplazoDropdownComponents } from '@aplazo/shared-ui/dropdown';
import { AplazoIconComponent } from '@aplazo/shared-ui/icon';
import { AplazoSimpleTableComponents } from '@aplazo/shared-ui/simple-table';
import { AplazoTooltipDirective } from '@aplazo/shared-ui/tooltip';
import { Subject } from 'rxjs';
import { IProductRequestUIDto } from '../../../../core/domain/product-request';

export interface ICartProductsTableInjectedTextUI {
  product: string;
  sku: string;
  quantity: string;
  price: string;
  actions: {
    label: string;
    editButton: string;
    deleteButton: string;
  };
}

@Component({
  standalone: true,
  selector: 'aplazo-cart-products-table',
  imports: [
    AplazoSimpleTableComponents,
    AplazoIconComponent,
    AplazoButtonComponent,
    AplazoDropdownComponents,
    AplazoTooltipDirective,
    AplazoCardComponent,
    NgIf,
    NgFor,
    NgClass,
    AsyncPipe,
    AplazoDynamicPipe,
  ],
  templateUrl: './cart-products-table.component.html',
})
export class CartProductsTableComponent {
  readonly #matchMedia = inject(AplazoMatchMediaService);

  @Output()
  editEvent = new Subject<IProductRequestUIDto>();

  @Output()
  deleteEvent = new Subject<IProductRequestUIDto>();

  @Input()
  products: IProductRequestUIDto[];

  @Input()
  set i18Text(value: ICartProductsTableInjectedTextUI | null) {
    if (!value) {
      console.warn('cart products table i18Text is null');
    }
    this.#text = value ?? null;
  }
  get text(): ICartProductsTableInjectedTextUI | null {
    return this.#text;
  }
  #text: ICartProductsTableInjectedTextUI | null = null;

  isLargeScreen$ = this.#matchMedia.matchLgScreen$;

  edit(product: IProductRequestUIDto): void {
    this.editEvent.next({ ...product });
  }

  delete(product: IProductRequestUIDto): void {
    this.deleteEvent.next({ ...product });
  }
}
