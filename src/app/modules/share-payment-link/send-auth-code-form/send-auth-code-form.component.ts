import { NgIf } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { AplazoButtonComponent } from '@aplazo/shared-ui/button';
import { AplazoFormFieldDirectives } from '@aplazo/shared-ui/forms';
import { NgxMaskDirective } from 'ngx-mask';

@Component({
  standalone: true,
  selector: 'aplazo-send-auth-code-form',
  imports: [
    AplazoFormFieldDirectives,
    ReactiveFormsModule,
    AplazoButtonComponent,
    NgxMaskDirective,
    NgIf,
  ],
  template: `
    <div class="py-4">
      <div class="my-4">
        <aplz-ui-form-field>
          <input
            aplzFormInput
            [formControl]="phoneControl"
            placeholder="00 0000 0000"
            mask="99 9999 9999"
            inputmode="tel" />
          <p aplzInputPrefix class="pt-1 pl-2">+52</p>
        </aplz-ui-form-field>
      </div>
      <div *ngIf="!messageSent; else resend" class="flex justify-end gap-x-4">
        <button
          aplzButton
          aplzAppearance="solid"
          size="md"
          aplzColor="dark"
          type="button"
          [disabled]="phoneControl.invalid"
          (click)="onRequestCustomerAuthCode()">
          Enviar
        </button>
      </div>
      <ng-template #resend>
        <div class="flex justify-center">
          <button
            aplzButton
            aplzAppearance="stroked"
            size="md"
            type="button"
            class="mx-1"
            (click)="onUpdatePhone()">
            Corregir número
          </button>
          <button
            aplzButton
            aplzAppearance="stroked"
            size="md"
            type="button"
            class="mx-1"
            (click)="onRequestCustomerAuthCode()">
            Reenviar
          </button>
        </div>
      </ng-template>
      <div *ngIf="messageSent" class="py-4">
        <div class="my-4">
          <aplz-ui-form-field>
            <aplz-ui-form-label>Código de autorización</aplz-ui-form-label>
            <input
              aplzFormInput
              [formControl]="authCodeControl"
              placeholder="AAAAA"
              inputmode="text"
              class="uppercase"
              maxLength="5" />
          </aplz-ui-form-field>
        </div>
        <div class="flex justify-end gap-x-4">
          <button
            aplzButton
            aplzAppearance="solid"
            size="md"
            aplzColor="dark"
            type="button"
            [disabled]="authCodeControl.invalid"
            (click)="onConfirmFirstInstallment()">
            Confirmar compra
          </button>
        </div>
      </div>
    </div>
  `,
})
export class SendAuthCodeFormComponent {
  readonly phoneControl = new FormControl('', {
    validators: [Validators.required, Validators.minLength(10)],
    nonNullable: true,
  });
  readonly authCodeControl = new FormControl('', {
    validators: [Validators.required, Validators.pattern(/^[a-zA-Z0-9]{5}$/)],
    nonNullable: true,
  });
  #messageSent = false;
  @Output()
  public requestCustomerAuthCode = new EventEmitter<number>();
  @Output()
  public updatePhone = new EventEmitter<void>();
  @Output()
  public confirmFirstInstallment = new EventEmitter<{
    phone: number;
    authCode: string;
  }>();
  @Output()
  public cancel = new EventEmitter<void>();
  @Input()
  set messageSent(messageSent: boolean | null) {
    if (messageSent) {
      this.phoneControl.disable();
    } else {
      this.phoneControl.enable();
      this.authCodeControl.reset();
    }
    this.#messageSent = !!messageSent;
  }
  get messageSent(): boolean {
    return this.#messageSent;
  }

  onRequestCustomerAuthCode(): void {
    this.requestCustomerAuthCode.emit(+this.phoneControl.value);
  }

  onConfirmFirstInstallment(): void {
    this.confirmFirstInstallment.emit({
      phone: +this.phoneControl.value,
      authCode: this.authCodeControl.value,
    });
  }

  onUpdatePhone(): void {
    this.updatePhone.emit();
  }

  onCancel(): void {
    this.cancel.emit();
  }
}
