import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AplazoPopupComponent } from 'src/app/modules/shared/popup/infra/components/aplazo-popup/aplazo-popup.component';
import { DialogRef } from '@ngneat/dialog';

describe('AplazoPopupComponent', () => {
  let component: AplazoPopupComponent;
  let fixture: ComponentFixture<AplazoPopupComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AplazoPopupComponent],
      providers: [
        {
          provide: DialogRef,
          useValue: {
            close: jasmine.createSpy('close'),
            data: {
              htmlContent: '<h2>Test Popup</h2><p>Contenido dinámico</p>',
            },
          },
        },
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(AplazoPopupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should render provided HTML content', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.innerHTML).toContain('<h2>Test Popup</h2>');
    expect(compiled.innerHTML).toContain('<p>Contenido dinámico</p>');
    // Verifica que el DOM incluye el HTML dinámico, no el texto estático anterior
  });

  it('should render fallback if no htmlContent provided', () => {
    TestBed.resetTestingModule();
    TestBed.configureTestingModule({
      imports: [AplazoPopupComponent],
      providers: [
        {
          provide: DialogRef,
          useValue: {
            close: jasmine.createSpy('close'),
            data: {},
          },
        },
      ],
    }).compileComponents();
    const testFixture = TestBed.createComponent(AplazoPopupComponent);
    testFixture.detectChanges();
    const compiled = testFixture.nativeElement as HTMLElement;
    expect(compiled.innerHTML).toContain('<p>No content</p>');
  });

  // ✅ NEW TESTS FOR PERMANENT DISMISS CHECKBOX
  describe('Permanent Dismiss Functionality', () => {
    it('should display checkbox for permanent dismiss', () => {
      const compiled = fixture.nativeElement as HTMLElement;
      const checkbox = compiled.querySelector('input[type="checkbox"]');
      expect(checkbox).toBeTruthy();
    });

    it('should have correct label for permanent dismiss checkbox', () => {
      const compiled = fixture.nativeElement as HTMLElement;
      const label = compiled.querySelector('label');
      expect(label?.textContent?.trim()).toContain('No mostrar de nuevo');
    });

    it('should emit dismiss event when checkbox is checked and close button clicked', () => {
      const dialogRef = TestBed.inject(DialogRef);
      const compiled = fixture.nativeElement as HTMLElement;

      // Check the checkbox
      const checkbox = compiled.querySelector(
        'input[type="checkbox"]'
      ) as HTMLInputElement;
      checkbox.checked = true;
      checkbox.dispatchEvent(new Event('change'));
      fixture.detectChanges();

      // Click close button
      const closeButton = compiled.querySelector(
        'button[aria-label="Cerrar"]'
      ) as HTMLButtonElement;
      closeButton.click();

      expect(dialogRef.close).toHaveBeenCalledWith({ permanentDismiss: true });
    });

    it('should emit normal close when checkbox is unchecked', () => {
      const dialogRef = TestBed.inject(DialogRef);
      const compiled = fixture.nativeElement as HTMLElement;

      // Ensure checkbox is unchecked
      const checkbox = compiled.querySelector(
        'input[type="checkbox"]'
      ) as HTMLInputElement;
      checkbox.checked = false;
      checkbox.dispatchEvent(new Event('change'));
      fixture.detectChanges();

      // Click close button
      const closeButton = compiled.querySelector(
        'button[aria-label="Cerrar"]'
      ) as HTMLButtonElement;
      closeButton.click();

      expect(dialogRef.close).toHaveBeenCalledWith({ permanentDismiss: false });
    });

    it('should update checkbox state when changed', () => {
      const compiled = fixture.nativeElement as HTMLElement;
      const checkbox = compiled.querySelector(
        'input[type="checkbox"]'
      ) as HTMLInputElement;

      // Initially unchecked
      expect(component.permanentDismiss()).toBe(false);

      // Check the checkbox
      checkbox.checked = true;
      checkbox.dispatchEvent(new Event('change'));
      fixture.detectChanges();

      expect(component.permanentDismiss()).toBe(true);
    });

    it('should have proper accessibility attributes for checkbox', () => {
      const compiled = fixture.nativeElement as HTMLElement;
      const checkbox = compiled.querySelector(
        'input[type="checkbox"]'
      ) as HTMLInputElement;

      expect(checkbox.getAttribute('id')).toBe('permanent-dismiss');
      expect(checkbox.getAttribute('aria-describedby')).toBe(
        'permanent-dismiss-description'
      );
    });

    it('should have proper label association', () => {
      const compiled = fixture.nativeElement as HTMLElement;
      const label = compiled.querySelector('label') as HTMLLabelElement;

      expect(label.getAttribute('for')).toBe('permanent-dismiss');
    });
  });
});
