import { TestBed } from '@angular/core/testing';
import { LoaderService, NotifierService } from '@aplazo/merchant/shared';
import { LocalLoader, LocalNotifier } from '@aplazo/merchant/shared-testing';
import { lastValueFrom, take } from 'rxjs';
import { PosOfflineRepository } from '../../../app/core/domain/repositories/pos-offline.repository';
import { CancelOrderUseCase } from '../../../app/modules/orders-v1/application/usecases/cancel-order.usecase';
import { ordersDummy } from '../../../mocks/order.dummy';
import { LocalPosOfflineRepository } from '../../core/services/impl/local-pos-offline.repository';

describe('CancelOrderUseCase', () => {
  let useCase: CancelOrderUseCase;
  let repository: PosOfflineRepository;
  let loaderService: LoaderService;
  let notifierService: NotifierService;

  let showLoaderSpy: jasmine.Spy;
  let hideLoaderSpy: jasmine.Spy;
  let deleteOrderSpy: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        CancelOrderUseCase,
        {
          provide: PosOfflineRepository,
          useClass: LocalPosOfflineRepository,
        },
        {
          provide: LoaderService,
          useClass: LocalLoader,
        },
        {
          provide: NotifierService,
          useClass: LocalNotifier,
        },
      ],
    });

    repository = TestBed.inject(PosOfflineRepository);
    loaderService = TestBed.inject(LoaderService);
    notifierService = TestBed.inject(NotifierService);
    useCase = TestBed.inject(CancelOrderUseCase);

    showLoaderSpy = spyOn(loaderService, 'show').and.callThrough();
    hideLoaderSpy = spyOn(loaderService, 'hide').and.callThrough();
    deleteOrderSpy = spyOn(repository, 'deleteOrder').and.callThrough();
  });

  it('should be created', () => {
    expect(useCase).toBeTruthy();
  });

  it('should call show, hide, deleteOrder and success when execute is invoked', async () => {
    await lastValueFrom(useCase.execute(ordersDummy[0]).pipe(take(1)));

    expect(deleteOrderSpy).toHaveBeenCalledTimes(1);
    expect(showLoaderSpy).toHaveBeenCalledTimes(1);
    expect(hideLoaderSpy).toHaveBeenCalledTimes(1);
  });
});
