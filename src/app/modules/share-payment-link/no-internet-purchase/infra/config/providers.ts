import { EnvironmentProviders, makeEnvironmentProviders } from '@angular/core';
import { RequestOTPUseCase } from '../../application/request-otp.usecase';
import { ValidateOTPUseCase } from '../../application/validate-otp.usecase';
import { NoInternetPurchaseRepository } from '../../domain/repositories/no-internet-purchase.repository';
import { NoInternetPurchaseWithHttp } from '../repositories/no-internet-purchase.repository';

export function provideNoInternetPurchase(): EnvironmentProviders {
  return makeEnvironmentProviders([
    {
      provide: NoInternetPurchaseRepository,
      useClass: NoInternetPurchaseWithHttp,
    },
    RequestOTPUseCase,
    ValidateOTPUseCase,
  ]);
}
