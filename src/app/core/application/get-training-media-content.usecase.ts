import { Injectable } from '@angular/core';
import {
  LoaderService,
  RuntimeMerchantError,
  UseCaseErrorHandler,
} from '@aplazo/merchant/shared';
import {
  MonoTypeOperatorFunction,
  Observable,
  OperatorFunction,
  catchError,
  finalize,
  map,
  of,
  pipe,
  switchMap,
  take,
} from 'rxjs';
import {
  CreateNotificationDto,
  IMediaContentUIDto,
  INotificationResponseDto,
  ITrainingMediaContentUIDto,
  MappedMediaContentTags,
  NotificationStatus,
  NotificationType,
  NotificationUI,
} from '../domain/notification';
import { NotificationsRepository } from '../domain/repositories/notifications.repository';
import { DateUtilsService } from '../services/date-utils.service';
import { StoreService } from './services/store.service';

@Injectable({ providedIn: 'root' })
export class GetTrainingContentMediaUsecase {
  readonly #activeStatus: NotificationStatus = 'A';
  readonly #defaultThumbnail =
    'https://cdn.aplazo.mx/merchant-dash-assets/default-video.thumb.png';
  readonly #errorImageThumbnail =
    'https://cdn.aplazo.mx/merchant-dash-assets/not-video-found.png';
  readonly #trainingNotificationType: NotificationType = 'TRAINING';

  notificationType(): NotificationType {
    return this.#trainingNotificationType;
  }

  constructor(
    private notificationService: NotificationsRepository,
    private loader: LoaderService,
    private dateUtils: DateUtilsService,
    private readonly storeService: StoreService,
    private usecaseError: UseCaseErrorHandler
  ) {}

  execute(): Observable<ITrainingMediaContentUIDto | null> {
    const idLoader = this.loader.show();
    return this.storeService.selectedBranch$.pipe(
      take(1),

      switchMap(branch => {
        if (branch === null) {
          throw new RuntimeMerchantError(
            'No se pudo obtener el id del comercio',
            'GetNotificationsByTypeUsecase::execute::branch'
          );
        }

        return this.notificationService.getNotificationsByTypeAndBranchId(
          this.notificationType(),
          branch.id
        );
      }),

      this.#hasNewOnes(),

      this.#createNotificationUI(),

      this.#filterByStatus(this.#activeStatus),

      this.#parseTrainingItemUI(),

      catchError(error => this.usecaseError.handle(error, of(null))),

      take(1),

      finalize(() => {
        this.loader.hide(idLoader);
      })
    );
  }

  #hasNewOnes(): OperatorFunction<
    INotificationResponseDto[],
    Array<CreateNotificationDto & { isNewOne: boolean }>
  > {
    return pipe(
      map(response =>
        response.map(item => ({
          ...item,
          lastUpdate: this.dateUtils.parseFromISO(item.lastUpdate),
          isNewOne: this.dateUtils.isTodayOrYesterday(item.lastUpdate),
        }))
      )
    );
  }

  #createNotificationUI(): OperatorFunction<
    Array<CreateNotificationDto & { isNewOne: boolean }>,
    NotificationUI[]
  > {
    return pipe(
      map(response =>
        response
          .map(item => NotificationUI.create(item))
          .sort((a, b) => b.lastUpdateTimestamp - a.lastUpdateTimestamp)
      )
    );
  }

  #filterByStatus(
    status: NotificationStatus
  ): MonoTypeOperatorFunction<NotificationUI[]> {
    return pipe(
      map(parsedResponse =>
        parsedResponse.filter(
          item => item.status.toLowerCase() === status.toLowerCase()
        )
      )
    );
  }

  #parseTrainingItemUI(): OperatorFunction<
    NotificationUI[],
    {
      tags: MappedMediaContentTags[];
      content: IMediaContentUIDto[];
    }
  > {
    return pipe(
      map(items => {
        const tags: MappedMediaContentTags[] = [];
        const mediaContent: IMediaContentUIDto[] = [];

        items.forEach(item => {
          if (item.complement) {
            item.complement.tags.forEach(tag => {
              if (!tags.includes(MappedMediaContentTags[tag])) {
                tags.push(MappedMediaContentTags[tag]);
              }
            });
          }

          let thumbnailUrl = '';
          let videoUrl = '';
          let isValidVideoUrl = false;

          if (!item.complement) {
            return;
          }

          try {
            thumbnailUrl = new URL(item.complement?.urlThumbnail).toString();
          } catch (error) {
            thumbnailUrl = this.#defaultThumbnail;
          }

          try {
            videoUrl = new URL(item.complement.urlVideo).toString();
            isValidVideoUrl = true;
          } catch (error) {
            thumbnailUrl = this.#errorImageThumbnail;

            console.error(
              'Invalid media content -> video URL: ',
              `title:  ${item.title}`,
              `videUrl: ${item.complement.urlVideo}`
            );
          }

          mediaContent.push({
            title: item.title,
            description: item.description,
            urlVideo: videoUrl,
            isValidVideoUrl,
            urlThumbnail: thumbnailUrl,
            tags:
              item.complement?.tags.map(t => MappedMediaContentTags[t]) ?? [],
            id: item.id,
          });
        });

        return {
          tags,
          content: mediaContent,
        };
      })
    );
  }
}
