import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { Observable, catchError, map } from 'rxjs';
import { POS_ENVIRONMENT_CORE } from '../../../../app-core/domain/environments';
import { PaymentsResponse, PaymentsResponseUIDto, Scheme } from './payment';

@Injectable({
  providedIn: 'root',
})
export class DynamicTokenService {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(POS_ENVIRONMENT_CORE);
  readonly #mspaymentMicro = this.#environment.mspaymentMicro;

  getCalendar(params: {
    code: string;
    loanId: number;
    phone: string;
  }): Observable<PaymentsResponseUIDto> {
    return this.#http
      .post<PaymentsResponse>(
        `${this.#mspaymentMicro}api/v1/checkout/${params.loanId}/merchant`,
        {
          username: params.phone,
          code: params.code,
        }
      )
      .pipe(
        map(response => {
          const scheme = response.schemes.find(
            sc => sc.installments === 5
          ) as Scheme;

          return {
            ...response,
            schemes: {
              ...scheme,
            },
          };
        }),
        catchError(e => {
          throw e;
        })
      );
  }

  finishOrder(params: {
    loandId: string;
    sessionId: string;
    schemeId: number;
    phone: string;
    code: string;
  }): Observable<any> {
    return this.#http.post<any>(
      `${this.#mspaymentMicro}api/v1/payment/first-installment/merchant`,
      {
        username: params.phone,
        code: params.code,
        sessionId: params.sessionId,
        schemeId: params.schemeId,
        useAplazoPoints: false,
      },
      {
        params: {
          'loan-id': params.loandId,
        },
      }
    );
  }
}
