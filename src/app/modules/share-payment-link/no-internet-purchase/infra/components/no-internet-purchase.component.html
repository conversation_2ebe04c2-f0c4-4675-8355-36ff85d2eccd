<ng-container [ngTemplateOutlet]="step$ | async"> </ng-container>

<ng-template #idle>
  <h3 class="text-lg font-medium mt-4 mb-6">
    Solicita al cliente el número telefónico registrado en Aplazo.
  </h3>

  <form [formGroup]="requestOtpForm" (ngSubmit)="requestOTP()">
    <aplz-ui-form-field>
      <aplz-ui-form-label> Teléfono </aplz-ui-form-label>

      <input
        type="text"
        aplzFormInput
        formControlName="phoneNIP"
        placeholder="00 0000 0000"
        inputmode="tel"
        aplazoOnlyNumbers
        [aplazoTruncateLength]="10" />
      <p aplzInputPrefix class="pt-1 pl-2">+52</p>

      <p
        aplzFormError
        *ngIf="phoneNIP.touched && phoneNIP.hasError('required')">
        El teléfono es requerido
      </p>

      <p
        aplzFormError
        *ngIf="
          phoneNIP.touched &&
          !phoneNIP.hasError('required') &&
          (phoneNIP.hasError('minlength') || phoneNIP.hasError('maxlength'))
        ">
        El teléfono debe tener 10 dígitos
      </p>

      <p aplzFormError *ngIf="phoneNIP.hasError(this.otpErrorKey)">
        {{ phoneNIP.getError('otpError') }}
      </p>
    </aplz-ui-form-field>

    <div class="flex justify-end gap-4 w-full mt-12">
      <button
        *ngIf="phoneNIP.hasError(this.otpErrorKey)"
        aplzButton
        type="button"
        aplzAppearance="stroked"
        aplzColor="light"
        size="md"
        (click)="clearOTPRequest()">
        Nuevo teléfono
      </button>

      <button
        aplzButton
        type="submit"
        aplzAppearance="solid"
        aplzColor="dark"
        size="md"
        [disabled]="
          (isLoading$ | async) === true ||
          this.phoneNIP.hasError(this.otpErrorKey)
        ">
        <span class="mr-3">
          {{ (isLoading$ | async) === true ? 'Enviando...' : 'Enviar código' }}
        </span>

        <aplz-ui-icon
          *ngIf="(isLoading$ | async) === true"
          name="spin-circle"
          size="sm"></aplz-ui-icon>
      </button>
    </div>
  </form>
</ng-template>

<ng-template #validateOTP>
  <h3 class="text-lg font-medium mt-4 mb-6">Registro de Código.</h3>
  <form [formGroup]="validateOtpForm" (ngSubmit)="validateCode()">
    <aplz-ui-form-field>
      <aplz-ui-form-label> Código autorización </aplz-ui-form-label>

      <input
        type="text"
        aplzFormInput
        formControlName="otpCode"
        placeholder="AAAAAA"
        inputmode="search"
        aplazoUppercase />

      <p aplzFormError *ngIf="otpCode.touched && otpCode.hasError('required')">
        El código de autorización es requerido
      </p>

      <p aplzFormError *ngIf="otpCode.hasError(this.otpErrorKey)">
        {{ otpCode.getError('otpError') }}
      </p>
    </aplz-ui-form-field>

    <div class="flex justify-end gap-4 w-full mt-12">
      <button
        aplzButton
        type="button"
        aplzAppearance="stroked"
        aplzColor="light"
        size="md"
        [disabled]="(isLoading$ | async) === true"
        (click)="resetAndClose()">
        Cancelar
      </button>

      <button
        aplzButton
        type="submit"
        aplzAppearance="solid"
        aplzColor="dark"
        size="md"
        [disabled]="
          (isLoading$ | async) === true || otpCode.hasError(this.otpErrorKey)
        ">
        <span class="mr-3">
          {{ (isLoading$ | async) === true ? 'Procesando...' : 'Enviar' }}
        </span>

        <aplz-ui-icon
          *ngIf="(isLoading$ | async) === true"
          name="spin-circle"
          size="sm"></aplz-ui-icon>
      </button>
    </div>
  </form>
</ng-template>
