<!-- Toggle Button -->
<button
  *ngIf="!isOpen"
  class="popup-demo-toggle-btn"
  (click)="togglePanel()"
  title="Popup Demo Panel">
  🧪
</button>

<!-- Demo Panel -->
<div *ngIf="isOpen" class="popup-demo-widget">
  <button class="popup-demo-close-btn" (click)="togglePanel()">×</button>

  <div class="popup-demo-content">
    <div class="popup-demo-section">
      <h3>🧪 Popup Demo Panel</h3>
      <p>Debug and test popup functionality</p>
    </div>

    <div class="popup-demo-section">
      <h4>Manual Triggers</h4>
      <div class="popup-demo-grid">
        <button (click)="triggerLoginPopup()">Trigger Login Popup</button>
        <button (click)="triggerPaymentPopup()">Trigger Payment Popup</button>
        <button (click)="clearPopupStorage()">Clear Storage</button>
        <button (click)="refreshPopups()">Refresh Popups</button>
      </div>
    </div>

    <div class="popup-demo-section">
      <h4>Configuration</h4>
      <div class="popup-demo-grid">
        <label>
          <input type="checkbox" [(ngModel)]="config.autoTrigger" />
          Auto Trigger
        </label>
        <label>
          <input type="checkbox" [(ngModel)]="config.debugMode" />
          Debug Mode
        </label>
      </div>
    </div>

    <div class="popup-demo-section">
      <h4>Custom HTML Popup</h4>
      <textarea
        class="popup-demo-textarea"
        [(ngModel)]="customHtml"
        placeholder="Enter custom HTML content for popup...">
      </textarea>
      <button (click)="showCustomPopup()">Show Custom Popup</button>
    </div>

    <div class="popup-demo-section">
      <h4>Debug Info</h4>
      <div class="popup-demo-grid">
        <div>
          <strong>Available Popups:</strong> {{ availablePopups.length }}
        </div>
        <div><strong>Shown Today:</strong> {{ shownToday.length }}</div>
      </div>
      <div *ngIf="config.debugMode">
        <small>Last Action: {{ lastAction }}</small>
      </div>
    </div>
  </div>
</div>
