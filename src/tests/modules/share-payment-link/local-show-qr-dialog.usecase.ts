import { Order } from '../../../app/core/domain/order.interface';
import { ShowOnlyQrDialogUsecase } from '../../../app/modules/share-payment-link/application/show-only-qr-dialog.usecase';

export class LocalShowQrDialog implements ShowOnlyQrDialogUsecase {
  async execute(loan: Order, showAlternativeHeader?: boolean): Promise<void> {
    console.log('local show qr dialog :::: order  :::: >>>>  ', loan);
    console.log(
      'local show qr dialog :::: alternativeHeader :::: >>>>  ',
      showAlternativeHeader
    );
  }
}
