import { inject } from '@angular/core';
import { CanActivateFn, CanDeactivateFn } from '@angular/router';
import {
  ConnectionStatusService,
  NotifierService,
} from '@aplazo/merchant/shared';
import { lastValueFrom, map, take } from 'rxjs';

export const allowNavigationWhenOnline:
  | CanActivateFn
  | CanDeactivateFn<any> = async () => {
  const statusService = inject(ConnectionStatusService);
  const notifier = inject(NotifierService);

  const isOnline = await lastValueFrom(
    statusService.status$.pipe(
      map(({ isOnline }) => isOnline),
      take(1)
    )
  );

  if (!isOnline) {
    notifier.warning({
      title: 'Verifica la conexión',
      message: 'Esta acción no está disponible sin conexión a internet',
    });
  }

  return isOnline;
};
