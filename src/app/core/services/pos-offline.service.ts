import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { UIDateRange } from '@aplazo/shared-ui';
import { Observable, catchError, map, of, take } from 'rxjs';
import { POS_ENVIRONMENT_CORE } from '../../../app-core/domain/environments';
import { LoanRequest } from '../domain/cart';
import { Challenge } from '../domain/challenge';
import { Order } from '../domain/order.interface';
import {
  CommonPosOfflineResponse,
  PosOfflineRepository,
  SendLinkRequestDTO,
} from '../domain/repositories/pos-offline.repository';

@Injectable({ providedIn: 'root' })
export class PosOfflineService implements PosOfflineRepository {
  readonly #http = inject(HttpClient);
  readonly #environment = inject(POS_ENVIRONMENT_CORE);

  getTodayOrders(): Observable<Order[]> {
    return this.#http
      .get<Order[]>(`${this.#environment.posApiUrl}/api/orders/today`)
      .pipe(
        catchError(error => {
          console.error('getTodayOrders', error);

          return of([]);
        })
      );
  }

  getHistoricalWeekOrders(
    startAt?: string,
    endAt?: string
  ): Observable<Order[]> {
    if (!startAt || !endAt) {
      return of([]);
    }
    return this.#http
      .get<Order[]>(
        `${this.#environment.posApiUrl}/api/orders/weekly?startAt=${startAt}&endAt=${endAt}`
      )
      .pipe(map(orders => [...orders].sort((a, b) => b.loanId - a.loanId)));
  }

  getHistoricalReportByDate(startAt: string, endAt: string): Observable<any> {
    if (!startAt || !endAt) {
      return of([]);
    }

    let params: HttpParams = new HttpParams();
    params = params.set('startAt', startAt);
    params = params.set('endAt', endAt);

    return this.#http.get(
      `${this.#environment.posApiUrl}/api/reports/historical`,
      {
        params,
        responseType: 'blob',
      }
    );
  }

  getHistoricalSummaryByDate(dateRange: UIDateRange): Observable<{
    totalOrder: number;
    totalSale: number;
    avgPrice: number;
  } | null> {
    let params: HttpParams = new HttpParams();
    params = params.append('startAt', dateRange.startDate);
    params = params.append('endAt', dateRange.endDate as string);

    return this.#http.get<{
      totalOrder: number;
      totalSale: number;
      avgPrice: number;
    } | null>(`${this.#environment.posApiUrl}/api/orders/summary-historical`, {
      params,
    });
  }

  deleteOrder(loanId: number): Observable<any> {
    return this.#http
      .delete(`${this.#environment.posApiUrl}/api/orders/${loanId}`)
      .pipe(take(1));
  }

  createLoan(loan: LoanRequest): Observable<Order> {
    return this.#http
      .post<Order>(`${this.#environment.posApiUrl}/api/orders`, loan)
      .pipe(take(1));
  }

  createLoanWithPreloan(
    order: LoanRequest,
    preloanCode: string
  ): Observable<Order> {
    return this.#http
      .post<Order>(
        `${this.#environment.posApiUrl}/api/orders/create-wit-preloan/${preloanCode}`,
        order
      )
      .pipe(take(1));
  }

  sendPaymentLink(
    request: SendLinkRequestDTO
  ): Observable<CommonPosOfflineResponse<any>> {
    return this.#http.post<CommonPosOfflineResponse<any>>(
      `${this.#environment.posApiUrl}/api/orders/send-checkout-link-sms`,
      {
        orderId: request.loanId,
        phoneNumber: request.phoneNumber,
      }
    );
  }

  getQrImage(loanId: string): Observable<string> {
    return this.#http
      .get<{ base64: string; content: string }>(
        `${this.#environment.posApiUrl}/api/orders/qr-image`,
        {
          params: {
            loanId: loanId,
          },
        }
      )
      .pipe(map(r => r.base64));
  }

  getLastLoanQrImage(loanId: string): Observable<string> {
    return this.#http
      .get<{ qrCode: string; checkoutUrl: string }>(
        `${this.#environment.msMerchantMicro}/api/v1/merchant/checkout-qr-url/me`,
        {
          params: {
            shopId: loanId,
            shortLink: false,
          },
        }
      )
      .pipe(map(r => r.qrCode));
  }

  refundOrder(loanId: number): Observable<any> {
    return this.#http.post(
      `${this.#environment.posApiUrl}/api/orders/refund/${loanId}`,
      {}
    );
  }

  sendWSPaymentLink(
    request: SendLinkRequestDTO
  ): Observable<CommonPosOfflineResponse<any>> {
    return this.#http.post<CommonPosOfflineResponse<any>>(
      `${this.#environment.posApiUrl}/api/orders/send-checkout-link`,
      {
        phoneNumber: request.phoneNumber,
        orderId: request.loanId,
      }
    );
  }

  checkOrderStatus(
    loanId: number
  ): Observable<CommonPosOfflineResponse<Order>> {
    return this.#http.post<CommonPosOfflineResponse<Order>>(
      `${this.#environment.posApiUrl}/api/orders/${loanId}/validate-status`,
      {}
    );
  }

  getChallenges(branchId: number): Observable<Challenge[]> {
    return this.#http
      .get<any>(`${this.#environment.posApiUrl}/api/challenges`, {
        params: {
          branchId: branchId,
        },
      })
      .pipe(map(r => r.content ?? []));
  }

  requestReport(): Observable<Blob> {
    return this.#http.post(
      `${this.#environment.posApiUrl}/api/reports/today`,
      {},
      { responseType: 'blob' }
    );
  }
}
