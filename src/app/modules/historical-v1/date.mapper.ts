import { inject, Injectable } from '@angular/core';
import {
  Guard,
  RawDateYearFirstWithHyphen,
  TemporalService,
} from '@aplazo/merchant/shared';
import { UIDateRange } from '@aplazo/shared-ui';
import { RuntimePosOfflineError } from '../../core/domain/runtime-error';

export const notDateRangeProvided =
  'Conversión Rango de fecha: No se ha proporcionado un rango de fechas válido';

@Injectable({
  providedIn: 'root',
})
export class DateMapper {
  readonly #temporal = inject(TemporalService);

  // the initial date range format is provided by the UI component
  // from dd/MM/yyyy to yyyy-MM-dd
  public static fromDateRangeToRequest(date: UIDateRange): {
    startDate: string;
    endDate: string;
  } {
    if (!date || !date.startDate || !date.endDate) {
      throw new RuntimePosOfflineError(
        notDateRangeProvided,
        'DateMapper::invalid-date-range',
        'date-mapper-error'
      );
    }

    if (!Guard.againstInvalidRawDateDayFirst(date.startDate as any).succeeded) {
      throw new RuntimePosOfflineError(
        notDateRangeProvided,
        'DateMapper::fromDateRangeToRequest::invalidaStartDate',
        'date-mapper-error'
      );
    }

    if (!Guard.againstInvalidRawDateDayFirst(date.endDate as any).succeeded) {
      throw new RuntimePosOfflineError(
        notDateRangeProvided,
        'DateMapper::fromDateRangeToRequest::invalidaEndDate',
        'date-mapper-error'
      );
    }

    const startDate = date.startDate.split('/').reverse().join('-');

    const endDate = date.endDate.split('/').reverse().join('-');

    return {
      startDate,
      endDate,
    };
  }

  fromDateRangeToRawDateYearFistWithHyphen(range: {
    startDate: Date;
    endDate: Date;
  }): {
    startDate: RawDateYearFirstWithHyphen;
    endDate: RawDateYearFirstWithHyphen;
  } {
    return {
      startDate: this.#temporal.formatRawDateYearFirstWithHyphen(
        range.startDate
      ),
      endDate: this.#temporal.formatRawDateYearFirstWithHyphen(range.endDate),
    };
  }
}
