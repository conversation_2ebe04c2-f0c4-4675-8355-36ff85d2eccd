import { Observable } from 'rxjs';
import { IUserJwt } from '../../domain/access-token';
import { Branch } from '../../domain/entities';
import { MerchantConfig } from '../../domain/merchant-config';

export interface IShareLinkFlags {
  isWhatsappAllowed: boolean;
  isQrAllowed: boolean;
  isSMSAllowed: boolean;
}

export interface IBranchFeatureFlags {
  isSellAgentTrackEnable: boolean;
  isSupportChatEnable: boolean;
  isOrderRefundEnable: boolean;
  isTodayReportEnable: boolean;
}

export abstract class StoreService {
  abstract token$: Observable<string | null>;
  abstract merchantConfig$: Observable<MerchantConfig | null>;
  abstract decodedToken$: Observable<IUserJwt | null>;

  abstract getToken(): string | null;

  abstract getMerchantId$(): Observable<number | null>;

  abstract setMerchantConfig(config: MerchantConfig | null): void;

  abstract setToken(token: string | null): void;

  abstract setDecodedToken(token: IUserJwt | null): void;

  abstract setSelectedBranch(branch: Branch | null): void;

  abstract selectedBranch$: Observable<Branch | null>;

  abstract isLoggedIn$: Observable<boolean>;

  abstract userRole$: Observable<string>;

  abstract merchantName$: Observable<string>;

  abstract integrationType$: Observable<string>;

  abstract selectedBranchName$: Observable<string | null>;

  abstract selectedBranchShareLinkFlags$: Observable<IShareLinkFlags | null>;

  abstract hasBranchShareLinkEnabled$: Observable<boolean>;

  abstract selectedBranchFeatureFlags$: Observable<IBranchFeatureFlags | null>;

  abstract hasBranchCreationEnabled$: Observable<boolean>;

  abstract branches$: Observable<Branch[]>;

  abstract hasBranches$: Observable<boolean>;

  abstract minimumOrderAmount$: Observable<number | null>;

  abstract merchantLogoUrl$: Observable<string | null>;

  abstract clearStore(): void;

  abstract setStoreFromSessionStorage(): Promise<void>;

  abstract setMerchantName(merchantName: string | null): Promise<void>;
}
