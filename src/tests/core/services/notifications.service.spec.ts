import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import {
  HttpClientTestingModule,
  HttpTestingController,
} from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';
import { INotificationResponseDto } from 'src/app/core/domain/notification';
import { POS_ENVIRONMENT_CORE } from '../../../app-core/domain/environments';
import { NOtificationsService } from '../../../app/core/services/notifications.service';
import promotionsData from '../promotions.db.json';

describe('NotificationsService', () => {
  let httpController: HttpTestingController;
  let service: NOtificationsService;
  let spyHttp: jasmine.Spy;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        NOtificationsService,
        {
          provide: POS_ENVIRONMENT_CORE,
          useValue: {
            promoApiUrl: 'https://mpromotions.aplazo.net/',
          },
        },
      ],
    });

    httpController = TestBed.inject(HttpTestingController);
    service = TestBed.inject(NOtificationsService);
    spyHttp = spyOn(TestBed.inject(HttpClient), 'get').and.callThrough();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
    expect(service).toBeInstanceOf(NOtificationsService);
  });

  it('should retrieve notifications successfully', () => {
    const expectedResponse = promotionsData as INotificationResponseDto[];

    service.getNotificationsByTypeAndMerchantId('PROMO').subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://mpromotions.aplazo.net/api/v1/promotions?type=PROMO'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should retrieve notifications successfully with valid merchantId', () => {
    const expectedResponse = promotionsData as INotificationResponseDto[];

    service.getNotificationsByTypeAndMerchantId('PROMO', 1).subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://mpromotions.aplazo.net/api/v1/promotions?type=PROMO&merchantId=1'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should retrieve notifications successfully with invalid merchantId', () => {
    const expectedResponse = promotionsData as INotificationResponseDto[];

    service.getNotificationsByTypeAndMerchantId('PROMO', 'invalid').subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://mpromotions.aplazo.net/api/v1/promotions?type=PROMO'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should retrieve notifications successfully with null merchantId', () => {
    const expectedResponse = promotionsData as INotificationResponseDto[];

    service
      .getNotificationsByTypeAndMerchantId(
        'PROMO',
        //@ts-expect-error: testing purposes
        null
      )
      .subscribe({
        next: response => {
          expect(response).toEqual(expectedResponse);
          expect(spyHttp).toHaveBeenCalledTimes(1);
        },
        error: fail,
      });

    const req = httpController.expectOne(
      'https://mpromotions.aplazo.net/api/v1/promotions?type=PROMO'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should retrieve notifications successfully with a merchantId lesser than 0', () => {
    const expectedResponse = promotionsData as INotificationResponseDto[];

    service.getNotificationsByTypeAndMerchantId('PROMO', -1).subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://mpromotions.aplazo.net/api/v1/promotions?type=PROMO'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should retrieve notifications successfully with a merchantId equal to 0', () => {
    const expectedResponse = promotionsData as INotificationResponseDto[];

    service.getNotificationsByTypeAndMerchantId('PROMO', 0).subscribe({
      next: response => {
        expect(response).toEqual(expectedResponse);
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
      error: fail,
    });

    const req = httpController.expectOne(
      'https://mpromotions.aplazo.net/api/v1/promotions?type=PROMO'
    );

    expect(req.request.method).toBe('GET');

    req.flush(expectedResponse);
  });

  it('should throws an error when the request fails', () => {
    service.getNotificationsByTypeAndMerchantId('PROMO').subscribe({
      next: fail,
      error: response => {
        expect(response).toBeTruthy();
        expect(response).toBeInstanceOf(HttpErrorResponse);
        expect(response.status).toBe(500);
        expect(response.statusText).toBe('Internal Server Error');
        expect(spyHttp).toHaveBeenCalledTimes(1);
      },
    });

    const req = httpController.expectOne(
      'https://mpromotions.aplazo.net/api/v1/promotions?type=PROMO'
    );

    expect(req.request.method).toBe('GET');

    req.flush('Deliberate error', {
      status: 500,
      statusText: 'Internal Server Error',
    });
  });
});
