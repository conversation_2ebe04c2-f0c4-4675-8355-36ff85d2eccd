import { InjectionToken } from '@angular/core';

export const ROUTE_CONFIG = {
  securedCart: 'product-cart',
  securedOrders: 'orders-list',
  securedOrdersV2: 'orders-list-v2',
  securedHistoric: 'orders-historic',
  securedSelectBranch: 'branch-selection',
  securedNewBranch: 'new-branch',
  securedSettings: 'settings',
  challenges: 'challenges',
  contestEntries: 'premios-aplazo',

  contestPosition: 'position',
  contestRegistration: 'registration',

  preloanCart: 'preloan-cart',

  aplazoRoot: 'aplazo-secured',
  aplazoLayout: 'v1',
  aplazoNotifications: 'notifications',
  aplazoContentMedia: 'aplazoversity',

  aplazoHelp: 'help',

  authentication: 'authorization',
  login: 'login',
  forgotPassword: 'forgot-password',
  forgotPassLink: 'forgot-password-link',
  resetPassword: 'enter-new-password',

  successView: 'success',
  wrongIntegrationView: 'wrong-integration',

  unavailable: 'unavailable',

  // Demo routes
  popupDemo: 'popup-demo',
} as const;

export type RouteConfig = typeof ROUTE_CONFIG;
export type PosRouteName = keyof typeof ROUTE_CONFIG;
export type PosRoute = (typeof ROUTE_CONFIG)[PosRouteName];

export const POS_APP_ROUTES = new InjectionToken<RouteConfig>('POS_APP_ROUTES');
