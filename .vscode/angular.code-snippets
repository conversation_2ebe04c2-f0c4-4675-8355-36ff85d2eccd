{"Angular Component (Standalone)": {"prefix": "ng-component", "body": ["import { Component, signal, computed, effect, inject } from '@angular/core';", "import { CommonModule } from '@angular/common';", "", "interface ${1:ComponentName}State {", "  isLoading: boolean;", "  error: string | null;", "  data: ${2:DataType}[];", "}", "", "@Component({", "  selector: 'app-${3:component-name}',", "  standalone: true,", "  imports: [CommonModule],", "  template: `", "    <div class=\"${4:component-class}\">", "      @if (state().isLoading) {", "        <div class=\"loading\">Loading...</div>", "      } @else if (state().error) {", "        <div class=\"error\">{{ state().error }}</div>", "      } @else {", "        <div class=\"content\">", "          $0", "        </div>", "      }", "    </div>", "  `,", "  styles: [``]", "})", "export class ${1:ComponentName}Component {", "  // State management with signals", "  private readonly state = signal<${1:ComponentName}State>({", "    isLoading: false,", "    error: null,", "    data: []", "  });", "", "  // Computed values", "  readonly isLoading = computed(() => this.state().isLoading);", "  readonly error = computed(() => this.state().error);", "  readonly data = computed(() => this.state().data);", "", "  constructor() {", "    this.initializeComponent();", "  }", "", "  private initializeComponent(): void {", "    effect(() => {", "      console.log('State changed:', this.state());", "    });", "  }", "}"], "description": "Create a modern Angular 17+ standalone component with signals and proper state management"}, "Angular Component with Control Flow": {"prefix": "ng-component-cf", "body": ["import { Component, signal } from '@angular/core';", "import { CommonModule } from '@angular/common';", "", "@Component({", "  selector: 'app-${1:component-name}',", "  standalone: true,", "  imports: [CommonModule],", "  template: `", "    @if (${2:condition}()) {", "      <div>Content when true</div>", "    } @else {", "      <div>Content when false</div>", "    }", "    ", "    @for (item of ${3:items}(); track item.id) {", "      <div>{{ item.name }}</div>", "    }", "  `,", "  styles: [``]", "})", "export class ${4:ComponentName}Component {", "  ${2:condition} = signal(true);", "  ${3:items} = signal<${5:ItemType}[]>(${6:[]});", "  $0", "}"], "description": "Create Angular 17+ component with new control flow syntax"}, "Angular Service (Modern)": {"prefix": "ng-service", "body": ["import { Injectable, inject, signal, computed } from '@angular/core';", "import { HttpClient } from '@angular/common/http';", "import { Observable, catchError, tap, finalize } from 'rxjs';", "import { environment } from 'src/environments/environment';", "", "interface ${1:ServiceName}State {", "  isLoading: boolean;", "  error: string | null;", "  data: ${2:DataType}[];", "}", "", "interface ${2:DataType} {", "  id: number;", "  $0", "}", "", "@Injectable({", "  providedIn: 'root'", "})", "export class ${1:ServiceName}Service {", "  private readonly http = inject(HttpClient);", "  private readonly apiUrl = `${environment.apiUrl}/${3:endpoint}`;", "", "  // State management", "  private readonly state = signal<${1:ServiceName}State>({", "    isLoading: false,", "    error: null,", "    data: []", "  });", "", "  // Computed values", "  readonly isLoading = computed(() => this.state().isLoading);", "  readonly error = computed(() => this.state().error);", "  readonly data = computed(() => this.state().data);", "", "  // Public methods", "  get${2:DataType}(): Observable<${2:DataType}[]> {", "    this.setLoading(true);", "    this.clearError();", "", "    return this.http.get<${2:DataType}[]>(this.apiUrl).pipe(", "      tap(data => this.updateData(data)),", "      catchError(error => this.handleError(error)),", "      finalize(() => this.setLoading(false))", "    );", "  }", "", "  create${2:DataType}(item: Omit<${2:DataType}, 'id'>): Observable<${2:DataType}> {", "    this.setLoading(true);", "    this.clearError();", "", "    return this.http.post<${2:DataType}>(this.apiUrl, item).pipe(", "      tap(newItem => this.addItem(newItem)),", "      catchError(error => this.handleError(error)),", "      finalize(() => this.setLoading(false))", "    );", "  }", "", "  // Private state management methods", "  private setLoading(isLoading: boolean): void {", "    this.state.update(state => ({ ...state, isLoading }));", "  }", "", "  private clearError(): void {", "    this.state.update(state => ({ ...state, error: null }));", "  }", "", "  private updateData(data: ${2:DataType}[]): void {", "    this.state.update(state => ({ ...state, data }));", "  }", "", "  private addItem(item: ${2:DataType}): void {", "    this.state.update(state => ({", "      ...state,", "      data: [...state.data, item]", "    }));", "  }", "", "  private handleError(error: any): Observable<never> {", "    const errorMessage = error?.message || 'An error occurred';", "    this.state.update(state => ({ ...state, error: errorMessage }));", "    throw error;", "  }", "}"], "description": "Create a modern Angular service with signals, proper error handling, and state management"}, "Angular Service with Signals": {"prefix": "ng-service-signals", "body": ["import { Injectable, signal, computed } from '@angular/core';", "import { HttpClient, inject } from '@angular/common/http';", "import { Observable, tap } from 'rxjs';", "", "@Injectable({", "  providedIn: 'root'", "})", "export class ${1:ServiceName}Service {", "  private http = inject(HttpClient);", "", "  // Signals", "  private ${2:data} = signal<${3:DataType}[]>(${4:[]});", "  private ${5:loading} = signal(false);", "  private ${6:error} = signal<string | null>(null);", "", "  // Computed", "  ${7:filteredData} = computed(() => {", "    return this.${2:data}().filter(item => $0);", "  });", "", "  // Methods", "  get${3:DataType}(): Observable<${3:DataType}[]> {", "    this.${5:loading}.set(true);", "    this.${6:error}.set(null);", "    ", "    return this.http.get<${3:DataType}[]>('${8:api-url}').pipe(", "      tap({", "        next: (data) => this.${2:data}.set(data),", "        error: (error) => this.${6:error}.set(error.message),", "        finalize: () => this.${5:loading}.set(false)", "      })", "    );", "  }", "}"], "description": "Create Angular service with signals and modern patterns"}, "Angular Guard (Functional)": {"prefix": "ng-guard", "body": ["import { inject } from '@angular/core';", "import { CanActivateFn, Router } from '@angular/router';", "import { Observable, of } from 'rxjs';", "import { catchError, map } from 'rxjs/operators';", "", "/**", " * Guard to ${1:description}", " * @param route - Current route", " * @param state - Router state", " * @returns Observable<boolean> | Promise<boolean> | boolean", " */", "export const ${2:guardName}Guard: CanActivateFn = (", "  route,", "  state", "): Observable<boolean> | Promise<boolean> | boolean => {", "  const router = inject(Router);", "  $0", "", "  // Example implementation", "  const isAuthenticated = true; // Replace with actual logic", "", "  if (!isAuthenticated) {", "    router.navigate(['/login']);", "    return false;", "  }", "", "  return true;", "};"], "description": "Create a functional guard with proper error handling"}, "Angular Interceptor (Functional)": {"prefix": "ng-interceptor", "body": ["import { HttpInterceptorFn } from '@angular/common/http';", "import { catchError } from 'rxjs/operators';", "import { throwError } from 'rxjs';", "", "/**", " * HTTP interceptor for ${1:description}", " * @param req - HTTP request", " * @param next - Next handler", " * @returns Observable<HttpEvent<any>>", " */", "export const ${2:interceptorName}Interceptor: HttpInterceptorFn = (", "  req,", "  next", ") => {", "  $0", "", "  // Example: Add authorization header", "  const authReq = req.clone({", "    setHeaders: {", "      Authorization: `Bearer ${localStorage.getItem('token')}`", "    }", "  });", "", "  return next(authReq).pipe(", "    catchError(error => {", "      console.error('HTTP Error:', error);", "      return throwError(() => error);", "    })", "  );", "};"], "description": "Create a functional HTTP interceptor with error handling"}, "Angular Observable with Signals": {"prefix": "ng-observable-signals", "body": ["import { Observable, tap } from 'rxjs';", "import { signal } from '@angular/core';", "", "// Signals for state management", "private ${1:data} = signal<${2:Type}[]>(${3:[]});", "private ${4:loading} = signal(false);", "private ${5:error} = signal<string | null>(null);", "", "// Method that updates signals", "get${2:Type}(): Observable<${2:Type}[]> {", "  this.${4:loading}.set(true);", "  this.${5:error}.set(null);", "  ", "  return this.http.get<${2:Type}[]>('${6:url}').pipe(", "    tap({", "      next: (response) => this.${1:data}.set(response),", "      error: (error) => this.${5:error}.set(error.message),", "      finalize: () => this.${4:loading}.set(false)", "    })", "  );", "}"], "description": "Create Observable with signal state management"}, "Angular Signal with Computed": {"prefix": "ng-signal", "body": ["import { signal, computed, effect } from '@angular/core';", "", "// Base signals", "${1:data} = signal<${2:Type}[]>(${3:[]});", "${4:filter} = signal('');", "", "// Computed signals", "${5:filteredData} = computed(() => {", "  const data = this.${1:data}();", "  const filter = this.${4:filter}();", "  ", "  if (!filter) return data;", "  ", "  return data.filter(item => $0);", "});", "", "${6:totalCount} = computed(() => this.${1:data}().length);", "${7:filteredCount} = computed(() => this.${5:filteredData}().length);", "", "// Effects for side effects", "effect(() => {", "  console.log('Data changed:', this.${1:data}());", "});", "", "effect(() => {", "  console.log('Filtered data:', this.${5:filteredData}());", "});"], "description": "Create comprehensive signal setup with computed and effects"}, "Angular Template Form (Modern)": {"prefix": "ng-template-form", "body": ["<form #${1:formName}=\"ngForm\" (ngSubmit)=\"${2:onSubmit}()\">", "  <div class=\"form-group\">", "    <label for=\"${3:fieldName}\">${4:Field Label}</label>", "    <input", "      type=\"${5:text}\"", "      id=\"${3:fieldName}\"", "      name=\"${3:fieldName}\"", "      [(ngModel)]=\"${6:model}\"", "      required", "      class=\"form-control\"", "      #${3:fieldName}=\"ngModel\"", "    />", "    @if (${3:fieldName}.invalid && (${3:fieldName}.dirty || ${3:fieldName}.touched)) {", "      <div class=\"error-message\">", "        @if (${3:fieldName}.errors?.['required']) {", "          <div>This field is required.</div>", "        }", "      </div>", "    }", "  </div>", "  <button type=\"submit\" [disabled]=\"${1:formName}.invalid\">Submit</button>", "</form>"], "description": "Create template-driven form with Angular 17+ control flow"}, "Angular Reactive Form (Modern)": {"prefix": "ng-reactive-form", "body": ["import { FormBuilder, FormGroup, Validators, inject } from '@angular/forms';", "import { signal } from '@angular/core';", "", "export class ${1:ComponentName}Component {", "  private fb = inject(FormBuilder);", "  ", "  ${2:formName}: FormGroup = this.fb.group({", "    ${3:fieldName}: ['', [Validators.required]],", "    $0", "  });", "  ", "  // Signal for form state", "  isFormValid = signal(false);", "  ", "  constructor() {", "    // Watch form validity", "    effect(() => {", "      this.isFormValid.set(this.${2:formName}.valid);", "    });", "  }", "  ", "  onSubmit() {", "    if (this.${2:formName}.valid) {", "      console.log(this.${2:formName}.value);", "    }", "  }", "}"], "description": "Create reactive form with signals and inject()"}, "Angular Test (Modern)": {"prefix": "ng-test", "body": ["import { ComponentFixture, TestBed } from '@angular/core/testing';", "import { NoopAnimationsModule } from '@angular/platform-browser/animations';", "import { ${1:ComponentName}Component } from './${2:component-name}.component';", "", "describe('${1:ComponentName}Component', () => {", "  let component: ${1:ComponentName}Component;", "  let fixture: ComponentFixture<${1:ComponentName}Component>;", "", "  const setupTestBed = () => {", "    TestBed.configureTestingModule({", "      imports: [${1:ComponentName}Component, NoopAnimationsModule]", "    });", "  };", "", "  const createComponent = () => {", "    fixture = TestBed.createComponent(${1:ComponentName}Component);", "    component = fixture.componentInstance;", "    fixture.detectChanges();", "  };", "", "  beforeEach(async () => {", "    setupTestBed();", "    await TestBed.compileComponents();", "    createComponent();", "  });", "", "  it('should create', () => {", "    expect(component).toBeTruthy();", "  });", "", "  it('should have initial state', () => {", "    // <PERSON><PERSON>nge", "    const expectedInitialState = {", "      isLoading: false,", "      error: null,", "      data: []", "    };", "", "    // Act & Assert", "    expect(component.isLoading()).toBe(expectedInitialState.isLoading);", "    expect(component.error()).toBe(expectedInitialState.error);", "    expect(component.data()).toEqual(expectedInitialState.data);", "  });", "", "  $0", "});"], "description": "Create a modern Angular test with proper setup and patterns"}, "Angular Route (Standalone)": {"prefix": "ng-route", "body": ["import { Routes } from '@angular/router';", "import { ${1:ComponentName}Component } from './${2:component-name}.component';", "", "export const ${3:ROUTES}: Routes = [", "  {", "    path: '${4:path}',", "    component: ${1:ComponentName}Component,", "    title: '${5:Page Title}',", "    data: {", "      breadcrumb: '${5:Page Title}'", "    }", "  },", "  {", "    path: '${6:child-path}',", "    loadComponent: () => import('./${7:child-component}.component').then(m => m.${8:ChildComponent}Component),", "    title: '${9:Child Page Title}'", "  },", "  $0", "];"], "description": "Create Angular route configuration with lazy loading"}, "Angular Pipe (Standalone)": {"prefix": "ng-pipe", "body": ["import { Pipe, PipeTransform } from '@angular/core';", "", "/**", " * Pipe to ${1:description}", " */", "@Pipe({", "  name: '${2:pipeName}',", "  standalone: true,", "  pure: true", "})", "export class ${3:PipeName}Pipe implements PipeTransform {", "  transform(value: ${4:InputType}, ...args: ${5:ArgsType}[]): ${6:OutputType} {", "    if (!value) return ${7:defaultValue};", "    $0", "    return value;", "  }", "}"], "description": "Create a standalone Angular pipe with proper typing"}, "Angular Directive (Standalone)": {"prefix": "ng-directive", "body": ["import { Directive, ElementRef, inject, OnInit, OnDestroy } from '@angular/core';", "", "/**", " * Directive to ${1:description}", " */", "@Directive({", "  selector: '[${2:directiveName}]',", "  standalone: true", "})", "export class ${3:DirectiveName}Directive implements OnInit, OnDestroy {", "  private readonly elementRef = inject(ElementRef);", "  private readonly element = this.elementRef.nativeElement;", "", "  ngOnInit(): void {", "    $0", "  }", "", "  ngOnDestroy(): void {", "    // Cleanup if needed", "  }", "}"], "description": "Create a standalone Angular directive with lifecycle hooks"}, "Angular Interface": {"prefix": "ng-interface", "body": ["/**", " * Interface for ${1:description}", " */", "export interface ${2:InterfaceName} {", "  id: number;", "  $0", "  createdAt: Date;", "  updatedAt: Date;", "}"], "description": "Create a TypeScript interface with common fields"}, "Angular Enum": {"prefix": "ng-enum", "body": ["/**", " * Enum for ${1:description}", " */", "export enum ${2:EnumName} {", "  ${3:VALUE_ONE} = '${4:value-one}',", "  ${5:VALUE_TWO} = '${6:value-two}',", "  $0", "}"], "description": "Create a TypeScript enum with string values"}, "Angular Use Case": {"prefix": "ng-usecase", "body": ["import { Injectable, inject } from '@angular/core';", "import { Observable, of, catchError, tap } from 'rxjs';", "", "interface ${1:UseCaseName}Request {", "  $0", "}", "", "interface ${1:UseCaseName}Response {", "  success: boolean;", "  data?: any;", "  error?: string;", "}", "", "/**", " * Use case for ${2:description}", " * Follows clean architecture principles", " */", "@Injectable({ providedIn: 'root' })", "export class ${1:UseCaseName}UseCase {", "  // Inject dependencies", "  private readonly ${3:serviceName} = inject(${4:ServiceName}Service);", "", "  /**", "   * Executes the use case", "   * @param request - The request parameters", "   * @returns Observable with the result", "   */", "  execute(request: ${1:UseCaseName}Request): Observable<${1:UseCaseName}Response> {", "    return this.${3:serviceName}.${5:methodName}(request).pipe(", "      tap(response => {", "        console.log('${1:UseCaseName} executed successfully:', response);", "      }),", "      catchError(error => {", "        console.error('Error executing ${1:UseCaseName}:', error);", "        return of({", "          success: false,", "          error: error?.message || 'An error occurred'", "        });", "      })", "    );", "  }", "}"], "description": "Create a use case following clean architecture principles"}, "Angular Form (Reactive)": {"prefix": "ng-form-reactive", "body": ["import { Component, inject, signal, effect } from '@angular/core';", "import { CommonModule } from '@angular/common';", "import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';", "", "interface ${1:FormName}Form {", "  ${2:fieldName}: string;", "  $0", "}", "", "@Component({", "  selector: 'app-${3:form-name}',", "  standalone: true,", "  imports: [CommonModule, ReactiveFormsModule],", "  template: `", "    <form [formGroup]=\"form\" (ngSubmit)=\"onSubmit()\" class=\"space-y-4\">", "      <div class=\"form-group\">", "        <label for=\"${2:fieldName}\" class=\"block text-sm font-medium\">", "          ${4:Field Label}", "        </label>", "        <input", "          id=\"${2:fieldName}\"", "          type=\"${5:text}\"", "          formControlName=\"${2:fieldName}\"", "          class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm\"", "          [class.border-red-500]=\"isFieldInvalid('${2:fieldName}')\"", "        />", "        @if (isFieldInvalid('${2:fieldName}')) {", "          <p class=\"mt-1 text-sm text-red-600\">", "            {{ getFieldError('${2:fieldName}') }}", "          </p>", "        }", "      </div>", "      ", "      <button", "        type=\"submit\"", "        [disabled]=\"form.invalid || isSubmitting()\"", "        class=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50\"", "      >", "        {{ isSubmitting() ? 'Submitting...' : 'Submit' }}", "      </button>", "    </form>", "  `,", "  styles: [``]", "})", "export class ${6:FormName}Component {", "  private readonly fb = inject(FormBuilder);", "", "  // Form state", "  readonly form: FormGroup<${1:FormName}Form> = this.fb.group({", "    ${2:fieldName}: ['', [Validators.required, Validators.minLength(3)]],", "  });", "", "  // Component state", "  private readonly isSubmitting = signal(false);", "", "  constructor() {", "    this.initializeForm();", "  }", "", "  private initializeForm(): void {", "    effect(() => {", "      console.log('Form validity:', this.form.valid);", "    });", "  }", "", "  onSubmit(): void {", "    if (this.form.valid) {", "      this.isSubmitting.set(true);", "      console.log('Form submitted:', this.form.value);", "      // Handle form submission", "      this.isSubmitting.set(false);", "    }", "  }", "", "  isFieldInvalid(fieldName: keyof ${1:FormName}Form): boolean {", "    const control = this.form.get(fieldName);", "    return !!(control?.invalid && control?.touched);", "  }", "", "  getFieldError(fieldName: keyof ${1:FormName}Form): string {", "    const control = this.form.get(fieldName);", "    if (control?.errors?.['required']) return 'This field is required';", "    if (control?.errors?.['minlength']) return 'Minimum length is 3 characters';", "    return 'Invalid field';", "  }", "}"], "description": "Create a reactive form with proper validation and error handling"}}