# Merchant Offline POS UI

### 🏠 [Homepage](https://posui.aplazo.mx)

## Overview

Merchant Offline POS UI is an Angular-based web application for Aplazo merchants. This README provides instructions for setting up and running the project in different environments.

## Prerequisites

- `node >= 22.12.0`
- `angular 17.3.0`
- `docker >= latest` (optional, for containerized development)

## Getting Started

### 1. Environment Setup

#### Setting up the .env file

The application requires environment variables to function properly. Create a `.env` file in the project root:

```sh
# Copy the example file (if available)
cp .env.example .env

# Or create a new .env file with required variables
```

Required environment variables:

| Variable                          | Description                                  | Example                                              |
| --------------------------------- | -------------------------------------------- | ---------------------------------------------------- |
| `AUTH_TOKEN`                      | Token for accessing private npm packages     | `NpmToken.xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`      |
| `ENV`                             | Environment target (`dev`, `stg`, or `prod`) | `stg`                                                |
| `DEV_ENV`                         | Development environment                      | `dev`                                                |
| `NODE_ENV`                        | Node environment                             | `stage`                                              |
| `NG_APP_PRODUCTION`               | Production mode flag                         | `false`                                              |
| `NG_APP_API_URL`                  | Backend API URL                              | `https://api.example.com/`                           |
| `NG_APP_API_MICROSERVICE_URL`     | Microservice API URL                         | `https://merchantdash.example.com/`                  |
| `NG_APP_MERCHANT_ACCESS_BASE_URL` | Merchant access API URL                      | `https://merchant-acs.example.com/api`               |
| `NG_APP_LANDING_URL`              | Landing page URL                             | `https://www.example.com`                            |
| `NG_APP_CUSTOMER_LOGIN_URL`       | Customer login URL                           | `https://customer.example.com/login/credentials`     |
| `NG_APP_CUSTOMER_REGISTER_URL`    | Customer registration URL                    | `https://customer.example.com/login/credentials`     |
| `NG_APP_POSUI_URL`                | POS UI URL                                   | `https://posui.example.com`                          |
| `NG_APP_POSUI_REGISTER_URL`       | POS registration URL                         | `https://aliados.example.com`                        |
| `NG_APP_POS_API_URL`              | POS API URL                                  | `https://pos.example.com`                            |
| `NG_APP_MERCHANT_LOGIN_URL`       | Merchant login URL                           | `https://merchantui.example.com/authorization/login` |
| `NG_APP_MERCHANT_REGISTER_URL`    | Merchant registration URL                    | `https://aliados.example.com`                        |
| `NG_APP_MERCHANT_PATH`            | Merchant path                                | `merchant`                                           |
| `NG_APP_I18N_URL`                 | Internationalization service URL             | `https://example.com/i18n/merchants-dashboard/dev/`  |
| `NG_APP_FLAGS_AUTHORIZATION_KEY`  | Feature flags authorization key              | `client-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`        |
| `NG_APP_FLAGS_ENV`                | Feature flags environment                    | `staging`                                            |
| `NG_APP_GTM_ID`                   | Google Tag Manager ID                        | `GTM-XXXXXX`                                         |
| `NG_APP_WEBCHAT_API_KEY`          | Web chat API key                             | `xxxxx.xxxxxxxxxxxxxxxxxxxxxxxxxxxxx`                |
| `NG_APP_WEBCHAT_BRAND_ID`         | Web chat brand ID                            | `xxxxxxxxxxxxxxxxxxxxxxxx`                           |
| `NG_APP_DATADOG_APPLICATION_ID`   | Datadog application ID                       | `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`               |
| `NG_APP_DATADOG_CLIENT_TOKEN`     | Datadog client token                         | `pubxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`                |
| `NG_APP_DATADOG_ENV`              | Datadog environment                          | `stage`                                              |
| `NG_APP_DATADOG_SERVICE`          | Datadog service name                         | `merchant-dashboard`                                 |

#### Generating AUTH_TOKEN for Nexus Registry

To access private packages from the Nexus registry, you need to generate an AUTH_TOKEN:

1. Run the following command in your terminal:

   ```sh
   npm login --registry=https://nexus.aplazo.dev/repository/npm-group-aplazo/
   ```

2. After successful login, open your user's `.npmrc` file and copy the authToken:

   ```sh
   # For macOS/Linux
   cat ~/.npmrc

   # Example output:
   //nexus.example.com/repository/npm-group/:_authToken=NpmToken.xxxxxxxx
   ```

3. Add the token to your shell configuration file or directly to the environment:

   ```sh
   # For Bash/Zsh
   export AUTH_TOKEN=your-copied-token
   ```

### 2. Development Options

You can run the application in different ways:

#### Option 1: Production-like Environment (No HMR)

To test the application in a production-like environment:

```sh
# Build and run the container
docker-compose up posui_local --build

# For Linux users
# docker compose up posui_local --build
```

The application will be available at http://localhost:4200 without hot module replacement.

#### Option 2: Local Development with Docker (Recommended)

This method provides a consistent development environment with hot reloading:

```sh
# Build and run the development container
docker-compose up posui_local_dev --build

# For Linux users
# docker compose up posui_local_dev --build
```

The application will be available at http://localhost:4200.

##### Development with Dev Containers (VS Code)

For VS Code users, you can leverage Dev Containers for an enhanced development experience:

1. Install the "Dev Containers" extension in VS Code
2. Start the container with `docker-compose up posui_local_dev --build`
3. Open the command palette (Ctrl+Shift+P)
4. Search for "Dev Containers: Attach to Running Container" ![Dev Containers](./docs/dev-containers.png)
5. Select the correct container
6. Reinstall the recommended extensions (once only)

#### Option 3: Local Development with npm

If you prefer not to use Docker:

```sh
# Set the AUTH_TOKEN environment variable
export AUTH_TOKEN=<token>

# Install dependencies
npm install

# Start the development server
node --run dev:dev
```

The application will be available at http://localhost:4200.

### 3. Running Tests

#### E2E Tests with Docker

```sh
# Build and run the test container
docker-compose up posui_local_test --build

# For Linux users
# docker compose up posui_local_test --build
```

#### E2E Tests with npm

```sh
# Set the AUTH_TOKEN environment variable
export AUTH_TOKEN=<token>

# Install dependencies
npm install

# Install Playwright dependencies (first time only)
npx playwright install

# Run E2E tests
node --run e2e
```

## Project Structure

The project follows standard Angular architecture with modern patterns:

- `src/` - Application source code
- `Dockerfile` - Production build configuration
- `Dockerfile.dev` - Development environment configuration
- `Dockerfile.test` - Test environment configuration
- `docker-compose.yml` - Container orchestration configuration

## CI/CD Pipeline

The project uses Jenkins for CI/CD with the following pipeline:

1. **Prepare Environment**: Sets up the environment and retrieves secrets
2. **Unit Tests**: Runs unit tests and generates coverage reports
3. **SonarQube Analysis**: Performs static code analysis
4. **Quality Gate Check**: Ensures code quality standards are met
5. **Build Image**: Builds Docker image for deployment
6. **Push Image**: Pushes Docker image to ECR
7. **Deploy**: Deploys the application using Pulumi

## Author

👤 **Aplazo**

- Website: https://aplazo.mx
- Github: [@aplazo](https://github.com/aplazo)

## Show your support

Give a ⭐️ if this project helped you!

---

_This README was generated with ❤️ by [readme-md-generator](https://github.com/kefranabg/readme-md-generator)_
