import { NgIf } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  ViewEncapsulation,
} from '@angular/core';
import { AplazoCardComponent } from '@aplazo/shared-ui/card';
import { IMediaContentUIDto } from '../../core/domain/notification';

@Component({
  standalone: true,
  selector: 'aplazo-media-card-content-thumbnail',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, AplazoCardComponent],
  template: `
    <aplz-ui-card appearance="flat" *ngIf="media">
      <article
        class="grid grid-cols-[min(90vw,_150px)_2fr_40px] p-3 w-full gap-x-3 items-center">
        <div
          class="flex items-center justify-center w-full rounded-lg overflow-hidden">
          <img
            [src]="media.urlThumbnail"
            class="max-w-full object-center"
            alt="thumbnail for apla<PERSON>'s video content" />
        </div>

        <div class="h-full">
          <p class="text-sm font-medium line-clamp-2">
            {{ media.title }}
          </p>

          <p class="text-sm font-normal text-dark-secondary line-clamp-3">
            {{ media.description }}
          </p>
        </div>

        <div class="media-content-card__actions">
          <ng-content></ng-content>
        </div>
      </article>
    </aplz-ui-card>
  `,
})
export class AplazoMediaCardThumbnailComponent {
  @Input()
  public media: IMediaContentUIDto;
}
