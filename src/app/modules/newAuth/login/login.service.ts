import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';

import { catchError, map, Observable, of, throwError } from 'rxjs';

import {
  POS_ENVIRONMENT_CORE,
  PosEnvironmentCoreType,
} from '../../../../app-core/domain/environments';
import { JWTToken } from '../../../core/domain/access-token';
import { LoginRepository } from './domain/login.repository';
import { CredentialsV2 } from 'src/app/core/domain/credentialsV2.interface';
import { Credentials } from 'src/app/core/domain/credentials.interface';

@Injectable()
export class LoginService implements LoginRepository {
  constructor(
    private http: HttpClient,
    @Inject(POS_ENVIRONMENT_CORE)
    private environment: PosEnvironmentCoreType
  ) {}

  public execute(credentials: Credentials): Observable<JWTToken> {
    return this.http
      .post<any>(`${this.environment.authApiUrl}/auth/login`, {
        login: credentials.username,
        password: credentials.password,
        username: credentials.username,
      })
      .pipe(
        map(response => {
          if (Object.prototype.hasOwnProperty.call(response, 'content')) {
            return response.content.token;
          }

          return response?.Authorization ?? '';
        }),
        map<any, string>(authToken => authToken.replace('Bearer ', '')),
        catchError(error => throwError(() => error))
      );
  }

  public legacyByCredentials(credentials: Credentials): Observable<JWTToken> {
    return this.http
      .post<{ Authorization?: string }>(`${this.environment.apiUrl}login`, {
        login: credentials.username,
        password: credentials.password,
      })
      .pipe(
        map(response => response?.Authorization ?? ''),
        map<any, string>(authToken => authToken.replace('Bearer ', '')),
        catchError(error => throwError(() => error))
      );
  }

  getMerchantDetails(token: string): Observable<{ name: string }> {
    const headers = new HttpHeaders({ Authorization: `Bearer ${token}` });

    return this.http
      .get<any>(
        `${this.environment.posApiUrl}/api/merchant_configs/merchant-details`,
        { headers }
      )
      .pipe(
        map(res => {
          if (
            Object.prototype.hasOwnProperty.call(res, 'content') &&
            Object.prototype.hasOwnProperty.call(res, 'code')
          ) {
            return res.content;
          }

          return res;
        }),
        catchError(() => {
          return of({ name: '' } as { name: string });
        })
      );
  }
}
