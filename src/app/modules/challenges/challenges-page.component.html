<main *ngIf="challenges$ | async as challenges">
  <div class="p-6" *ngIf="challenges.length > 0; else empty">
    <h1 class="text-lg mb-3">Tus retos</h1>

    <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
      <aplazo-challenge-component
        *ngFor="let challenge of challenges"
        [challenge]="challenge"></aplazo-challenge-component>
    </div>
  </div>

  <ng-template #empty>
    <div class="p-6">
      <img
        src="https://cdn.aplazo.mx/merchant-dash-assets/challenges-empty.svg"
        alt="No hay retos para mostrar"
        class="mx-auto mb-5" />

      <h1 class="font-bold text-xl mb-3">No hay retos para mostrar</h1>

      <p>
        Mantente pendiente de nuestras actualizaciones. ¡Pronto habrá nuevos
        retos y premios esperándote!
      </p>
    </div>
  </ng-template>
</main>
