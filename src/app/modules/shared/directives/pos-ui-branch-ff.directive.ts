/* eslint-disable @angular-eslint/no-input-rename */
import {
  Directive,
  Input,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewContainerRef,
  inject,
} from '@angular/core';
import {
  MonoTypeOperatorFunction,
  Subject,
  pipe,
  takeUntil,
  tap,
  withLatestFrom,
} from 'rxjs';
import {
  IBranchFeatureFlags,
  StoreService,
} from '../../../core/application/services/store.service';

class FeatureFlagsContext {
  elseTemplate: string | boolean = false;
  config: any = null;
}

export const BRANCH_FF_NAMES = [
  'isSellAgentTrackEnable',
  'isSupportChatEnable',
  'isOrderRefundEnable',
  'isTodayReportEnable',
] as const;

export type ValidPosUIBranchFFNames = (typeof BRANCH_FF_NAMES)[number];

@Directive({
  selector: '[aplazoPosUiBranchFf]',
  standalone: true,
})
export class PosUiBranchFfDirective implements OnInit, OnDestroy {
  readonly #service = inject(StoreService);
  #context = new FeatureFlagsContext();
  #templateRef: TemplateRef<any> = inject(TemplateRef);
  #viewContainer = inject(ViewContainerRef);
  #destroy$: Subject<void> = new Subject<void>();
  #name$ = new Subject<ValidPosUIBranchFFNames | null>();

  @Input('aplazoPosUiBranchFf')
  set featureName(value: ValidPosUIBranchFFNames | null) {
    this.#name$.next(value ?? null);
  }

  @Input('elseTemplate')
  elseStatement: TemplateRef<FeatureFlagsContext> | null = null;

  static ngTemplateContextGuard(
    dir: PosUiBranchFfDirective,
    ctx: unknown
  ): ctx is FeatureFlagsContext {
    return true;
  }

  ngOnInit(): void {
    this.#name$
      .pipe(
        withLatestFrom(this.#service.selectedBranchFeatureFlags$),
        this.render(),
        takeUntil(this.#destroy$)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.#destroy$.next();
    this.#destroy$.complete();
  }

  private render(): MonoTypeOperatorFunction<
    [ValidPosUIBranchFFNames | null, IBranchFeatureFlags | null]
  > {
    return pipe(
      tap(([nameOrNull, ffOrNull]) => {
        this.#viewContainer.clear();

        if (!nameOrNull || !ffOrNull) {
          this.renderElse();
          return;
        }

        const config = ffOrNull[nameOrNull];

        if (!config) {
          this.renderElse();
        }

        this.#viewContainer.createEmbeddedView(
          this.#templateRef,
          this.#context
        );
      })
    );
  }

  private renderElse(): void {
    if (this.elseStatement) {
      this.#viewContainer.createEmbeddedView(this.elseStatement);
    }
  }
}
