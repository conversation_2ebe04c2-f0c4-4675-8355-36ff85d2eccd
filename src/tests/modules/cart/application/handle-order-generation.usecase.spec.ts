import { TestBed } from '@angular/core/testing';
import { I18NService, I18NTranslator } from '@aplazo/i18n';
import { RedirectionService } from '@aplazo/merchant/shared';
import { provideRedirecterTesting } from '@aplazo/merchant/shared-testing';
import { DialogService } from '@ngneat/dialog';
import { isEmpty, lastValueFrom, of, take } from 'rxjs';
import { StoreService } from '../../../../app/core/application/services/store.service';
import {
  POS_APP_ROUTES,
  ROUTE_CONFIG,
} from '../../../../app/core/domain/config/app-routes.core';
import { Branch } from '../../../../app/core/domain/entities';
import { FinishOrderUseCase } from '../../../../app/modules/cart-v1/application/finish-order.usecase';
import { HandleOrderGenerationUseCase } from '../../../../app/modules/cart-v1/application/handle-order-generation.usecase';

const setup = (
  args: {
    branch: Partial<Branch> | null;
    merchantId: number | null;
    dialogResult:
      | {
          price: number;
          agentId: number;
        }
      | undefined;
  } = {
    branch: null,
    merchantId: null,
    dialogResult: undefined,
  }
) => {
  TestBed.configureTestingModule({
    providers: [
      HandleOrderGenerationUseCase,
      provideRedirecterTesting(),
      {
        provide: FinishOrderUseCase,
        useValue: jasmine.createSpyObj('FinishOrderUseCase', ['execute']),
      },
      {
        provide: DialogService,
        useValue: {
          open: () => ({
            afterClosed$: of(args.dialogResult),
          }),
        },
      },
      {
        provide: StoreService,
        useValue: {
          selectedBranch$: of(args.branch),
          getMerchantId$: () => of(args.merchantId),
        },
      },
      {
        provide: POS_APP_ROUTES,
        useValue: ROUTE_CONFIG,
      },
      {
        provide: I18NService,
        useValue: jasmine.createSpyObj('I18NService', [
          'getTranslateObjectByKey',
        ]),
      },
    ],
  });

  const usecase = TestBed.inject(HandleOrderGenerationUseCase);
  const finishUsecase = TestBed.inject(
    FinishOrderUseCase
  ) as jasmine.SpyObj<FinishOrderUseCase>;
  const internalRedirectionSpy = spyOn(
    TestBed.inject(RedirectionService),
    'internalNavigation'
  ).and.callThrough();
  const dialogSpy = spyOn(
    TestBed.inject(DialogService),
    'open'
  ).and.callThrough();
  const i18n = TestBed.inject(I18NService) as jasmine.SpyObj<I18NTranslator>;

  return {
    usecase,
    finishUsecase,
    internalRedirectionSpy,
    dialogSpy,
    i18n,
  };
};

describe('HandleOrderGenerationUseCase', () => {
  const successConfig = {
    merchantId: 199,
    branch: { id: 456 },
    dialogResult: {
      price: 100,
      agentId: 0,
    },
  };

  it('should be created', () => {
    const { usecase } = setup();

    expect(usecase).toBeTruthy();
    expect(usecase).toBeInstanceOf(HandleOrderGenerationUseCase);
  });

  it('should call internalNavigation when no config are retrieved from i18n Service (default behavior)', async () => {
    const { usecase, internalRedirectionSpy, dialogSpy, i18n } =
      setup(successConfig);

    i18n.getTranslateObjectByKey.and.returnValue(of(null) as any);

    const finishWithoutStream = await lastValueFrom(
      usecase.execute().pipe(take(1), isEmpty())
    );

    expect(finishWithoutStream)
      .withContext('Finish usecase without stream')
      .toBeTrue();

    expect(internalRedirectionSpy)
      .withContext('should call internalNavigation instead of dialogSpy.open')
      .toHaveBeenCalledTimes(1);
    expect(dialogSpy).toHaveBeenCalledTimes(0);
  });

  it('should call finishOrderUseCase when the flag is enabled', async () => {
    const { usecase, finishUsecase, dialogSpy, internalRedirectionSpy, i18n } =
      setup(successConfig);

    i18n.getTranslateObjectByKey.and.returnValue(of({ 199: [456] }) as any);
    finishUsecase.execute.and.returnValue(of({} as any));

    const result = await lastValueFrom(usecase.execute().pipe(take(1)));

    expect(finishUsecase.execute)
      .withContext('should call finishOrderUseCase instead of dialogSpy.open')
      .toHaveBeenCalledTimes(1);
    expect(dialogSpy)
      .withContext('should call dialogSpy.open')
      .toHaveBeenCalledTimes(1);
    expect(result).withContext('should return an object').toBeTruthy();
    expect(internalRedirectionSpy)
      .withContext('should not call internalNavigation')
      .toHaveBeenCalledTimes(0);
  });

  it('should finish without stream when the flag is enabled and dialog result is undefined', async () => {
    const { i18n, usecase, finishUsecase, dialogSpy, internalRedirectionSpy } =
      setup({
        ...successConfig,
        dialogResult: undefined,
      });
    i18n.getTranslateObjectByKey.and.returnValue(of({ 199: [456] }) as any);

    const finishWithoutStream = await lastValueFrom(
      usecase.execute().pipe(take(1), isEmpty())
    );

    expect(finishWithoutStream)
      .withContext('should finish without stream')
      .toBeTruthy();
    expect(dialogSpy)
      .withContext('should call dialogSpy.open')
      .toHaveBeenCalledTimes(1);
    expect(internalRedirectionSpy)
      .withContext('should not call internalNavigation')
      .toHaveBeenCalledTimes(0);
    expect(finishUsecase.execute)
      .withContext('should not call finishOrderUseCase')
      .toHaveBeenCalledTimes(0);
  });

  it('should finish without stream when the flag is enabled and dialog result has not a price', async () => {
    const { i18n, usecase, finishUsecase, dialogSpy, internalRedirectionSpy } =
      setup({
        ...successConfig,
        dialogResult: {
          // @ts-expect-error: price is not defined
          price: undefined,
          agentId: 0,
        },
      });

    i18n.getTranslateObjectByKey.and.returnValue(of({ 199: [456] }) as any);
    const finishWithoutStream = await lastValueFrom(
      usecase.execute().pipe(take(1), isEmpty())
    );

    expect(finishWithoutStream)
      .withContext('should finish without stream')
      .toBeTruthy();
    expect(dialogSpy)
      .withContext('should call dialogSpy.open')
      .toHaveBeenCalledTimes(1);
    expect(internalRedirectionSpy)
      .withContext('should not call internalNavigation')
      .toHaveBeenCalledTimes(0);
    expect(finishUsecase.execute)
      .withContext('should not call finishOrderUseCase')
      .toHaveBeenCalledTimes(0);
  });

  it('should finish without stream when the flag is enabled and dialog result has a negative price', async () => {
    const { i18n, usecase, finishUsecase, dialogSpy, internalRedirectionSpy } =
      setup({
        ...successConfig,
        dialogResult: {
          price: -1,
          agentId: 0,
        },
      });

    i18n.getTranslateObjectByKey.and.returnValue(of({ 199: [456] }) as any);
    const finishWithoutStream = await lastValueFrom(
      usecase.execute().pipe(take(1), isEmpty())
    );

    expect(finishWithoutStream)
      .withContext('should finish without stream')
      .toBeTruthy();
    expect(dialogSpy)
      .withContext('should call dialogSpy.open')
      .toHaveBeenCalledTimes(1);
    expect(internalRedirectionSpy)
      .withContext('should not call internalNavigation')
      .toHaveBeenCalledTimes(0);
    expect(finishUsecase.execute)
      .withContext('should not call finishOrderUseCase')
      .toHaveBeenCalledTimes(0);
  });

  it('should finish without stream when the flag is enabled and dialog result has not agentId', async () => {
    const { i18n, usecase, finishUsecase, dialogSpy, internalRedirectionSpy } =
      setup({
        ...successConfig,
        dialogResult: {
          price: 100,
          // @ts-expect-error: price is not defined
          agentId: undefined,
        },
      });

    i18n.getTranslateObjectByKey.and.returnValue(of({ 199: [456] }) as any);
    const finishWithoutStream = await lastValueFrom(
      usecase.execute().pipe(take(1), isEmpty())
    );

    expect(finishWithoutStream)
      .withContext('should finish without stream')
      .toBeTruthy();
    expect(dialogSpy)
      .withContext('should call dialogSpy.open')
      .toHaveBeenCalledTimes(1);
    expect(internalRedirectionSpy)
      .withContext('should not call internalNavigation')
      .toHaveBeenCalledTimes(0);
    expect(finishUsecase.execute)
      .withContext('should not call finishOrderUseCase')
      .toHaveBeenCalledTimes(0);
  });
});
