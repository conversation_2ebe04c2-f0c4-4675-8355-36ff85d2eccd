import { TestBed } from '@angular/core/testing';
import { Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';
import { StatsigService } from '@statsig/angular-bindings';
import { ROUTE_CONFIG } from '../domain/config/app-routes.core';
import { isUnderMaintenanceGuard } from './maintenance.guard';

describe('isUnderMaintenanceGuard', () => {
  let statsigServiceMock: jasmine.SpyObj<StatsigService>;
  let router: Router;
  let stateSnapshotMock: RouterStateSnapshot;

  beforeEach(() => {
    statsigServiceMock = jasmine.createSpyObj('StatsigService', ['checkGate']);

    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      providers: [{ provide: StatsigService, useValue: statsigServiceMock }],
    });

    router = TestBed.inject(Router);
    spyOn(router, 'parseUrl').and.callThrough();
  });

  it('should redirect to maintenance page when maintenance is enabled and current route is not maintenance', async () => {
    // Arrange
    stateSnapshotMock = { url: '/some-route' } as RouterStateSnapshot;
    statsigServiceMock.checkGate.and.returnValue(true);

    // Act
    const result = await TestBed.runInInjectionContext(() =>
      isUnderMaintenanceGuard(null as any, stateSnapshotMock)
    );

    // Assert
    expect(statsigServiceMock.checkGate).toHaveBeenCalledWith(
      'b2b_front_maintenance_screen_enabled'
    );
    expect(router.parseUrl).toHaveBeenCalledWith(
      `/${ROUTE_CONFIG.unavailable}`
    );
    expect(result).toBeInstanceOf(UrlTree);
  });

  it('should redirect to authentication when maintenance is disabled but current route is maintenance', async () => {
    // Arrange
    stateSnapshotMock = {
      url: `/${ROUTE_CONFIG.unavailable}`,
    } as RouterStateSnapshot;
    statsigServiceMock.checkGate.and.returnValue(false);

    // Act
    const result = await TestBed.runInInjectionContext(() =>
      isUnderMaintenanceGuard(null as any, stateSnapshotMock)
    );

    // Assert
    expect(statsigServiceMock.checkGate).toHaveBeenCalledWith(
      'b2b_front_maintenance_screen_enabled'
    );
    expect(router.parseUrl).toHaveBeenCalledWith(
      `/${ROUTE_CONFIG.authentication}`
    );
    expect(result).toBeInstanceOf(UrlTree);
  });

  it('should allow navigation when maintenance is enabled and current route is maintenance', async () => {
    // Arrange
    stateSnapshotMock = {
      url: `/${ROUTE_CONFIG.unavailable}`,
    } as RouterStateSnapshot;
    statsigServiceMock.checkGate.and.returnValue(true);

    // Act
    const result = await TestBed.runInInjectionContext(() =>
      isUnderMaintenanceGuard(null as any, stateSnapshotMock)
    );

    // Assert
    expect(statsigServiceMock.checkGate).toHaveBeenCalledWith(
      'b2b_front_maintenance_screen_enabled'
    );
    expect(router.parseUrl).not.toHaveBeenCalled();
    expect(result).toBeTrue();
  });

  it('should allow navigation when maintenance is disabled and current route is not maintenance', async () => {
    // Arrange
    stateSnapshotMock = { url: '/some-other-route' } as RouterStateSnapshot;
    statsigServiceMock.checkGate.and.returnValue(false);

    // Act
    const result = await TestBed.runInInjectionContext(() =>
      isUnderMaintenanceGuard(null as any, stateSnapshotMock)
    );

    // Assert
    expect(statsigServiceMock.checkGate).toHaveBeenCalledWith(
      'b2b_front_maintenance_screen_enabled'
    );
    expect(router.parseUrl).not.toHaveBeenCalled();
    expect(result).toBeTrue();
  });

  it('should handle null flag value as maintenance disabled', async () => {
    // Arrange
    stateSnapshotMock = { url: '/some-route' } as RouterStateSnapshot;
    statsigServiceMock.checkGate.and.returnValue(false);

    // Act
    const result = await TestBed.runInInjectionContext(() =>
      isUnderMaintenanceGuard(null as any, stateSnapshotMock)
    );

    // Assert
    expect(statsigServiceMock.checkGate).toHaveBeenCalledWith(
      'b2b_front_maintenance_screen_enabled'
    );
    expect(router.parseUrl).not.toHaveBeenCalled();
    expect(result).toBeTrue();
  });
});
