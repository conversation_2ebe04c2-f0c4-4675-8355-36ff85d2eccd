import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ShowPopupUseCase } from './application/usecases/show-popup.usecase';
import { PopupStorageService } from './application/services/popup-storage.service';
import { PopupTriggerService } from './application/services/popup-trigger.service';

/**
 * ✅ SIMPLE TEST COMPONENT FOR POPUP FUNCTIONALITY
 * 
 * This component provides a simple interface to test that popups are working
 * correctly after the Batch 4 implementation.
 */
@Component({
  selector: 'app-test-popup-functionality',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="bg-white p-6 rounded-lg shadow-lg max-w-md mx-auto">
      <h2 class="text-xl font-bold mb-4">🧪 Test Popup Functionality</h2>
      
      <div class="space-y-4">
        <!-- Test ShowPopupUseCase directly -->
        <button 
          (click)="testShowPopupUseCase()"
          class="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
        >
          🎭 Test ShowPopupUseCase
        </button>
        
        <!-- Test PopupTriggerService -->
        <button 
          (click)="testPopupTriggerService()"
          class="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
        >
          🎯 Test PopupTriggerService
        </button>
        
        <!-- Check Storage Status -->
        <button 
          (click)="checkStorageStatus()"
          class="w-full bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600"
        >
          💾 Check Storage Status
        </button>
        
        <!-- Clear All Data -->
        <button 
          (click)="clearAllData()"
          class="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
        >
          🗑️ Clear All Data
        </button>
      </div>

      <!-- Results Display -->
      <div class="mt-6 p-4 bg-gray-100 rounded">
        <h3 class="font-bold mb-2">📊 Last Result:</h3>
        <pre class="text-sm overflow-auto">{{ lastResult | json }}</pre>
      </div>

      <!-- Instructions -->
      <div class="mt-4 p-4 bg-blue-50 rounded">
        <h3 class="font-bold text-blue-800 mb-2">📋 Instructions:</h3>
        <ol class="text-sm text-blue-700 space-y-1">
          <li>1. Click "Test ShowPopupUseCase" - should show popup with checkbox</li>
          <li>2. Check "No mostrar de nuevo" and close popup</li>
          <li>3. Click test again - should not show popup</li>
          <li>4. Click "Clear All Data" to reset</li>
        </ol>
      </div>
    </div>
  `,
})
export class TestPopupFunctionalityComponent {
  private showPopupUseCase = inject(ShowPopupUseCase);
  private popupStorageService = inject(PopupStorageService);
  private popupTriggerService = inject(PopupTriggerService);

  lastResult: any = null;

  /**
   * Test ShowPopupUseCase directly
   */
  testShowPopupUseCase() {
    console.log('🧪 [TestComponent] Testing ShowPopupUseCase...');
    
    this.showPopupUseCase.execute().subscribe({
      next: (result) => {
        console.log('🧪 [TestComponent] ShowPopupUseCase result:', result);
        this.lastResult = {
          test: 'ShowPopupUseCase',
          success: result.success,
          reason: result.reason || 'N/A',
          popupShown: result.popupShown?.popupId || 'None',
          timestamp: new Date().toISOString(),
        };
      },
      error: (error) => {
        console.error('🧪 [TestComponent] ShowPopupUseCase error:', error);
        this.lastResult = {
          test: 'ShowPopupUseCase',
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        };
      }
    });
  }

  /**
   * Test PopupTriggerService
   */
  testPopupTriggerService() {
    console.log('🧪 [TestComponent] Testing PopupTriggerService...');
    
    this.popupTriggerService.triggerPopupIfNeeded({
      branchId: '123', // Default test branch
    }).subscribe({
      next: (result) => {
        console.log('🧪 [TestComponent] PopupTriggerService result:', result);
        this.lastResult = {
          test: 'PopupTriggerService',
          success: result,
          timestamp: new Date().toISOString(),
        };
      },
      error: (error) => {
        console.error('🧪 [TestComponent] PopupTriggerService error:', error);
        this.lastResult = {
          test: 'PopupTriggerService',
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        };
      }
    });
  }

  /**
   * Check storage status
   */
  checkStorageStatus() {
    console.log('🧪 [TestComponent] Checking storage status...');
    
    const records = this.popupStorageService.getShownRecords();
    const testPopupId = 'test-popup-1';
    const testBranchId = '123';
    
    this.lastResult = {
      test: 'StorageStatus',
      totalRecords: records.length,
      records: records,
      testPopupPermanentlyDismissed: this.popupStorageService.isPopupPermanentlyDismissed(testPopupId, testBranchId),
      testPopupShouldShow: this.popupStorageService.shouldShowPopup(testPopupId, testBranchId),
      timestamp: new Date().toISOString(),
    };
    
    console.log('🧪 [TestComponent] Storage status:', this.lastResult);
  }

  /**
   * Clear all data
   */
  clearAllData() {
    console.log('🧪 [TestComponent] Clearing all data...');
    
    this.popupStorageService.clearAllRecords();
    
    this.lastResult = {
      test: 'ClearData',
      success: true,
      message: 'All popup records cleared',
      timestamp: new Date().toISOString(),
    };
    
    console.log('🧪 [TestComponent] Data cleared');
  }
}

/**
 * USAGE:
 * 
 * Add this component to any page for testing:
 * 
 * <app-test-popup-functionality></app-test-popup-functionality>
 * 
 * Or add to app.component.html temporarily:
 * 
 * <div class="p-4">
 *   <app-test-popup-functionality></app-test-popup-functionality>
 * </div>
 */
