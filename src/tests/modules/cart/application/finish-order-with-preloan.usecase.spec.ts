import { HttpErrorResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { ObservabilityService } from '@aplazo/front-observability';
import { LoaderService } from '@aplazo/merchant/shared';
import {
  provideLoaderTesting,
  provideNotifierTesting,
  provideRedirecterTesting,
  provideUseCaseErrorHandlerTesting,
} from '@aplazo/merchant/shared-testing';
import { DialogService } from '@ngneat/dialog';
import { StatsigService } from '@statsig/angular-bindings';
import { isEmpty, lastValueFrom, of, take } from 'rxjs';
import {
  POS_ENVIRONMENT_CORE,
  posEnvironmentCore,
} from '../../../../app-core/domain/environments';
import { StoreService } from '../../../../app/core/application/services/store.service';
import {
  POS_APP_ROUTES,
  ROUTE_CONFIG,
} from '../../../../app/core/domain/config/app-routes.core';
import { Branch } from '../../../../app/core/domain/entities';
import { IntegrationType } from '../../../../app/core/domain/integration-type';
import { PosOfflineRepository } from '../../../../app/core/domain/repositories/pos-offline.repository';
import { FinishOrderWithPreloanUseCase } from '../../../../app/modules/cart-v1/application/finish-order-with-preloan.usecase';
import branches from '../../../local-branches.db.json';

const disabledFlag = false;

const setup = (args?: {
  branch?: Branch | null;
  integrationType?: IntegrationType;
}) => {
  const config = {
    branch: branches[0],
    integrationType: 'POSUI',
    ...args,
  };

  TestBed.configureTestingModule({
    providers: [
      FinishOrderWithPreloanUseCase,
      provideLoaderTesting(),
      provideNotifierTesting(),
      provideRedirecterTesting(),
      provideUseCaseErrorHandlerTesting(),
      {
        provide: POS_APP_ROUTES,
        useValue: ROUTE_CONFIG,
      },
      {
        provide: POS_ENVIRONMENT_CORE,
        useValue: posEnvironmentCore,
      },
      {
        provide: PosOfflineRepository,
        useValue: jasmine.createSpyObj('PosOfflineRepository', [
          'createLoanWithPreloan',
        ]),
      },
      {
        provide: ObservabilityService,
        useValue: jasmine.createSpyObj('ObservabilityService', [
          'sendCustomLog',
        ]),
      },
      {
        provide: StoreService,
        useValue: {
          selectedBranch$: of(config.branch),
          setSelectedBranch: () => {
            void 0;
          },
          integrationType$: of(config.integrationType),
        },
      },
      {
        provide: DialogService,
        useValue: {
          open: () => {
            return {
              afterClosed$: of(undefined),
            };
          },
        },
      },
      {
        provide: StatsigService,
        useValue: {
          checkGate: () => disabledFlag,
        },
      },
    ],
  });
  const useCase = TestBed.inject(FinishOrderWithPreloanUseCase);
  const loader = TestBed.inject(LoaderService);
  const loaderShowSpy = spyOn(loader, 'show').and.callThrough();
  const loaderHideSpy = spyOn(loader, 'hide').and.callThrough();
  const repository = TestBed.inject(
    PosOfflineRepository
  ) as jasmine.SpyObj<PosOfflineRepository>;
  const store = TestBed.inject(StoreService);
  const setSelectedBranchSpy = spyOn(
    store,
    'setSelectedBranch'
  ).and.callThrough();
  const dialogService = TestBed.inject(DialogService);

  return {
    useCase,
    loaderShowSpy,
    loaderHideSpy,
    repository,
    setSelectedBranchSpy,
    dialogService,
  };
};

describe('FinishOrderWithPreloanUseCase', () => {
  const testRequest = {
    products: [
      {
        productId: '1',
        name: 'Express pilot purchase',
        description: 'POS OFFLine',
        sku: 'AplazoID',
        price: '122',
        quantity: 1,
      },
    ],
    totalPrice: '122',
    sellsAgentId: '0',
    preloanCode: 'ABC123',
  };
  const successfullResponse = {
    id: 35776,
    createdAt: '2024-08-02T18:10:20.957028121Z',
    updatedAt: '2024-08-02T18:10:20.957028121Z',
    DeletedAt: null,
    date: '2024-08-02 18:10:20.953632131 +0000 UTC m=+64332.239903732',
    status: 'created',
    price: 122,
    url: 'https://checkout-offline.aplazo.net/main/034eedd6-3eca-4f22-8096-babaaeea8979',
    loanId: 127166,
    merchantId: 199,
    products: [
      {
        id: 37837,
        createdAt: '2024-08-02T18:10:20.959298219Z',
        updatedAt: '2024-08-02T18:10:20.959298219Z',
        DeletedAt: null,
        quantity: 1,
        description: 'POS OFFLine',
        imageUrl:
          'https://aplazoassets.s3-us-west-2.amazonaws.com/aplazo-logo-png-colores.png',
        price: 122,
        title: 'Express pilot purchase',
        ExternalId: 'AplazoID',
        merchantPosOrderId: 35776,
      },
    ],
    branchId: 642,
    sellsAgentId: 0,
  };

  it('should be created', () => {
    const { useCase } = setup();

    expect(useCase).toBeTruthy();
    expect(useCase).toBeInstanceOf(FinishOrderWithPreloanUseCase);
  });

  it('should have a maxLoanAmountForRestrictedIntegrationType property and is equal to 6000', () => {
    const { useCase } = setup();

    expect(useCase.maxLoanAmountForRestrictedIntegrationType)
      .withContext(
        'should have a maxLoanAmountForRestrictedIntegrationType property'
      )
      .toBe(6000);
  });

  it('should show warning and return EMPTY when preloanCode is not provided', async () => {
    const { useCase, loaderHideSpy, loaderShowSpy, repository } = setup();

    const requestWithoutPreloanCode = {
      ...testRequest,
      preloanCode: undefined,
    };
    const isCompleteWithoutStream = await lastValueFrom(
      useCase.execute(requestWithoutPreloanCode).pipe(take(1), isEmpty())
    );

    expect(isCompleteWithoutStream)
      .withContext('should complete with no stream emission')
      .toBeTruthy();
    expect(loaderShowSpy)
      .withContext('should show loader once')
      .toHaveBeenCalledTimes(1);
    expect(loaderHideSpy)
      .withContext('should hide loader once')
      .toHaveBeenCalledTimes(1);
    expect(repository.createLoanWithPreloan).toHaveBeenCalledTimes(0);
  });

  it('should handle error when branch is null', async () => {
    const { useCase, loaderHideSpy, loaderShowSpy, repository } = setup({
      branch: null,
    });

    const isCompleteWithoutStream = await lastValueFrom(
      useCase.execute(testRequest).pipe(take(1), isEmpty())
    );

    expect(isCompleteWithoutStream)
      .withContext('should complete with no stream emission')
      .toBeTruthy();
    expect(loaderShowSpy)
      .withContext('should show loader once')
      .toHaveBeenCalledTimes(1);
    expect(loaderHideSpy)
      .withContext('should hide loader once')
      .toHaveBeenCalledTimes(1);
    expect(repository.createLoanWithPreloan).toHaveBeenCalledTimes(0);
  });

  it('should set shareLinkFlags to a branch when response does not have it set any', async () => {
    const {
      useCase,
      loaderHideSpy,
      loaderShowSpy,
      repository,
      setSelectedBranchSpy,
    } = setup({
      branch: { ...branches[0], shareLinkFlags: null },
    });
    repository.createLoanWithPreloan.and.returnValue(of(successfullResponse));

    await lastValueFrom(useCase.execute(testRequest).pipe(take(1)));

    expect(loaderShowSpy)
      .withContext('should show loader once')
      .toHaveBeenCalledTimes(1);
    expect(loaderHideSpy)
      .withContext('should hide loader once')
      .toHaveBeenCalledTimes(1);
    expect(setSelectedBranchSpy)
      .withContext(
        'only call setSelectedBranch once when shareLinkFlags is null'
      )
      .toHaveBeenCalledTimes(1);
  });

  it('should handle error when total price is greater than max valid amount and is WM_APLAZO integrationType', async () => {
    const { useCase, loaderHideSpy, loaderShowSpy, repository } = setup({
      branch: { ...branches[0] },
      integrationType: 'WM_APLAZO',
    });

    const isCompleteWithoutStream = await lastValueFrom(
      useCase
        .execute({
          ...testRequest,
          totalPrice: `${
            useCase.maxLoanAmountForRestrictedIntegrationType + 1
          }`,
        })
        .pipe(take(1), isEmpty())
    );

    expect(isCompleteWithoutStream)
      .withContext(
        'should complete with no stream emission when total price is greater than max valid amount'
      )
      .toBeTruthy();
    expect(loaderShowSpy)
      .withContext('should show loader once')
      .toHaveBeenCalledTimes(1);
    expect(loaderHideSpy)
      .withContext('should hide loader once')
      .toHaveBeenCalledTimes(1);
    expect(repository.createLoanWithPreloan).toHaveBeenCalledTimes(0);
  });

  it('should not apply validation when total price is greater than max valid amount and integrationType is not WM_APLAZO', async () => {
    const { useCase, loaderHideSpy, loaderShowSpy, repository } = setup({
      branch: { ...branches[0] },
      integrationType: 'API',
    });
    repository.createLoanWithPreloan.and.returnValue(of(successfullResponse));

    const isCompleteWithoutStream = await lastValueFrom(
      useCase
        .execute({
          ...testRequest,
          totalPrice: `${
            useCase.maxLoanAmountForRestrictedIntegrationType + 1
          }`,
        })
        .pipe(take(1), isEmpty())
    );

    expect(isCompleteWithoutStream)
      .withContext(
        'should complete normally when total price is greater than max valid amount and integrationType is not WM_APLAZO'
      )
      .toBeFalse();
    expect(loaderShowSpy)
      .withContext('should show loader once')
      .toHaveBeenCalledTimes(1);
    expect(loaderHideSpy)
      .withContext('should hide loader once')
      .toHaveBeenCalledTimes(1);
    expect(repository.createLoanWithPreloan).toHaveBeenCalledTimes(1);
  });

  it('should complete successfully and call createLoanWithPreloan with correct parameters', async () => {
    const { useCase, loaderHideSpy, loaderShowSpy, repository } = setup({
      branch: { ...branches[0] },
    });
    repository.createLoanWithPreloan.and.returnValue(of(successfullResponse));

    const result = await lastValueFrom(
      useCase.execute(testRequest).pipe(take(1))
    );

    expect(result).withContext('should complete successfully').toBeTruthy();
    expect(loaderShowSpy)
      .withContext('should show loader once')
      .toHaveBeenCalledTimes(1);
    expect(loaderHideSpy)
      .withContext('should hide loader once')
      .toHaveBeenCalledTimes(1);
    expect(repository.createLoanWithPreloan).toHaveBeenCalledTimes(1);
    expect(repository.createLoanWithPreloan.calls.first().args[1])
      .withContext('should pass preloanCode as second argument')
      .toBe(testRequest.preloanCode);
  });

  it('should handle error from repository and show error dialog', async () => {
    const { useCase, loaderHideSpy, loaderShowSpy, repository } = setup({
      branch: { ...branches[0] },
    });

    const errorResponse = new HttpErrorResponse({
      status: 500,
      statusText: 'Internal Server Error',
    });
    repository.createLoanWithPreloan.and.throwError(errorResponse);

    const isCompleteWithoutStream = await lastValueFrom(
      useCase.execute(testRequest).pipe(take(1), isEmpty())
    );

    expect(isCompleteWithoutStream)
      .withContext('should complete with no stream emission')
      .toBeTruthy();
    expect(loaderShowSpy)
      .withContext('should show loader once')
      .toHaveBeenCalledTimes(1);
    expect(loaderHideSpy)
      .withContext('should hide loader once')
      .toHaveBeenCalledTimes(1);
    expect(repository.createLoanWithPreloan).toHaveBeenCalledTimes(1);
  });
});
