import {
  Directive,
  ElementRef,
  Input,
  OnD<PERSON>roy,
  OnInit,
  Renderer2,
} from '@angular/core';
import { interval, Subscription } from 'rxjs';

@Directive({
  standalone: true,
  selector: '[aplazoCountdown]',
})
export class CountdownDirective implements OnInit, OnDestroy {
  @Input() date: string;

  private subscription: Subscription;

  constructor(
    private el: ElementRef,
    private renderer: Renderer2
  ) {}

  ngOnInit() {
    this.subscription = interval(1000).subscribe(() => {
      const now = new Date().getTime();
      const target = new Date(this.date).getTime();

      const diff = target - now;

      if (diff < 0) {
        return;
      }

      const days = Math.floor(diff / (1000 * 60 * 60 * 24));

      if (days <= 1) {
        this.renderer.addClass(this.el.nativeElement, 'text-special-danger');
      } else {
        this.renderer.removeClass(this.el.nativeElement, 'text-special-danger');
      }

      this.el.nativeElement.innerHTML = `Quedan: <span class="font-bold">${days} días</span>`;
    });
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }
}
