import { Injectable, inject } from '@angular/core';
import {
  Observable,
  Subject,
  catchError,
  combineLatestWith,
  map,
  of,
  startWith,
  switchMap,
} from 'rxjs';
import { StoreService } from '../../core/application/services/store.service';
import { PosOfflineRepository } from '../../core/domain/repositories/pos-offline.repository';
import { GetQrImageService } from './application/services/qr-image.service';

@Injectable({ providedIn: 'root' })
export class GetQrImageWithHttpService implements GetQrImageService {
  readonly #service = inject(PosOfflineRepository);
  readonly #store = inject(StoreService);
  readonly #imgSrc$ = new Subject<void>();
  readonly #base64Enc = 'data:image/png;base64, ';
  readonly #noQrImgUrl =
    'https://aplazoassets.s3.us-west-2.amazonaws.com/merchant-dash-assets/loan_lost.png';

  getImageSrc$(
    loanId: number | null
  ): Observable<{ src: string; succeded: boolean; loading: boolean }> {
    return this.#service.getQrImage(String(loanId)).pipe(
      map(img => ({
        src: `${this.#base64Enc}${img}`,
        succeded: true,
        loading: false,
      })),
      catchError(error => {
        console.warn('getQrImage::serviceError >>> ', error);
        return of({
          src: this.#noQrImgUrl,
          succeded: false,
          loading: false,
        });
      }),
      startWith({
        src: '',
        succeded: false,
        loading: true,
      })
    );
  }

  getLastLoanQrImage$(): Observable<{
    src: string;
    succeded: boolean;
  }> {
    return this.#imgSrc$.pipe(
      combineLatestWith(this.#store.selectedBranch$),
      switchMap(([, branch]) => {
        if (!branch || !branch.id) {
          return of({
            src: '',
            succeded: false,
          });
        }

        return this.#service.getLastLoanQrImage(String(branch.id)).pipe(
          map(img => ({
            src: `${this.#base64Enc}${img}`,
            succeded: true,
            loading: false,
          })),
          catchError(error => {
            console.warn('getQrImage::serviceError >>> ', error);
            return of({
              src: this.#noQrImgUrl,
              succeded: false,
            });
          })
        );
      })
    );
  }

  refreshImage(): void {
    this.#imgSrc$.next();
  }
}
