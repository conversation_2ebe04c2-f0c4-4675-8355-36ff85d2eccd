import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { of } from 'rxjs';
import { PopupTriggerService } from 'src/app/modules/shared/popup/application/services/popup-trigger.service';
import { PopupRepository } from 'src/app/modules/shared/popup/infra/repositories/popup.repository';
import { PopupStorageService } from 'src/app/modules/shared/popup/application/services/popup-storage.service';
import { ShowPopupUseCase } from 'src/app/modules/shared/popup/application/usecases/show-popup.usecase';

/**
 * Test de integración para Batch 3 - Verifica que todos los servicios funcionan juntos
 * sin hacer llamadas HTTP reales
 */
describe('Popup Batch 3 Integration', () => {
  let popupTriggerService: PopupTriggerService;
  let popupRepository: jasmine.SpyObj<PopupRepository>;
  let popupStorage: jasmine.SpyObj<PopupStorageService>;
  let showPopupUseCase: jasmine.SpyObj<ShowPopupUseCase>;

  const mockPopups = [
    {
      popupId: 'POPUP-001',
      startAt: '2025-06-23:00:00:00UTC',
      endAt: '2025-07-23:00:00:00UTC',
      priority: 1,
    },
    {
      popupId: 'POPUP-002',
      startAt: '2025-06-23:00:00:00UTC',
      endAt: '2025-07-23:00:00:00UTC',
      priority: 2,
    },
  ];

  beforeEach(() => {
    const popupRepositorySpy = jasmine.createSpyObj('PopupRepository', [
      'getAvailablePopups',
      'getPopupHtml',
    ]);
    const popupStorageSpy = jasmine.createSpyObj('PopupStorageService', [
      'wasPopupShownToday',
      'markPopupAsShown',
      'cleanupOldRecords',
    ]);
    const showPopupUseCaseSpy = jasmine.createSpyObj('ShowPopupUseCase', [
      'execute',
    ]);

    TestBed.configureTestingModule({
      providers: [
        PopupTriggerService,
        { provide: PopupRepository, useValue: popupRepositorySpy },
        { provide: PopupStorageService, useValue: popupStorageSpy },
        { provide: ShowPopupUseCase, useValue: showPopupUseCaseSpy },
      ],
    });

    popupTriggerService = TestBed.inject(PopupTriggerService);
    popupRepository = TestBed.inject(
      PopupRepository
    ) as jasmine.SpyObj<PopupRepository>;
    popupStorage = TestBed.inject(
      PopupStorageService
    ) as jasmine.SpyObj<PopupStorageService>;
    showPopupUseCase = TestBed.inject(
      ShowPopupUseCase
    ) as jasmine.SpyObj<ShowPopupUseCase>;
  });

  it('should integrate all Batch 3 services without making real HTTP calls', fakeAsync(() => {
    // Mock repository to return popups without HTTP call
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(false);
    showPopupUseCase.execute.and.returnValue(
      of({ success: true, popupShown: mockPopups[0] })
    );

    let result = false;
    popupTriggerService.triggerAfterLogin('BRANCH-001').subscribe(value => {
      result = value;
    });
    tick();

    // Verify integration works
    expect(result).toBe(true);
    expect(popupRepository.getAvailablePopups).toHaveBeenCalledWith(
      'BRANCH-001'
    );
    expect(popupStorage.wasPopupShownToday).toHaveBeenCalled();
    expect(showPopupUseCase.execute).toHaveBeenCalled();
  }));

  it('should handle no popups available scenario', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of([]));

    let result = false;
    popupTriggerService
      .triggerAfterPaymentSuccess('BRANCH-001')
      .subscribe(value => {
        result = value;
      });
    tick();

    expect(result).toBe(false);
    expect(showPopupUseCase.execute).not.toHaveBeenCalled();
  }));

  it('should handle all popups already shown scenario', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.returnValue(true);

    let result = false;
    popupTriggerService
      .shouldShowPopup({ branchId: 'BRANCH-001' })
      .subscribe(value => {
        result = value;
      });
    tick();

    expect(result).toBe(false);
  }));

  it('should prioritize popups correctly', fakeAsync(() => {
    popupRepository.getAvailablePopups.and.returnValue(of(mockPopups));
    popupStorage.wasPopupShownToday.and.callFake((popupId: string) => {
      return popupId === 'POPUP-001'; // POPUP-001 ya mostrado, POPUP-002 no
    });

    let result = false;
    popupTriggerService
      .shouldShowPopup({ branchId: 'BRANCH-001' })
      .subscribe(value => {
        result = value;
      });
    tick();

    expect(result).toBe(true);
    // Should check POPUP-002 first (higher priority)
    expect(popupStorage.wasPopupShownToday).toHaveBeenCalledWith(
      'POPUP-002',
      'BRANCH-001',
      jasmine.any(String)
    );
  }));
});
