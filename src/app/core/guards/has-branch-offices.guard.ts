import { Inject, Injectable } from '@angular/core';
import {
  CanActivate,
  Router,
  RouterStateSnapshot,
  UrlTree,
} from '@angular/router';
import {
  Observable,
  OperatorFunction,
  catchError,
  map,
  of,
  pipe,
  take,
  tap,
} from 'rxjs';

import { StoreService } from '../application/services/store.service';
import { POS_APP_ROUTES, RouteConfig } from '../domain/config/app-routes.core';
import { RuntimePosOfflineError } from '../domain/runtime-error';
import { MerchantsService } from '../services/merchants.service';

@Injectable({
  providedIn: 'root',
})
export class HasBranchOfficesGuard implements CanActivate {
  constructor(
    private storeService: StoreService,
    private router: Router,
    private merchantsService: MerchantsService,
    @Inject(POS_APP_ROUTES)
    private appRoutes: RouteConfig
  ) {}

  canActivate(
    _: any,
    state: RouterStateSnapshot
  ): Observable<boolean | UrlTree> {
    if (state.url.includes(this.appRoutes.securedSelectBranch)) {
      return this.merchantsService.getMerchantConfig().pipe(
        take(1),
        tap(config => {
          this.storeService.setMerchantConfig(config);
        }),

        map(
          config => (config?.branches && config.branches.length > 0) ?? false
        ),

        this.hasBranches()
      );
    }

    return this.storeService.hasBranches$.pipe(
      take(1),

      this.hasBranches()
    );
  }

  private hasBranches(): OperatorFunction<boolean, UrlTree | boolean> {
    return pipe(
      map(hasBranches => {
        if (!hasBranches) {
          throw new RuntimePosOfflineError(
            'No hay sucursales creadas',
            'SelectBranch::emptyBranches',
            'has-branches-guard-error'
          );
        }

        return true;
      }),

      catchError((error: unknown) => {
        if (error instanceof RuntimePosOfflineError) {
          console.warn(error.code);

          return of(
            this.router.parseUrl(`/${this.appRoutes.securedNewBranch}`)
          );
        }

        console.error(error);

        throw error;
      })
    );
  }
}
