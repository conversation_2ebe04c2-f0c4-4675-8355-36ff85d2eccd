<article class="px-4 lg:px-10 pt-5 pb-10 bg-dark-background">
  <h2 class="text-xl font-medium mb-3 uppercase">
    {{ (summaryTitleText$ | async)?.label }}
  </h2>

  <ng-container *ngIf="screenSize$ | async as screenSize">
    <aplz-ui-card
      *ngIf="summary$ | async as stats"
      appearance="flat"
      [dark]="true">
      <div class="grid gap-2 grid-cols-1 lg:grid-cols-3 md:grid-cols-2">
        <aplz-ui-stat-card
          *ngFor="let stat of stats; let even = even; let last = last"
          [I18nText]="{ statCardTitle: stat.statCardTitle }"
          [pipeName]="stat.pipeName || null"
          [principalAmount]="stat.value"
          [hasBorder]="
            (!last &&
              ['lg', 'xl', '2xl', '3xl', '4xl', '5xl'].includes(screenSize)) ||
            (even && ['md'].includes(screenSize))
          ">
        </aplz-ui-stat-card>
      </div>
    </aplz-ui-card>
  </ng-container>
</article>

<section class="px-4 lg:px-10 pb-[30vh]">
  <ng-container *ngIf="orders$ | async as orders">
    <div
      class="grid justify-center gap-y-4 w-full py-6 lg:py-0 lg:flex lg:items-center lg:gap-x-4 lg:flex-wrap">
      <div
        class="mb-8 mx-auto lg:mx-0 lg:text-left w-[125px] flex-shrink-0 flex-grow-0 leading-[0rem]">
        <span
          class="inline-block font-medium text-lg truncate w-full"
          *ngIf="couterLabelTextTemplate$ | async as couterLabelTextTemplate">
          {{ ordersCount | i18nPlural: couterLabelTextTemplate }}
        </span>
      </div>

      <aplz-ui-form-datepicker
        [formControl]="dateControl"
        [maxDate]="todayRawDateDayFirst"
        [rangeEnabled]="true"
        [centerText]="true">
      </aplz-ui-form-datepicker>

      <div
        class="mb-8 lg:mt-8 max-w-full flex"
        [class.flex-grow-[999]]="orders.length > 0">
        <aplz-ui-search
          [textUI]="searchbarInjectedText$ | async"
          [formControl]="searchControl"
          [minLength]="minLength">
        </aplz-ui-search>
      </div>

      <span class="flex-grow-[9999]"></span>
      <button
        aplzButton
        size="md"
        aplzAppearance="solid"
        aplzColor="dark"
        *ngIf="
          newOrderButtonTextTemplate$ | async as newOrderButtonTextTemplate
        "
        (click)="newOrder()"
        class="mb-8 flex-grow-0 flex-shrink-0">
        {{ newOrderButtonTextTemplate.label }}
      </button>

      <aplazo-preloan-cart-button
        class="mb-8"
        *stgCheckGate="'b2b_front_posui_preloan_enabled'">
      </aplazo-preloan-cart-button>

      <button
        aplzButton
        size="md"
        aplzAppearance="solid"
        aplzColor="dark"
        type="button"
        *ngIf="orders.length > 0 && (isActiveSearch$ | async) !== true"
        (click)="downloadReport()"
        class="mb-8 flex-grow-0 flex-shrink-0">
        descargar
      </button>
    </div>

    <ng-container *ngIf="orders.length > 0; else emptyList">
      <div class="-mt-6">
        <aplazo-orders-table
          *ngIf="ordersTableInjectedText$ | async as ordersTableInjectedText"
          [orders]="orders"
          [i18text]="ordersTableInjectedText"></aplazo-orders-table>
      </div>
    </ng-container>

    <ng-template #emptyList>
      <ng-container
        *ngIf="(searchValue$ | async)?.length === 0; else emptySearch">
        <aplz-ui-card appearance="flat">
          <div class="pt-14">
            <aplz-ui-common-message
              *ngIf="
                emptyDateRangeInjectedText$
                  | async as emptyDateRangeInjectedText
              "
              [i18Text]="emptyDateRangeInjectedText">
            </aplz-ui-common-message>
          </div>
        </aplz-ui-card>
      </ng-container>
    </ng-template>
    <ng-template #emptySearch>
      <aplz-ui-card class="mt-6" appearance="flat">
        <div class="pt-14">
          <aplz-ui-common-message
            *ngIf="emptySearchInjectedText$ | async as emptySearchInjectedText"
            [i18Text]="emptySearchInjectedText">
          </aplz-ui-common-message>
        </div>
      </aplz-ui-card>
    </ng-template>
  </ng-container>
</section>
