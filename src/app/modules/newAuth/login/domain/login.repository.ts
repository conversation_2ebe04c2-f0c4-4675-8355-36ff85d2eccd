import { Observable } from 'rxjs';
import { JWTToken } from '../../../../core/domain/access-token';
import { Credentials } from 'src/app/core/domain/credentials.interface';

export abstract class LoginRepository {
  abstract execute(credentials: Credentials): Observable<JWTToken>;
  abstract legacyByCredentials(credentials: Credentials): Observable<JWTToken>;
  abstract getMerchantDetails(token: string): Observable<{ name: string }>;
}
