import { Injectable } from '@angular/core';
import { LoaderService, UseCaseErrorHandler } from '@aplazo/merchant/shared';
import { Observable, catchError, finalize, of, take } from 'rxjs';
import { Order } from '../../../core/domain/order.interface';
import { PosOfflineRepository } from '../../../core/domain/repositories/pos-offline.repository';
import { DateMapper } from '../date.mapper';

@Injectable()
export class GetHistoricalOrdersUseCase {
  constructor(
    private readonly offlineService: PosOfflineRepository,
    private readonly loader: LoaderService,
    private readonly dateMapper: DateMapper,
    private readonly errorHandler: UseCaseErrorHandler
  ) {}

  execute(dateRange: { startDate: Date; endDate: Date }): Observable<Order[]> {
    const idLoader = this.loader.show();

    try {
      const { startDate, endDate } =
        this.dateMapper.fromDateRangeToRawDateYearFistWithHyphen(dateRange);

      return this.offlineService
        .getHistoricalWeekOrders(startDate, endDate)
        .pipe(
          take(1),
          finalize(() => {
            this.loader.hide(idLoader);
          }),
          catchError(error => this.errorHandler.handle<never>(error))
        );
    } catch (error) {
      this.loader.hide(idLoader);
      return this.errorHandler.handle(error, of([]));
    }
  }
}
