import { Inject, Injectable } from '@angular/core';

import { RedirectionService } from '@aplazo/merchant/shared';
import { Observable, map, take } from 'rxjs';
import {
  POS_APP_ROUTES,
  RouteConfig,
} from '../../core/domain/config/app-routes.core';
import { StoreService } from '../application/services/store.service';
import { CanActivate } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class NoLoggedOnlyGuard implements CanActivate {
  constructor(
    private storeService: StoreService,
    private redirectionService: RedirectionService,
    @Inject(POS_APP_ROUTES)
    private appRoutes: RouteConfig
  ) {}

  canActivate(): Observable<boolean> {
    return this.storeService.isLoggedIn$.pipe(
      map(isLoggedIn => {
        if (!isLoggedIn) {
          return true;
        }
        this.redirectionService.internalNavigation([
          this.appRoutes.securedSelectBranch,
        ]);
        return false;
      }),
      take(1)
    );
  }
}
